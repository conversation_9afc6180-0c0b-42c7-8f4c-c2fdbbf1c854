<?php
/**
 * User Controller
 */

class UserController extends BaseController {
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->userModel = new User();
    }
    
    public function index() {
        if (!$this->hasPermission('user', 'index')) {
            $this->show403();
            return;
        }
        
        $page = $this->get('page', 1);
        $users = $this->userModel->getPaginated($page);
        $this->setPagination($users);
        
        $this->data['users'] = $users['items'];
        $this->data['page_title'] = 'รายการผู้ใช้';
        $this->view('users/index');
    }
    
    public function view($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessUser($id)) {
            $this->show403();
            return;
        }
        
        $user = $this->userModel->getById($id);
        if (!$user) {
            $this->show404();
            return;
        }
        
        $this->data['user'] = $user;
        $this->data['page_title'] = 'รายละเอียดผู้ใช้';
        $this->view('users/view');
    }
    
    public function create() {
        if (!$this->hasPermission('user', 'create')) {
            $this->show403();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'เพิ่มผู้ใช้ใหม่';
        $this->view('users/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=user&action=create');
            return;
        }
        
        $data = $this->post();
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->userModel->create($data);
        
        if ($id) {
            showAlert('เพิ่มผู้ใช้เรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=user&action=view&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=user&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessUser($id)) {
            $this->show403();
            return;
        }
        
        $user = $this->userModel->getById($id);
        if (!$user) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $this->data['user'] = $user;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขผู้ใช้';
        $this->view('users/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=user&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        
        // ถ้ามีการเปลี่ยนรหัสผ่าน
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->userModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขผู้ใช้เรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=user&action=view&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=user&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        if (!$this->hasPermission('user', 'delete')) {
            $this->json(['success' => false, 'message' => 'ไม่มีสิทธิ์'], 403);
            return;
        }
        
        // ไม่สามารถลบตัวเอง
        if ($id == $this->user['id']) {
            $this->json(['success' => false, 'message' => 'ไม่สามารถลบตัวเองได้'], 400);
            return;
        }
        
        $success = $this->userModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
    
    public function profile() {
        $user = $this->userModel->getById($this->user['id']);
        
        if ($this->isMethod('POST')) {
            $this->processProfileUpdate();
            return;
        }
        
        $this->data['user'] = $user;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'โปรไฟล์ของฉัน';
        $this->view('users/profile');
    }
    
    private function processProfileUpdate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=user&action=profile');
            return;
        }
        
        $data = $this->post();
        
        // ถ้ามีการเปลี่ยนรหัสผ่าน
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            unset($data['password']);
        }
        
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->userModel->update($this->user['id'], $data);
        
        if ($success) {
            // อัปเดต session
            $updatedUser = $this->userModel->getById($this->user['id']);
            unset($updatedUser['password']);
            $_SESSION['user'] = $updatedUser;
            
            showAlert('อัปเดตโปรไฟล์เรียบร้อยแล้ว', 'success');
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
        }
        
        $this->redirect('index.php?controller=user&action=profile');
    }
}
