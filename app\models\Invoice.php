<?php
/**
 * Invoice Model
 * 
 * โมเดลสำหรับจัดการข้อมูลใบแจ้งหนี้
 */

class Invoice extends BaseModel {
    protected $table = 'invoices';
    protected $primaryKey = 'id';
    protected $fillable = [
        'invoice_number',
        'quotation_id',
        'customer_id',
        'contact_id',
        'issue_date',
        'due_date',
        'subtotal',
        'discount_amount',
        'discount_percentage',
        'tax_amount',
        'tax_percentage',
        'total_amount',
        'paid_amount',
        'balance_amount',
        'status',
        'payment_terms',
        'notes',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลใบแจ้งหนี้ของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return array
     */
    public function getByCustomerId($customerId) {
        return $this->getWhere(['customer_id' => $customerId]);
    }
    
    /**
     * ดึงข้อมูลใบแจ้งหนี้จากใบเสนอราคา
     * 
     * @param int $quotationId ID ของใบเสนอราคา
     * @return array|bool
     */
    public function getByQuotationId($quotationId) {
        return $this->getOneWhere(['quotation_id' => $quotationId]);
    }
    
    /**
     * ดึงข้อมูลใบแจ้งหนี้พร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @return array|bool
     */
    public function getInvoiceWithRelations($invoiceId) {
        $sql = "SELECT i.*, c.company_name, c.address, c.phone as customer_phone, c.email as customer_email,
                       ct.first_name, ct.last_name, ct.email as contact_email, ct.phone as contact_phone,
                       q.quotation_number, q.title as quotation_title,
                       u.first_name as creator_first_name, u.last_name as creator_last_name
                FROM " . TABLE_PREFIX . $this->table . " i
                LEFT JOIN " . TABLE_PREFIX . "customers c ON i.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON i.contact_id = ct.id
                LEFT JOIN " . TABLE_PREFIX . "quotations q ON i.quotation_id = q.id
                LEFT JOIN " . TABLE_PREFIX . "users u ON i.created_by = u.id
                WHERE i.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $invoiceId]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลใบแจ้งหนี้ทั้งหมดพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param array $filters ตัวกรอง
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function getAllInvoicesWithRelations($filters = [], $page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // สร้าง WHERE clause
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['customer_id'])) {
            $whereClause[] = "i.customer_id = :customer_id";
            $params[':customer_id'] = $filters['customer_id'];
        }
        
        if (!empty($filters['status'])) {
            $whereClause[] = "i.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereClause[] = "i.issue_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause[] = "i.issue_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['keyword'])) {
            $whereClause[] = "(i.invoice_number LIKE :keyword OR c.company_name LIKE :keyword)";
            $params[':keyword'] = '%' . $filters['keyword'] . '%';
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total 
                     FROM " . TABLE_PREFIX . $this->table . " i
                     LEFT JOIN " . TABLE_PREFIX . "customers c ON i.customer_id = c.id " . $whereStr;
        
        $this->db->prepare($countSql);
        if (!empty($params)) {
            $this->db->bind($params);
        }
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT i.*, c.company_name, ct.first_name, ct.last_name
                FROM " . TABLE_PREFIX . $this->table . " i
                LEFT JOIN " . TABLE_PREFIX . "customers c ON i.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON i.contact_id = ct.id
                " . $whereStr . "
                ORDER BY i.created_at DESC
                LIMIT :offset, :perPage";
        
        $params[':offset'] = $offset;
        $params[':perPage'] = $perPage;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * สร้างเลขที่ใบแจ้งหนี้ใหม่
     * 
     * @return string
     */
    public function generateInvoiceNumber() {
        $sql = "SELECT invoice_number FROM " . TABLE_PREFIX . $this->table . " 
                WHERE invoice_number LIKE :prefix 
                ORDER BY invoice_number DESC LIMIT 1";
        
        $prefix = INVOICE_PREFIX . date('Ym') . '%';
        
        $this->db->prepare($sql);
        $this->db->bind([':prefix' => $prefix]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        if ($result) {
            $lastNumber = intval(substr($result['invoice_number'], -4));
            return generateDocumentNumber(INVOICE_PREFIX, $lastNumber);
        } else {
            return generateDocumentNumber(INVOICE_PREFIX, 0);
        }
    }
    
    /**
     * อัปเดตสถานะใบแจ้งหนี้
     * 
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @param string $status สถานะใหม่
     * @return bool
     */
    public function updateStatus($invoiceId, $status) {
        return $this->update($invoiceId, ['status' => $status]);
    }
    
    /**
     * อัปเดตยอดชำระเงิน
     * 
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @param float $paidAmount ยอดที่ชำระ
     * @return bool
     */
    public function updatePaidAmount($invoiceId, $paidAmount) {
        $invoice = $this->getById($invoiceId);
        if (!$invoice) {
            return false;
        }
        
        $newPaidAmount = $invoice['paid_amount'] + $paidAmount;
        $balanceAmount = $invoice['total_amount'] - $newPaidAmount;
        
        // กำหนดสถานะใหม่
        $status = INVOICE_STATUS_PENDING;
        if ($balanceAmount <= 0) {
            $status = INVOICE_STATUS_PAID;
        } elseif ($newPaidAmount > 0) {
            $status = INVOICE_STATUS_PARTIAL;
        }
        
        return $this->update($invoiceId, [
            'paid_amount' => $newPaidAmount,
            'balance_amount' => $balanceAmount,
            'status' => $status
        ]);
    }
    
    /**
     * ดึงข้อมูลใบแจ้งหนี้ที่ครบกำหนดชำระ
     * 
     * @param int $days จำนวนวันที่เลยกำหนด
     * @return array
     */
    public function getOverdueInvoices($days = 0) {
        $sql = "SELECT i.*, c.company_name, ct.first_name, ct.last_name, ct.email, ct.phone
                FROM " . TABLE_PREFIX . $this->table . " i
                LEFT JOIN " . TABLE_PREFIX . "customers c ON i.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON i.contact_id = ct.id
                WHERE i.status IN (:pending, :partial) 
                AND i.due_date <= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                ORDER BY i.due_date ASC";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':pending' => INVOICE_STATUS_PENDING,
            ':partial' => INVOICE_STATUS_PARTIAL,
            ':days' => $days
        ]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงสถิติใบแจ้งหนี้
     * 
     * @param array $filters ตัวกรอง
     * @return array
     */
    public function getInvoiceStats($filters = []) {
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereClause[] = "issue_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause[] = "issue_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        $sql = "SELECT 
                    COUNT(*) as total_invoices,
                    SUM(CASE WHEN status = :pending THEN 1 ELSE 0 END) as pending_invoices,
                    SUM(CASE WHEN status = :partial THEN 1 ELSE 0 END) as partial_invoices,
                    SUM(CASE WHEN status = :paid THEN 1 ELSE 0 END) as paid_invoices,
                    SUM(CASE WHEN status = :overdue THEN 1 ELSE 0 END) as overdue_invoices,
                    SUM(total_amount) as total_amount,
                    SUM(paid_amount) as total_paid,
                    SUM(balance_amount) as total_balance
                FROM " . TABLE_PREFIX . $this->table . " " . $whereStr;
        
        $params[':pending'] = INVOICE_STATUS_PENDING;
        $params[':partial'] = INVOICE_STATUS_PARTIAL;
        $params[':paid'] = INVOICE_STATUS_PAID;
        $params[':overdue'] = INVOICE_STATUS_OVERDUE;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetch();
    }
}
