<?php
/**
 * Notification Model
 * 
 * โมเดลสำหรับจัดการข้อมูลการแจ้งเตือน
 */

class Notification extends BaseModel {
    protected $table = 'notifications';
    protected $primaryKey = 'id';
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'message',
        'data',
        'is_read',
        'read_at',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลการแจ้งเตือนของผู้ใช้
     * 
     * @param int $userId ID ของผู้ใช้
     * @param bool $unreadOnly เฉพาะที่ยังไม่ได้อ่าน
     * @param int $limit จำนวนรายการ
     * @return array
     */
    public function getUserNotifications($userId, $unreadOnly = false, $limit = 50) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE user_id = :user_id";
        $params = [':user_id' => $userId];
        
        if ($unreadOnly) {
            $sql .= " AND is_read = 0";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if ($limit > 0) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = $limit;
        }
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลการแจ้งเตือนที่ยังไม่ได้อ่าน
     * 
     * @param int $userId ID ของผู้ใช้
     * @return array
     */
    public function getUnreadNotifications($userId) {
        return $this->getUserNotifications($userId, true);
    }
    
    /**
     * นับจำนวนการแจ้งเตือนที่ยังไม่ได้อ่าน
     * 
     * @param int $userId ID ของผู้ใช้
     * @return int
     */
    public function getUnreadCount($userId) {
        return $this->count([
            'user_id' => $userId,
            'is_read' => 0
        ]);
    }
    
    /**
     * สร้างการแจ้งเตือนใหม่
     * 
     * @param int $userId ID ของผู้ใช้
     * @param string $type ประเภทการแจ้งเตือน
     * @param string $title หัวข้อ
     * @param string $message ข้อความ
     * @param array $data ข้อมูลเพิ่มเติม
     * @return int|bool
     */
    public function createNotification($userId, $type, $title, $message, $data = []) {
        return $this->create([
            'user_id' => $userId,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => json_encode($data),
            'is_read' => 0,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * ทำเครื่องหมายว่าอ่านแล้ว
     * 
     * @param int $notificationId ID ของการแจ้งเตือน
     * @return bool
     */
    public function markAsRead($notificationId) {
        return $this->update($notificationId, [
            'is_read' => 1,
            'read_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * ทำเครื่องหมายว่าอ่านแล้วทั้งหมด
     * 
     * @param int $userId ID ของผู้ใช้
     * @return bool
     */
    public function markAllAsRead($userId) {
        $sql = "UPDATE " . TABLE_PREFIX . $this->table . " 
                SET is_read = 1, read_at = :read_at 
                WHERE user_id = :user_id AND is_read = 0";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':read_at' => date('Y-m-d H:i:s'),
            ':user_id' => $userId
        ]);
        
        return $this->db->execute();
    }
    
    /**
     * ลบการแจ้งเตือนเก่า
     * 
     * @param int $days จำนวนวันที่เก่ากว่า
     * @return bool
     */
    public function deleteOldNotifications($days = 30) {
        $sql = "DELETE FROM " . TABLE_PREFIX . $this->table . " 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        
        $this->db->prepare($sql);
        $this->db->bind([':days' => $days]);
        
        return $this->db->execute();
    }
    
    /**
     * สร้างการแจ้งเตือนสำหรับโอกาสการขาย
     * 
     * @param int $userId ID ของผู้ใช้
     * @param int $opportunityId ID ของโอกาสการขาย
     * @param string $action การกระทำ
     * @param string $opportunityTitle ชื่อโอกาสการขาย
     * @return int|bool
     */
    public function createOpportunityNotification($userId, $opportunityId, $action, $opportunityTitle) {
        $messages = [
            'created' => 'โอกาสการขายใหม่ถูกสร้างขึ้น',
            'updated' => 'โอกาสการขายถูกอัปเดต',
            'stage_changed' => 'ขั้นตอนของโอกาสการขายเปลี่ยนแปลง',
            'won' => 'โอกาสการขายสำเร็จ',
            'lost' => 'โอกาสการขายไม่สำเร็จ'
        ];
        
        $message = $messages[$action] ?? 'โอกาสการขายมีการเปลี่ยนแปลง';
        
        return $this->createNotification(
            $userId,
            NOTIFICATION_TYPE_OPPORTUNITY,
            $message,
            "โอกาสการขาย: {$opportunityTitle}",
            ['opportunity_id' => $opportunityId, 'action' => $action]
        );
    }
    
    /**
     * สร้างการแจ้งเตือนสำหรับใบเสนอราคา
     * 
     * @param int $userId ID ของผู้ใช้
     * @param int $quotationId ID ของใบเสนอราคา
     * @param string $action การกระทำ
     * @param string $quotationNumber เลขที่ใบเสนอราคา
     * @return int|bool
     */
    public function createQuotationNotification($userId, $quotationId, $action, $quotationNumber) {
        $messages = [
            'created' => 'ใบเสนอราคาใหม่ถูกสร้างขึ้น',
            'sent' => 'ใบเสนอราคาถูกส่งแล้ว',
            'accepted' => 'ใบเสนอราคาได้รับการยอมรับ',
            'rejected' => 'ใบเสนอราคาถูกปฏิเสธ',
            'expired' => 'ใบเสนอราคาหมดอายุ'
        ];
        
        $message = $messages[$action] ?? 'ใบเสนอราคามีการเปลี่ยนแปลง';
        
        return $this->createNotification(
            $userId,
            NOTIFICATION_TYPE_QUOTATION,
            $message,
            "ใบเสนอราคา: {$quotationNumber}",
            ['quotation_id' => $quotationId, 'action' => $action]
        );
    }
    
    /**
     * สร้างการแจ้งเตือนสำหรับใบแจ้งหนี้
     * 
     * @param int $userId ID ของผู้ใช้
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @param string $action การกระทำ
     * @param string $invoiceNumber เลขที่ใบแจ้งหนี้
     * @return int|bool
     */
    public function createInvoiceNotification($userId, $invoiceId, $action, $invoiceNumber) {
        $messages = [
            'created' => 'ใบแจ้งหนี้ใหม่ถูกสร้างขึ้น',
            'paid' => 'ใบแจ้งหนี้ได้รับการชำระเงินแล้ว',
            'partial_paid' => 'ใบแจ้งหนี้ได้รับการชำระเงินบางส่วน',
            'overdue' => 'ใบแจ้งหนี้เลยกำหนดชำระ'
        ];
        
        $message = $messages[$action] ?? 'ใบแจ้งหนี้มีการเปลี่ยนแปลง';
        
        return $this->createNotification(
            $userId,
            NOTIFICATION_TYPE_INVOICE,
            $message,
            "ใบแจ้งหนี้: {$invoiceNumber}",
            ['invoice_id' => $invoiceId, 'action' => $action]
        );
    }
    
    /**
     * สร้างการแจ้งเตือนสำหรับกิจกรรม
     * 
     * @param int $userId ID ของผู้ใช้
     * @param int $activityId ID ของกิจกรรม
     * @param string $action การกระทำ
     * @param string $activitySubject หัวข้อกิจกรรม
     * @return int|bool
     */
    public function createActivityNotification($userId, $activityId, $action, $activitySubject) {
        $messages = [
            'assigned' => 'กิจกรรมใหม่ถูกมอบหมายให้คุณ',
            'reminder' => 'เตือนกิจกรรมที่กำลังจะมาถึง',
            'overdue' => 'กิจกรรมเลยกำหนดแล้ว'
        ];
        
        $message = $messages[$action] ?? 'กิจกรรมมีการเปลี่ยนแปลง';
        
        return $this->createNotification(
            $userId,
            NOTIFICATION_TYPE_ACTIVITY,
            $message,
            "กิจกรรม: {$activitySubject}",
            ['activity_id' => $activityId, 'action' => $action]
        );
    }
    
    /**
     * ดึงข้อมูลการแจ้งเตือนตามประเภท
     * 
     * @param int $userId ID ของผู้ใช้
     * @param string $type ประเภทการแจ้งเตือน
     * @param int $limit จำนวนรายการ
     * @return array
     */
    public function getNotificationsByType($userId, $type, $limit = 20) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " 
                WHERE user_id = :user_id AND type = :type
                ORDER BY created_at DESC
                LIMIT :limit";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':user_id' => $userId,
            ':type' => $type,
            ':limit' => $limit
        ]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
}
