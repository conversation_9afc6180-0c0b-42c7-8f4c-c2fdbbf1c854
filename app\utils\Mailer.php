<?php
/**
 * Mailer Utility
 * 
 * คลาสสำหรับจัดการการส่งอีเมล
 */

class Mailer {
    private static $instance = null;
    private $config = [];
    
    /**
     * คอนสตรักเตอร์
     */
    private function __construct() {
        $this->loadConfig();
    }
    
    /**
     * ดึง instance ของ Mailer (Singleton)
     * 
     * @return Mailer
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * โหลดการตั้งค่าอีเมล
     */
    private function loadConfig() {
        $settingModel = new Setting();
        
        $this->config = [
            'smtp_host' => $settingModel->getValue('smtp_host', 'localhost'),
            'smtp_port' => $settingModel->getValue('smtp_port', 587),
            'smtp_username' => $settingModel->getValue('smtp_username', ''),
            'smtp_password' => $settingModel->getValue('smtp_password', ''),
            'smtp_encryption' => $settingModel->getValue('smtp_encryption', 'tls'),
            'from_email' => $settingModel->getValue('from_email', '<EMAIL>'),
            'from_name' => $settingModel->getValue('from_name', 'Sales Tracking System'),
            'reply_to' => $settingModel->getValue('reply_to_email', ''),
            'enabled' => $settingModel->getValue('email_enabled', false)
        ];
    }
    
    /**
     * ส่งอีเมล
     * 
     * @param string|array $to ผู้รับ
     * @param string $subject หัวข้อ
     * @param string $body เนื้อหา
     * @param array $options ตัวเลือกเพิ่มเติม
     * @return bool
     */
    public static function send($to, $subject, $body, $options = []) {
        $instance = self::getInstance();
        
        if (!$instance->config['enabled']) {
            Logger::info('Email sending disabled', ['to' => $to, 'subject' => $subject]);
            return false;
        }
        
        try {
            // ในที่นี้จะใช้ PHP mail() function พื้นฐาน
            // สำหรับการใช้งานจริงควรใช้ PHPMailer หรือ SwiftMailer
            
            $headers = $instance->buildHeaders($options);
            $formattedBody = $instance->formatBody($body, $options);
            
            // แปลง array ของผู้รับเป็น string
            if (is_array($to)) {
                $to = implode(', ', $to);
            }
            
            $result = mail($to, $subject, $formattedBody, $headers);
            
            if ($result) {
                Logger::info('Email sent successfully', [
                    'to' => $to,
                    'subject' => $subject
                ]);
            } else {
                Logger::error('Failed to send email', [
                    'to' => $to,
                    'subject' => $subject
                ]);
            }
            
            return $result;
            
        } catch (Exception $e) {
            Logger::error('Email sending error', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * สร้าง headers สำหรับอีเมล
     * 
     * @param array $options ตัวเลือก
     * @return string
     */
    private function buildHeaders($options = []) {
        $headers = [];
        
        // From header
        $fromName = $options['from_name'] ?? $this->config['from_name'];
        $fromEmail = $options['from_email'] ?? $this->config['from_email'];
        $headers[] = "From: {$fromName} <{$fromEmail}>";
        
        // Reply-To header
        $replyTo = $options['reply_to'] ?? $this->config['reply_to'];
        if (!empty($replyTo)) {
            $headers[] = "Reply-To: {$replyTo}";
        }
        
        // Content type
        $isHtml = $options['is_html'] ?? true;
        if ($isHtml) {
            $headers[] = "Content-Type: text/html; charset=UTF-8";
        } else {
            $headers[] = "Content-Type: text/plain; charset=UTF-8";
        }
        
        // Additional headers
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "X-Mailer: Sales Tracking System";
        
        // CC
        if (!empty($options['cc'])) {
            $cc = is_array($options['cc']) ? implode(', ', $options['cc']) : $options['cc'];
            $headers[] = "Cc: {$cc}";
        }
        
        // BCC
        if (!empty($options['bcc'])) {
            $bcc = is_array($options['bcc']) ? implode(', ', $options['bcc']) : $options['bcc'];
            $headers[] = "Bcc: {$bcc}";
        }
        
        return implode("\r\n", $headers);
    }
    
    /**
     * จัดรูปแบบเนื้อหาอีเมล
     * 
     * @param string $body เนื้อหา
     * @param array $options ตัวเลือก
     * @return string
     */
    private function formatBody($body, $options = []) {
        $isHtml = $options['is_html'] ?? true;
        
        if ($isHtml && !$this->isHtml($body)) {
            // แปลง plain text เป็น HTML
            $body = nl2br(htmlspecialchars($body));
            $body = $this->wrapInHtmlTemplate($body, $options);
        }
        
        return $body;
    }
    
    /**
     * ตรวจสอบว่าเป็น HTML หรือไม่
     * 
     * @param string $content เนื้อหา
     * @return bool
     */
    private function isHtml($content) {
        return $content !== strip_tags($content);
    }
    
    /**
     * ห่อเนื้อหาด้วย HTML template
     * 
     * @param string $content เนื้อหา
     * @param array $options ตัวเลือก
     * @return string
     */
    private function wrapInHtmlTemplate($content, $options = []) {
        $title = $options['title'] ?? 'Sales Tracking System';
        $companyName = $this->config['from_name'];
        
        return "
        <!DOCTYPE html>
        <html lang='th'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$title}</title>
            <style>
                body { font-family: 'Sarabun', Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>{$companyName}</h1>
                </div>
                <div class='content'>
                    {$content}
                </div>
                <div class='footer'>
                    <p>อีเมลนี้ถูกส่งโดยอัตโนมัติจากระบบติดตามงานขาย</p>
                    <p>กรุณาอย่าตอบกลับอีเมลนี้</p>
                </div>
            </div>
        </body>
        </html>";
    }
    
    /**
     * ส่งอีเมลยืนยันการสมัครสมาชิก
     * 
     * @param string $email อีเมลผู้ใช้
     * @param string $name ชื่อผู้ใช้
     * @param string $verificationLink ลิงก์ยืนยัน
     * @return bool
     */
    public static function sendVerificationEmail($email, $name, $verificationLink) {
        $subject = 'ยืนยันการสมัครสมาชิก - Sales Tracking System';
        
        $body = "
        <h2>ยินดีต้อนรับ, {$name}!</h2>
        <p>ขอบคุณที่สมัครสมาชิกกับระบบติดตามงานขายของเรา</p>
        <p>กรุณาคลิกลิงก์ด้านล่างเพื่อยืนยันอีเมลของคุณ:</p>
        <p><a href='{$verificationLink}' class='button'>ยืนยันอีเมล</a></p>
        <p>หรือคัดลอกลิงก์นี้ไปวางในเบราว์เซอร์: {$verificationLink}</p>
        <p>ลิงก์นี้จะหมดอายุใน 24 ชั่วโมง</p>
        ";
        
        return self::send($email, $subject, $body, ['title' => 'ยืนยันการสมัครสมาชิก']);
    }
    
    /**
     * ส่งอีเมลรีเซ็ตรหัสผ่าน
     * 
     * @param string $email อีเมลผู้ใช้
     * @param string $name ชื่อผู้ใช้
     * @param string $resetLink ลิงก์รีเซ็ต
     * @return bool
     */
    public static function sendPasswordResetEmail($email, $name, $resetLink) {
        $subject = 'รีเซ็ตรหัสผ่าน - Sales Tracking System';
        
        $body = "
        <h2>สวัสดี, {$name}</h2>
        <p>เราได้รับคำขอให้รีเซ็ตรหัสผ่านสำหรับบัญชีของคุณ</p>
        <p>กรุณาคลิกลิงก์ด้านล่างเพื่อรีเซ็ตรหัสผ่าน:</p>
        <p><a href='{$resetLink}' class='button'>รีเซ็ตรหัสผ่าน</a></p>
        <p>หรือคัดลอกลิงก์นี้ไปวางในเบราว์เซอร์: {$resetLink}</p>
        <p>ลิงก์นี้จะหมดอายุใน 1 ชั่วโมง</p>
        <p>หากคุณไม่ได้ขอรีเซ็ตรหัสผ่าน กรุณาเพิกเฉยต่ออีเมลนี้</p>
        ";
        
        return self::send($email, $subject, $body, ['title' => 'รีเซ็ตรหัสผ่าน']);
    }
    
    /**
     * ส่งอีเมลแจ้งเตือนใบเสนอราคา
     * 
     * @param string $email อีเมลลูกค้า
     * @param string $customerName ชื่อลูกค้า
     * @param array $quotationData ข้อมูลใบเสนอราคา
     * @return bool
     */
    public static function sendQuotationEmail($email, $customerName, $quotationData) {
        $subject = "ใบเสนอราคา #{$quotationData['quotation_number']}";
        
        $body = "
        <h2>เรียน คุณ{$customerName}</h2>
        <p>ขอส่งใบเสนอราคาสำหรับการพิจารณา</p>
        
        <h3>รายละเอียดใบเสนอราคา</h3>
        <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
            <tr style='background: #f5f5f5;'>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>เลขที่ใบเสนอราคา:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>{$quotationData['quotation_number']}</td>
            </tr>
            <tr>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>วันที่:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>{$quotationData['quotation_date']}</td>
            </tr>
            <tr style='background: #f5f5f5;'>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>ยอดรวม:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>" . number_format($quotationData['total_amount'], 2) . " บาท</td>
            </tr>
            <tr>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>ใบเสนอราคามีผลถึง:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>{$quotationData['valid_until']}</td>
            </tr>
        </table>
        
        <p>หากมีข้อสงสัยประการใด กรุณาติดต่อกลับมาได้ทุกเมื่อ</p>
        <p>ขอบคุณที่ให้ความสนใจในผลิตภัณฑ์และบริการของเรา</p>
        ";
        
        return self::send($email, $subject, $body, ['title' => 'ใบเสนอราคา']);
    }
    
    /**
     * ส่งอีเมลแจ้งเตือนใบแจ้งหนี้
     * 
     * @param string $email อีเมลลูกค้า
     * @param string $customerName ชื่อลูกค้า
     * @param array $invoiceData ข้อมูลใบแจ้งหนี้
     * @return bool
     */
    public static function sendInvoiceEmail($email, $customerName, $invoiceData) {
        $subject = "ใบแจ้งหนี้ #{$invoiceData['invoice_number']}";
        
        $body = "
        <h2>เรียน คุณ{$customerName}</h2>
        <p>ขอส่งใบแจ้งหนี้สำหรับการชำระเงิน</p>
        
        <h3>รายละเอียดใบแจ้งหนี้</h3>
        <table style='width: 100%; border-collapse: collapse; margin: 20px 0;'>
            <tr style='background: #f5f5f5;'>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>เลขที่ใบแจ้งหนี้:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>{$invoiceData['invoice_number']}</td>
            </tr>
            <tr>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>วันที่:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>{$invoiceData['invoice_date']}</td>
            </tr>
            <tr style='background: #f5f5f5;'>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>ยอดรวม:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>" . number_format($invoiceData['total_amount'], 2) . " บาท</td>
            </tr>
            <tr>
                <td style='padding: 10px; border: 1px solid #ddd;'><strong>กำหนดชำระ:</strong></td>
                <td style='padding: 10px; border: 1px solid #ddd;'>{$invoiceData['due_date']}</td>
            </tr>
        </table>
        
        <p>กรุณาชำระเงินภายในกำหนด เพื่อหลีกเลี่ยงค่าปรับล่าช้า</p>
        <p>ขอบคุณสำหรับการใช้บริการ</p>
        ";
        
        return self::send($email, $subject, $body, ['title' => 'ใบแจ้งหนี้']);
    }
    
    /**
     * ทดสอบการส่งอีเมล
     * 
     * @param string $testEmail อีเมลทดสอบ
     * @return bool
     */
    public static function testEmail($testEmail) {
        $subject = 'ทดสอบการส่งอีเมล - Sales Tracking System';
        $body = "
        <h2>ทดสอบการส่งอีเมล</h2>
        <p>หากคุณได้รับอีเมลนี้ แสดงว่าระบบส่งอีเมลทำงานได้ปกติ</p>
        <p>วันที่ทดสอบ: " . date('Y-m-d H:i:s') . "</p>
        ";
        
        return self::send($testEmail, $subject, $body, ['title' => 'ทดสอบอีเมล']);
    }
}
