<?php
/**
 * Create Quotation Page
 * 
 * หน้าสำหรับสร้างใบเสนอราคาใหม่ในระบบติดตามงานขายและการเสนอราคา
 */

// เริ่มต้น Session
session_start();

// ตรวจสอบว่าผู้ใช้ล็อกอินแล้วหรือไม่
if (!isset($_SESSION['user_id'])) {
    // ถ้ายังไม่ได้ล็อกอินให้ redirect ไปที่หน้าล็อกอิน
    header('Location: ../auth/login.php');
    exit;
}

// โหลดไฟล์การตั้งค่า
require_once '../app/config/config.php';
require_once '../app/config/database.php';
require_once '../app/config/functions.php';

// โหลดโมเดลที่จำเป็น
require_once '../app/models/User.php';
require_once '../app/models/Customer.php';
require_once '../app/models/Product.php';
require_once '../app/models/Quotation.php';
require_once '../app/models/QuotationItem.php';

// สร้างอินสแตนซ์ของโมเดล
$userModel = new User($conn);
$customerModel = new Customer($conn);
$productModel = new Product($conn);
$quotationModel = new Quotation($conn);
$quotationItemModel = new QuotationItem($conn);

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$currentUser = $userModel->getUserById($_SESSION['user_id']);

// ดึงข้อมูลลูกค้าทั้งหมด
$customers = $customerModel->getAllCustomers();

// ดึงข้อมูลสินค้าทั้งหมด
$products = $productModel->getAllProducts();

// กำหนดค่าเริ่มต้นสำหรับใบเสนอราคา
$quotation = [
    'customer_id' => isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0,
    'opportunity_id' => isset($_GET['opportunity_id']) ? (int)$_GET['opportunity_id'] : 0,
    'quotation_no' => $quotationModel->generateQuotationNumber(),
    'quotation_date' => date('Y-m-d'),
    'expiry_date' => date('Y-m-d', strtotime('+30 days')),
    'reference' => '',
    'discount_type' => 'percentage',
    'discount_value' => 0,
    'tax_rate' => 7,
    'notes' => '',
    'terms' => 'การชำระเงิน: ชำระเงินภายใน 30 วันนับจากวันที่ออกใบเสนอราคา
ระยะเวลาการเสนอราคา: ใบเสนอราคานี้มีผลบังคับใช้ 30 วันนับจากวันที่ออกใบเสนอราคา
การรับประกัน: สินค้าทั้งหมดมีการรับประกัน 1 ปี',
    'status' => 'draft'
];

// ถ้ามีการส่งฟอร์มมา
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // ตรวจสอบ CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $_SESSION['error'] = 'การยืนยันความปลอดภัยล้มเหลว กรุณาลองใหม่อีกครั้ง';
        header('Location: create.php');
        exit;
    }
    
    // รับข้อมูลจากฟอร์ม
    $quotation = [
        'customer_id' => (int)$_POST['customer_id'],
        'opportunity_id' => isset($_POST['opportunity_id']) ? (int)$_POST['opportunity_id'] : 0,
        'quotation_no' => $_POST['quotation_no'],
        'quotation_date' => $_POST['quotation_date'],
        'expiry_date' => $_POST['expiry_date'],
        'reference' => $_POST['reference'],
        'discount_type' => $_POST['discount_type'],
        'discount_value' => (float)$_POST['discount_value'],
        'tax_rate' => (float)$_POST['tax_rate'],
        'notes' => $_POST['notes'],
        'terms' => $_POST['terms'],
        'status' => $_POST['status']
    ];
    
    // ตรวจสอบความถูกต้องของข้อมูล
    $errors = [];
    
    if (empty($quotation['customer_id'])) {
        $errors['customer_id'] = 'กรุณาเลือกลูกค้า';
    }
    
    if (empty($quotation['quotation_no'])) {
        $errors['quotation_no'] = 'กรุณาระบุเลขที่ใบเสนอราคา';
    } elseif ($quotationModel->isQuotationNumberExists($quotation['quotation_no'])) {
        $errors['quotation_no'] = 'เลขที่ใบเสนอราคานี้มีอยู่ในระบบแล้ว';
    }
    
    if (empty($quotation['quotation_date'])) {
        $errors['quotation_date'] = 'กรุณาระบุวันที่ใบเสนอราคา';
    }
    
    if (empty($quotation['expiry_date'])) {
        $errors['expiry_date'] = 'กรุณาระบุวันที่หมดอายุ';
    }
    
    // ตรวจสอบรายการสินค้า
    $items = [];
    $hasItems = false;
    
    if (isset($_POST['product_id']) && is_array($_POST['product_id'])) {
        foreach ($_POST['product_id'] as $key => $product_id) {
            if (!empty($product_id)) {
                $hasItems = true;
                $items[] = [
                    'product_id' => (int)$product_id,
                    'description' => $_POST['description'][$key],
                    'quantity' => (float)$_POST['quantity'][$key],
                    'unit_price' => (float)$_POST['unit_price'][$key],
                    'discount' => (float)$_POST['item_discount'][$key],
                    'tax' => isset($_POST['item_tax'][$key]) ? 1 : 0
                ];
            }
        }
    }
    
    if (!$hasItems) {
        $errors['items'] = 'กรุณาเพิ่มอย่างน้อยหนึ่งรายการสินค้า';
    }
    
    // ถ้าไม่มีข้อผิดพลาด ให้บันทึกข้อมูล
    if (empty($errors)) {
        // เพิ่มข้อมูลผู้สร้าง
        $quotation['created_by'] = $_SESSION['user_id'];
        
        // บันทึกใบเสนอราคา
        $quotationId = $quotationModel->createQuotation($quotation);
        
        if ($quotationId) {
            // บันทึกรายการสินค้า
            foreach ($items as $item) {
                $item['quotation_id'] = $quotationId;
                $quotationItemModel->createQuotationItem($item);
            }
            
            // บันทึกประวัติการเปลี่ยนแปลงสถานะ
            $quotationModel->addStatusHistory($quotationId, $quotation['status'], 'สร้างใบเสนอราคาใหม่', $_SESSION['user_id']);
            
            // แสดงข้อความสำเร็จ
            $_SESSION['success'] = 'สร้างใบเสนอราคาเรียบร้อยแล้ว';
            
            // ถ้าสถานะเป็น "sent" ให้ redirect ไปที่หน้าส่งอีเมล
            if ($quotation['status'] === 'sent') {
                header('Location: email.php?id=' . $quotationId);
                exit;
            } else {
                // ถ้าไม่ใช่ ให้ redirect ไปที่หน้าดูรายละเอียด
                header('Location: view.php?id=' . $quotationId);
                exit;
            }
        } else {
            $_SESSION['error'] = 'เกิดข้อผิดพลาดในการบันทึกข้อมูล กรุณาลองใหม่อีกครั้ง';
        }
    }
}

// สร้าง CSRF token
$_SESSION['csrf_token'] = bin2hex(random_bytes(32));

// กำหนดหัวข้อหน้า
$pageTitle = 'สร้างใบเสนอราคาใหม่';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ระบบติดตามงานขายและการเสนอราคา</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Flatpickr (Date Picker) -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        .select2-container--bootstrap-5 .select2-selection {
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            border: 1px solid #dee2e6;
        }
        .table-items th, .table-items td {
            vertical-align: middle;
        }
        .remove-item {
            cursor: pointer;
        }
        .product-description {
            min-height: 60px;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>ระบบติดตามงานขาย</h3>
            </div>
            
            <ul class="list-unstyled components">
                <li>
                    <a href="../dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> แดชบอร์ด
                    </a>
                </li>
                <li>
                    <a href="#salesSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-chart-line me-2"></i> งานขาย
                    </a>
                    <ul class="collapse list-unstyled" id="salesSubmenu">
                        <li>
                            <a href="../sales/targets.php">เป้าหมายการขาย</a>
                        </li>
                        <li>
                            <a href="../sales/opportunities.php">โอกาสการขาย</a>
                        </li>
                        <li>
                            <a href="../sales/pipeline.php">Pipeline การขาย</a>
                        </li>
                        <li>
                            <a href="../sales/forecasts.php">การคาดการณ์ยอดขาย</a>
                        </li>
                    </ul>
                </li>
                <li class="active">
                    <a href="#quotationSubmenu" data-bs-toggle="collapse" aria-expanded="true" class="dropdown-toggle">
                        <i class="fas fa-file-invoice me-2"></i> ใบเสนอราคา
                    </a>
                    <ul class="collapse show list-unstyled" id="quotationSubmenu">
                        <li class="active">
                            <a href="create.php">สร้างใบเสนอราคา</a>
                        </li>
                        <li>
                            <a href="list.php">รายการใบเสนอราคา</a>
                        </li>
                        <li>
                            <a href="templates.php">เทมเพลตใบเสนอราคา</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#customerSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-users me-2"></i> ลูกค้า
                    </a>
                    <ul class="collapse list-unstyled" id="customerSubmenu">
                        <li>
                            <a href="../customers/list.php">รายชื่อลูกค้า</a>
                        </li>
                        <li>
                            <a href="../customers/create.php">เพิ่มลูกค้าใหม่</a>
                        </li>
                        <li>
                            <a href="../customers/groups.php">กลุ่มลูกค้า</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#productSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-box me-2"></i> สินค้าและบริการ
                    </a>
                    <ul class="collapse list-unstyled" id="productSubmenu">
                        <li>
                            <a href="../products/list.php">รายการสินค้า</a>
                        </li>
                        <li>
                            <a href="../products/create.php">เพิ่มสินค้าใหม่</a>
                        </li>
                        <li>
                            <a href="../products/categories.php">หมวดหมู่สินค้า</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#paymentSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-money-bill-wave me-2"></i> การชำระเงิน
                    </a>
                    <ul class="collapse list-unstyled" id="paymentSubmenu">
                        <li>
                            <a href="../payments/list.php">รายการชำระเงิน</a>
                        </li>
                        <li>
                            <a href="../payments/create.php">บันทึกการชำระเงิน</a>
                        </li>
                        <li>
                            <a href="../payments/invoices.php">ใบแจ้งหนี้</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#reportSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-chart-bar me-2"></i> รายงาน
                    </a>
                    <ul class="collapse list-unstyled" id="reportSubmenu">
                        <li>
                            <a href="../reports/sales.php">รายงานยอดขาย</a>
                        </li>
                        <li>
                            <a href="../reports/performance.php">รายงานประสิทธิภาพ</a>
                        </li>
                        <li>
                            <a href="../reports/customers.php">รายงานลูกค้า</a>
                        </li>
                        <li>
                            <a href="../reports/products.php">รายงานสินค้า</a>
                        </li>
                    </ul>
                </li>
                <?php if ($_SESSION['role'] === 'admin'): ?>
                <li>
                    <a href="#settingSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-cog me-2"></i> ตั้งค่า
                    </a>
                    <ul class="collapse list-unstyled" id="settingSubmenu">
                        <li>
                            <a href="../settings/users.php">ผู้ใช้งาน</a>
                        </li>
                        <li>
                            <a href="../settings/roles.php">สิทธิ์การใช้งาน</a>
                        </li>
                        <li>
                            <a href="../settings/company.php">ข้อมูลบริษัท</a>
                        </li>
                        <li>
                            <a href="../settings/system.php">ตั้งค่าระบบ</a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
            
            <div class="sidebar-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-white-50">เข้าสู่ระบบล่าสุด:</small><br>
                        <small class="text-white-50"><?php echo date('d/m/Y H:i', strtotime($currentUser['last_login'])); ?></small>
                    </div>
                    <a href="../auth/logout.php" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white">
                <div class="container-fluid">
                    <button type="button" id="sidebar-toggle" class="btn btn-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="ms-auto d-flex align-items-center">
                        <!-- Notifications -->
                        <div class="dropdown me-3">
                            <a class="btn btn-light position-relative" href="#" role="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
                                <div class="notification-header">
                                    <h6 class="notification-title">การแจ้งเตือน</h6>
                                </div>
                                <div class="notification-list">
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">มีใบเสนอราคาใหม่ #QT-2023-0042</div>
                                        <div class="notification-item-time">เมื่อ 5 นาทีที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">ลูกค้ายืนยันการชำระเงิน #INV-2023-0036</div>
                                        <div class="notification-item-time">เมื่อ 2 ชั่วโมงที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">มีโอกาสการขายใหม่จาก บริษัท ABC จำกัด</div>
                                        <div class="notification-item-time">เมื่อ 3 ชั่วโมงที่แล้ว</div>
                                    </a>
                                </div>
                                <div class="notification-footer">
                                    <a href="../notifications.php" class="text-primary">ดูการแจ้งเตือนทั้งหมด</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Profile -->
                        <div class="dropdown">
                            <a class="btn btn-light dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="<?php echo !empty($currentUser['profile_image']) ? $currentUser['profile_image'] : '../assets/images/default-avatar.png'; ?>" alt="Profile" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php echo $currentUser['name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="../profile.php"><i class="fas fa-user me-2"></i> โปรไฟล์</a></li>
                                <li><a class="dropdown-item" href="../settings/preferences.php"><i class="fas fa-cog me-2"></i> ตั้งค่า</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i> ออกจากระบบ</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Page Content -->
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">สร้างใบเสนอราคาใหม่</h1>
                    <div>
                        <a href="list.php" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> กลับไปยังรายการ
                        </a>
                    </div>
                </div>
                
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php 
                        echo $_SESSION['error']; 
                        unset($_SESSION['error']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php 
                        echo $_SESSION['success']; 
                        unset($_SESSION['success']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" id="quotationForm">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-8">
                            <!-- Quotation Details -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-white py-3">
                                    <h5 class="card-title mb-0">รายละเอียดใบเสนอราคา</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="customer_id" class="form-label">ลูกค้า <span class="text-danger">*</span></label>
                                            <select class="form-select select2" id="customer_id" name="customer_id" required>
                                                <option value="">-- เลือกลูกค้า --</option>
                                                <?php foreach ($customers as $customer): ?>
                                                    <option value="<?php echo $customer['id']; ?>" <?php echo $quotation['customer_id'] == $customer['id'] ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($customer['name']); ?>
                                                        <?php if (!empty($customer['company'])): ?>
                                                            (<?php echo htmlspecialchars($customer['company']); ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <?php if (isset($errors['customer_id'])): ?>
                                                <div class="text-danger mt-1"><?php echo $errors['customer_id']; ?></div>
                                            <?php endif; ?>
                                            <div class="mt-2">
                                                <a href="../customers/create.php" target="_blank" class="text-primary">
                                                    <i class="fas fa-plus-circle me-1"></i> เพิ่มลูกค้าใหม่
                                                </a>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label for="quotation_no" class="form-label">เลขที่ใบเสนอราคา <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="quotation_no" name="quotation_no" value="<?php echo htmlspecialchars($quotation['quotation_no']); ?>" required>
                                            <?php if (isset($errors['quotation_no'])): ?>
                                                <div class="text-danger mt-1"><?php echo $errors['quotation_no']; ?></div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label for="quotation_date" class="form-label">วันที่ใบเสนอราคา <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control datepicker" id="quotation_date" name="quotation_date" value="<?php echo htmlspecialchars($quotation['quotation_date']); ?>" required>
                                            <?php if (isset($errors['quotation_date'])): ?>
                                                <div class="text-danger mt-1"><?php echo $errors['quotation_date']; ?></div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label for="expiry_date" class="form-label">วันที่หมดอายุ <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control datepicker" id="expiry_date" name="expiry_date" value="<?php echo htmlspecialchars($quotation['expiry_date']); ?>" required>
                                            <?php if (isset($errors['expiry_date'])): ?>
                                                <div class="text-danger mt-1"><?php echo $errors['expiry_date']; ?></div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label for="reference" class="form-label">อ้างอิง</label>
                                            <input type="text" class="form-control" id="reference" name="reference" value="<?php echo htmlspecialchars($quotation['reference']); ?>" placeholder="เช่น เลขที่ใบสั่งซื้อ หรือเลขที่โครงการ">
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <label for="opportunity_id" class="form-label">โอกาสการขาย</label>
                                            <select class="form-select select2" id="opportunity_id" name="opportunity_id">
                                                <option value="">-- ไม่เชื่อมโยงกับโอกาสการขาย --</option>
                                                <!-- ตรงนี้จะมีการดึงข้อมูลโอกาสการขายมาแสดง -->
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Items -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-white py-3">
                                    <h5 class="card-title mb-0">รายการสินค้า/บริการ</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (isset($errors['items'])): ?>
                                        <div class="alert alert-danger">
                                            <?php echo $errors['items']; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-items">
                                            <thead>
                                                <tr>
                                                    <th style="width: 30%;">สินค้า/บริการ</th>
                                                    <th>รายละเอียด</th>
                                                    <th style="width: 10%;">จำนวน</th>
                                                    <th style="width: 15%;">ราคาต่อหน่วย</th>
                                                    <th style="width: 10%;">ส่วนลด</th>
                                                    <th style="width: 5%;">ภาษี</th>
                                                    <th style="width: 15%;">รวม</th>
                                                    <th style="width: 5%;"></th>
                                                </tr>
                                            </thead>
                                            <tbody id="itemsContainer">
                                                <!-- รายการสินค้าจะถูกเพิ่มที่นี่ด้วย JavaScript -->
                                                <tr class="no-items">
                                                    <td colspan="8" class="text-center py-4">
                                                        <div class="text-muted">ยังไม่มีรายการสินค้า/บริการ</div>
                                                        <button type="button" class="btn btn-outline-primary mt-2" id="addFirstItem">
                                                            <i class="fas fa-plus me-1"></i> เพิ่มรายการแรก
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="8">
                                                        <button type="button" class="btn btn-outline-primary btn-sm" id="addItem">
                                                            <i class="fas fa-plus me-1"></i> เพิ่มรายการ
                                                        </button>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Notes and Terms -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-white py-3">
                                    <h5 class="card-title mb-0">หมายเหตุและเงื่อนไข</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-12">
                                            <label for="notes" class="form-label">หมายเหตุ</label>
                                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="หมายเหตุหรือข้อความเพิ่มเติมสำหรับลูกค้า"><?php echo htmlspecialchars($quotation['notes']); ?></textarea>
                                        </div>
                                        
                                        <div class="col-md-12">
                                            <label for="terms" class="form-label">เงื่อนไขและข้อตกลง</label>
                                            <textarea class="form-control" id="terms" name="terms" rows="5"><?php echo htmlspecialchars($quotation['terms']); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Right Column -->
                        <div class="col-md-4">
                            <!-- Summary -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-white py-3">
                                    <h5 class="card-title mb-0">สรุปยอด</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>ยอดรวม:</span>
                                        <span id="subtotal">0.00</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>ส่วนลด:</span>
                                            <div class="input-group" style="width: 60%;">
                                                <select class="form-select" id="discount_type" name="discount_type" style="width: 40%;">
                                                    <option value="percentage" <?php echo $quotation['discount_type'] === 'percentage' ? 'selected' : ''; ?>>%</option>
                                                    <option value="fixed" <?php echo $quotation['discount_type'] === 'fixed' ? 'selected' : ''; ?>>บาท</option>
                                                </select>
                                                <input type="number" class="form-control" id="discount_value" name="discount_value" value="<?php echo htmlspecialchars($quotation['discount_value']); ?>" min="0" step="0.01">
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span></span>
                                            <span id="discount_amount">0.00</span>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>ยอดก่อนภาษี:</span>
                                        <span id="amount_before_tax">0.00</span>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>ภาษีมูลค่าเพิ่ม:</span>
                                            <div class="input-group" style="width: 60%;">
                                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" value="<?php echo htmlspecialchars($quotation['tax_rate']); ?>" min="0" max="100" step="0.01">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span></span>
                                            <span id="tax_amount">0.00</span>
                                        </div>
                                    </div>
                                    
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>ยอดรวมทั้งสิ้น:</span>
                                        <span id="total_amount">0.00</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Status and Actions -->
                            <div class="card border-0 shadow-sm mb-4">
                                <div class="card-header bg-white py-3">
                                    <h5 class="card-title mb-0">สถานะและการดำเนินการ</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">สถานะ</label>
                                        <select class="form-select" id="status" name="status">
                                            <option value="draft" <?php echo $quotation['status'] === 'draft' ? 'selected' : ''; ?>>บันทึกเป็นร่าง</option>
                                            <option value="sent" <?php echo $quotation['status'] === 'sent' ? 'selected' : ''; ?>>ส่งให้ลูกค้า</option>
                                        </select>
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i> บันทึกใบเสนอราคา
                                        </button>
                                        <a href="list.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-1"></i> ยกเลิก
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Footer -->
            <footer class="bg-white p-3 text-center">
                <div class="container">
                    <p class="mb-0 text-muted">&copy; <?php echo date('Y'); ?> ระบบติดตามงานขายและการเสนอราคา | พัฒนาโดย <a href="#" class="text-decoration-none">บริษัท ตัวอย่าง จำกัด</a></p>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Item Template (Hidden) -->
    <template id="itemTemplate">
        <tr class="item-row">
            <td>
                <select class="form-select select2-products" name="product_id[]">
                    <option value="">-- เลือกสินค้า/บริการ --</option>
                    <?php foreach ($products as $product): ?>
                        <option value="<?php echo $product['id']; ?>" data-price="<?php echo $product['price']; ?>" data-description="<?php echo htmlspecialchars($product['description']); ?>">
                            <?php echo htmlspecialchars($product['name']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </td>
            <td>
                <textarea class="form-control product-description" name="description[]" rows="2"></textarea>
            </td>
            <td>
                <input type="number" class="form-control quantity" name="quantity[]" value="1" min="1" step="1">
            </td>
            <td>
                <input type="number" class="form-control unit-price" name="unit_price[]" value="0.00" min="0" step="0.01">
            </td>
            <td>
                <input type="number" class="form-control item-discount" name="item_discount[]" value="0" min="0" step="0.01">
            </td>
            <td class="text-center">
                <div class="form-check">
                    <input class="form-check-input item-tax" type="checkbox" name="item_tax[]" checked>
                </div>
            </td>
            <td>
                <span class="item-total">0.00</span>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger remove-item">
                    <i class="fas fa-times"></i>
                </button>
            </td>
        </tr>
    </template>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Flatpickr (Date Picker) -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/th.js"></script>
    
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize date pickers
            flatpickr(".datepicker", {
                dateFormat: "Y-m-d",
                locale: "th",
                allowInput: true,
                altInput: true,
                altFormat: "d/m/Y",
                disableMobile: true
            });
            
            // Initialize Select2
            $('.select2').select2({
                theme: 'bootstrap-5'
            });
            
            // Function to initialize Select2 for product dropdowns
            function initProductSelect2() {
                $('.select2-products').select2({
                    theme: 'bootstrap-5',
                    dropdownParent: $('#itemsContainer')
                });
            }
            
            // Function to add a new item row
            function addItemRow() {
                // Hide the "no items" row
                $('.no-items').hide();
                
                // Clone the template
                const template = document.getElementById('itemTemplate');
                const clone = template.content.cloneNode(true);
                
                // Append to the container
                document.getElementById('itemsContainer').appendChild(clone);
                
                // Initialize Select2 for the new row
                initProductSelect2();
                
                // Bind events for the new row
                bindItemEvents();
                
                // Update totals
                updateTotals();
            }
            
            // Function to bind events to item rows
            function bindItemEvents() {
                // Product selection change
                $('.select2-products').off('change').on('change', function() {
                    const row = $(this).closest('tr');
                    const option = $(this).find('option:selected');
                    const price = option.data('price') || 0;
                    const description = option.data('description') || '';
                    
                    row.find('.unit-price').val(price);
                    row.find('.product-description').val(description);
                    
                    updateRowTotal(row);
                    updateTotals();
                });
                
                // Quantity, price, or discount change
                $('.quantity, .unit-price, .item-discount, .item-tax').off('change input').on('change input', function() {
                    const row = $(this).closest('tr');
                    updateRowTotal(row);
                    updateTotals();
                });
                
                // Remove item button
                $('.remove-item').off('click').on('click', function() {
                    const row = $(this).closest('tr');
                    row.remove();
                    
                    // Show the "no items" row if there are no items
                    if ($('#itemsContainer tr.item-row').length === 0) {
                        $('.no-items').show();
                    }
                    
                    updateTotals();
                });
            }
            
            // Function to update a row's total
            function updateRowTotal(row) {
                const quantity = parseFloat(row.find('.quantity').val()) || 0;
                const unitPrice = parseFloat(row.find('.unit-price').val()) || 0;
                const discount = parseFloat(row.find('.item-discount').val()) || 0;
                
                let rowTotal = quantity * unitPrice;
                rowTotal = Math.max(0, rowTotal - discount);
                
                row.find('.item-total').text(rowTotal.toFixed(2));
            }
            
            // Function to update all totals
            function updateTotals() {
                let subtotal = 0;
                
                // Calculate subtotal from all items
                $('#itemsContainer tr.item-row').each(function() {
                    subtotal += parseFloat($(this).find('.item-total').text()) || 0;
                });
                
                // Update subtotal display
                $('#subtotal').text(subtotal.toFixed(2));
                
                // Calculate discount
                const discountType = $('#discount_type').val();
                const discountValue = parseFloat($('#discount_value').val()) || 0;
                let discountAmount = 0;
                
                if (discountType === 'percentage') {
                    discountAmount = subtotal * (discountValue / 100);
                } else {
                    discountAmount = discountValue;
                }
                
                // Update discount amount display
                $('#discount_amount').text(discountAmount.toFixed(2));
                
                // Calculate amount before tax
                const amountBeforeTax = Math.max(0, subtotal - discountAmount);
                $('#amount_before_tax').text(amountBeforeTax.toFixed(2));
                
                // Calculate tax
                const taxRate = parseFloat($('#tax_rate').val()) || 0;
                let taxAmount = 0;
                
                // Only apply tax to items with tax checked
                $('#itemsContainer tr.item-row').each(function() {
                    if ($(this).find('.item-tax').is(':checked')) {
                        const rowTotal = parseFloat($(this).find('.item-total').text()) || 0;
                        const rowProportion = rowTotal / subtotal;
                        const rowAmountBeforeTax = amountBeforeTax * rowProportion;
                        taxAmount += rowAmountBeforeTax * (taxRate / 100);
                    }
                });
                
                // Update tax amount display
                $('#tax_amount').text(taxAmount.toFixed(2));
                
                // Calculate total amount
                const totalAmount = amountBeforeTax + taxAmount;
                $('#total_amount').text(totalAmount.toFixed(2));
            }
            
            // Add first item button click
            $('#addFirstItem').on('click', function() {
                addItemRow();
            });
            
            // Add item button click
            $('#addItem').on('click', function() {
                addItemRow();
            });
            
            // Discount type or value change
            $('#discount_type, #discount_value').on('change input', function() {
                updateTotals();
            });
            
            // Tax rate change
            $('#tax_rate').on('change input', function() {
                updateTotals();
            });
            
            // Form submission validation
            $('#quotationForm').on('submit', function(e) {
                // Check if there are any items
                if ($('#itemsContainer tr.item-row').length === 0) {
                    e.preventDefault();
                    alert('กรุณาเพิ่มอย่างน้อยหนึ่งรายการสินค้า');
                    return false;
                }
                
                // Additional validation can be added here
                
                return true;
            });
            
            // Add an initial item row if there are no items
            if ($('#itemsContainer tr.item-row').length === 0) {
                // Don't add automatically, let user click the button
                // addItemRow();
            }
        });
    </script>
</body>
</html>

