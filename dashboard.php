<?php
/**
 * Dashboard Page
 * 
 * หน้าแดชบอร์ดสำหรับระบบติดตามงานขายและการเสนอราคา
 */

// เริ่มต้น Session
session_start();

// ตรวจสอบว่าผู้ใช้ล็อกอินแล้วหรือไม่
if (!isset($_SESSION['user_id'])) {
    // ถ้ายังไม่ได้ล็อกอินให้ redirect ไปที่หน้าล็อกอิน
    header('Location: auth/login.php');
    exit;
}

// โหลดไฟล์การตั้งค่า
require_once 'app/config/config.php';
require_once 'app/config/database.php';
require_once 'app/config/functions.php';

// โหลดโมเดลที่จำเป็น
require_once 'app/models/User.php';
require_once 'app/models/Customer.php';
require_once 'app/models/Opportunity.php';
require_once 'app/models/Quotation.php';
require_once 'app/models/SalesTarget.php';

// สร้างอินสแตนซ์ของโมเดล
$userModel = new User($conn);
$customerModel = new Customer($conn);
$opportunityModel = new Opportunity($conn);
$quotationModel = new Quotation($conn);
$salesTargetModel = new SalesTarget($conn);

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$currentUser = $userModel->getUserById($_SESSION['user_id']);

// ดึงข้อมูลสำหรับแดชบอร์ด
$totalCustomers = $customerModel->getTotalCustomers();
$totalOpportunities = $opportunityModel->getTotalOpportunities();
$totalQuotations = $quotationModel->getTotalQuotations();
$totalSales = $quotationModel->getTotalSalesAmount();

// ดึงข้อมูลโอกาสการขายล่าสุด
$recentOpportunities = $opportunityModel->getRecentOpportunities(5);

// ดึงข้อมูลใบเสนอราคาล่าสุด
$recentQuotations = $quotationModel->getRecentQuotations(5);

// ดึงข้อมูลเป้าหมายการขาย
$salesTarget = $salesTargetModel->getCurrentMonthTarget();
$salesAchieved = $quotationModel->getCurrentMonthSales();
$salesPercentage = ($salesTarget > 0) ? ($salesAchieved / $salesTarget) * 100 : 0;

// ดึงข้อมูลสำหรับกราฟยอดขายรายเดือน
$monthlySalesData = $quotationModel->getMonthlySalesData();

// ดึงข้อมูลสำหรับกราฟสถานะโอกาสการขาย
$opportunityStatusData = $opportunityModel->getOpportunityStatusData();

// ดึงข้อมูลกิจกรรมล่าสุด
$recentActivities = $userModel->getRecentActivities(10);

// กำหนดหัวข้อหน้า
$pageTitle = 'แดชบอร์ด';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ระบบติดตามงานขายและการเสนอราคา</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>ระบบติดตามงานขาย</h3>
            </div>
            
            <ul class="list-unstyled components">
                <li class="active">
                    <a href="dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> แดชบอร์ด
                    </a>
                </li>
                <li>
                    <a href="#salesSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-chart-line me-2"></i> งานขาย
                    </a>
                    <ul class="collapse list-unstyled" id="salesSubmenu">
                        <li>
                            <a href="sales/targets.php">เป้าหมายการขาย</a>
                        </li>
                        <li>
                            <a href="sales/opportunities.php">โอกาสการขาย</a>
                        </li>
                        <li>
                            <a href="sales/pipeline.php">Pipeline การขาย</a>
                        </li>
                        <li>
                            <a href="sales/forecasts.php">การคาดการณ์ยอดขาย</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#quotationSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-file-invoice me-2"></i> ใบเสนอราคา
                    </a>
                    <ul class="collapse list-unstyled" id="quotationSubmenu">
                        <li>
                            <a href="quotations/create.php">สร้างใบเสนอราคา</a>
                        </li>
                        <li>
                            <a href="quotations/list.php">รายการใบเสนอราคา</a>
                        </li>
                        <li>
                            <a href="quotations/templates.php">เทมเพลตใบเสนอราคา</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#customerSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-users me-2"></i> ลูกค้า
                    </a>
                    <ul class="collapse list-unstyled" id="customerSubmenu">
                        <li>
                            <a href="customers/list.php">รายชื่อลูกค้า</a>
                        </li>
                        <li>
                            <a href="customers/create.php">เพิ่มลูกค้าใหม่</a>
                        </li>
                        <li>
                            <a href="customers/groups.php">กลุ่มลูกค้า</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#productSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-box me-2"></i> สินค้าและบริการ
                    </a>
                    <ul class="collapse list-unstyled" id="productSubmenu">
                        <li>
                            <a href="products/list.php">รายการสินค้า</a>
                        </li>
                        <li>
                            <a href="products/create.php">เพิ่มสินค้าใหม่</a>
                        </li>
                        <li>
                            <a href="products/categories.php">หมวดหมู่สินค้า</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#paymentSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-money-bill-wave me-2"></i> การชำระเงิน
                    </a>
                    <ul class="collapse list-unstyled" id="paymentSubmenu">
                        <li>
                            <a href="payments/list.php">รายการชำระเงิน</a>
                        </li>
                        <li>
                            <a href="payments/create.php">บันทึกการชำระเงิน</a>
                        </li>
                        <li>
                            <a href="payments/invoices.php">ใบแจ้งหนี้</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#reportSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-chart-bar me-2"></i> รายงาน
                    </a>
                    <ul class="collapse list-unstyled" id="reportSubmenu">
                        <li>
                            <a href="reports/sales.php">รายงานยอดขาย</a>
                        </li>
                        <li>
                            <a href="reports/performance.php">รายงานประสิทธิภาพ</a>
                        </li>
                        <li>
                            <a href="reports/customers.php">รายงานลูกค้า</a>
                        </li>
                        <li>
                            <a href="reports/products.php">รายงานสินค้า</a>
                        </li>
                    </ul>
                </li>
                <?php if ($_SESSION['role'] === 'admin'): ?>
                <li>
                    <a href="#settingSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-cog me-2"></i> ตั้งค่า
                    </a>
                    <ul class="collapse list-unstyled" id="settingSubmenu">
                        <li>
                            <a href="settings/users.php">ผู้ใช้งาน</a>
                        </li>
                        <li>
                            <a href="settings/roles.php">สิทธิ์การใช้งาน</a>
                        </li>
                        <li>
                            <a href="settings/company.php">ข้อมูลบริษัท</a>
                        </li>
                        <li>
                            <a href="settings/system.php">ตั้งค่าระบบ</a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
            
            <div class="sidebar-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-white-50">เข้าสู่ระบบล่าสุด:</small><br>
                        <small class="text-white-50"><?php echo date('d/m/Y H:i', strtotime($currentUser['last_login'])); ?></small>
                    </div>
                    <a href="auth/logout.php" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white">
                <div class="container-fluid">
                    <button type="button" id="sidebar-toggle" class="btn btn-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="ms-auto d-flex align-items-center">
                        <!-- Notifications -->
                        <div class="dropdown me-3">
                            <a class="btn btn-light position-relative" href="#" role="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
                                <div class="notification-header">
                                    <h6 class="notification-title">การแจ้งเตือน</h6>
                                </div>
                                <div class="notification-list">
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">มีใบเสนอราคาใหม่ #QT-2023-0042</div>
                                        <div class="notification-item-time">เมื่อ 5 นาทีที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">ลูกค้ายืนยันการชำระเงิน #INV-2023-0036</div>
                                        <div class="notification-item-time">เมื่อ 2 ชั่วโมงที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">มีโอกาสการขายใหม่จาก บริษัท ABC จำกัด</div>
                                        <div class="notification-item-time">เมื่อ 3 ชั่วโมงที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item">
                                        <div class="notification-item-title">ถึงกำหนดติดตามลูกค้า บริษัท XYZ จำกัด</div>
                                        <div class="notification-item-time">เมื่อ 1 วันที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item">
                                        <div class="notification-item-title">ใบเสนอราคา #QT-2023-0041 หมดอายุในอีก 3 วัน</div>
                                        <div class="notification-item-time">เมื่อ 2 วันที่แล้ว</div>
                                    </a>
                                </div>
                                <div class="notification-footer">
                                    <a href="notifications.php" class="text-primary">ดูการแจ้งเตือนทั้งหมด</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Profile -->
                        <div class="dropdown">
                            <a class="btn btn-light dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="<?php echo !empty($currentUser['profile_image']) ? $currentUser['profile_image'] : 'assets/images/default-avatar.png'; ?>" alt="Profile" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php echo $currentUser['name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i> โปรไฟล์</a></li>
                                <li><a class="dropdown-item" href="settings/preferences.php"><i class="fas fa-cog me-2"></i> ตั้งค่า</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i> ออกจากระบบ</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Page Content -->
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">แดชบอร์ด</h1>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary me-2">
                            <i class="fas fa-calendar-alt me-1"></i> วันนี้: <?php echo date('d/m/Y'); ?>
                        </button>
                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#quickActionModal">
                            <i class="fas fa-plus me-1"></i> สร้างใหม่
                        </button>
                    </div>
                </div>
                
                <!-- Stats Cards -->
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-2">ลูกค้าทั้งหมด</h6>
                                        <h3 class="mb-0"><?php echo number_format($totalCustomers); ?></h3>
                                    </div>
                                    <div class="bg-primary bg-opacity-10 p-3 rounded">
                                        <i class="fas fa-users text-primary fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="customers/list.php" class="text-decoration-none">
                                        <small>ดูรายละเอียด <i class="fas fa-arrow-right ms-1"></i></small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-2">โอกาสการขาย</h6>
                                        <h3 class="mb-0"><?php echo number_format($totalOpportunities); ?></h3>
                                    </div>
                                    <div class="bg-success bg-opacity-10 p-3 rounded">
                                        <i class="fas fa-chart-line text-success fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="sales/opportunities.php" class="text-decoration-none">
                                        <small>ดูรายละเอียด <i class="fas fa-arrow-right ms-1"></i></small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-2">ใบเสนอราคา</h6>
                                        <h3 class="mb-0"><?php echo number_format($totalQuotations); ?></h3>
                                    </div>
                                    <div class="bg-info bg-opacity-10 p-3 rounded">
                                        <i class="fas fa-file-invoice text-info fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="quotations/list.php" class="text-decoration-none">
                                        <small>ดูรายละเอียด <i class="fas fa-arrow-right ms-1"></i></small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="text-muted mb-2">ยอดขายรวม</h6>
                                        <h3 class="mb-0"><?php echo number_format($totalSales, 2); ?> บาท</h3>
                                    </div>
                                    <div class="bg-warning bg-opacity-10 p-3 rounded">
                                        <i class="fas fa-money-bill-wave text-warning fa-2x"></i>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="reports/sales.php" class="text-decoration-none">
                                        <small>ดูรายละเอียด <i class="fas fa-arrow-right ms-1"></i></small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Sales Target Progress -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">เป้าหมายการขายประจำเดือน</h5>
                                    <a href="sales/targets.php" class="btn btn-sm btn-outline-primary">จัดการเป้าหมาย</a>
                                </div>
                                
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <div class="progress" style="height: 25px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo min($salesPercentage, 100); ?>%;" aria-valuenow="<?php echo $salesPercentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                                <?php echo number_format($salesPercentage, 1); ?>%
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-md-end mt-3 mt-md-0">
                                        <h5 class="mb-0"><?php echo number_format($salesAchieved, 2); ?> / <?php echo number_format($salesTarget, 2); ?> บาท</h5>
                                        <small class="text-muted">ประจำเดือน <?php echo date('F Y'); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts -->
                <div class="row mb-4">
                    <div class="col-md-8 mb-4 mb-md-0">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h5 class="card-title mb-0">ยอดขายรายเดือน</h5>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-outline-secondary">รายวัน</button>
                                        <button type="button" class="btn btn-sm btn-primary">รายเดือน</button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary">รายปี</button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="monthlySalesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <h5 class="card-title mb-0">สถานะโอกาสการขาย</h5>
                                    <a href="sales/opportunities.php" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
                                </div>
                                <div class="chart-container">
                                    <canvas id="opportunityStatusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Data -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">โอกาสการขายล่าสุด</h5>
                                    <a href="sales/opportunities.php" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>ชื่อโอกาส</th>
                                                <th>ลูกค้า</th>
                                                <th>มูลค่า</th>
                                                <th>สถานะ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOpportunities as $opportunity): ?>
                                            <tr>
                                                <td>
                                                    <a href="sales/opportunity_detail.php?id=<?php echo $opportunity['id']; ?>" class="text-decoration-none">
                                                        <?php echo $opportunity['name']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo $opportunity['customer_name']; ?></td>
                                                <td><?php echo number_format($opportunity['value'], 2); ?> บาท</td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($opportunity['status']) {
                                                        case 'new':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'ใหม่';
                                                            break;
                                                        case 'qualified':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'มีคุณสมบัติ';
                                                            break;
                                                        case 'proposal':
                                                            $statusClass = 'bg-warning';
                                                            $statusText = 'เสนอราคา';
                                                            break;
                                                        case 'negotiation':
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'เจรจาต่อรอง';
                                                            break;
                                                        case 'won':
                                                            $statusClass = 'bg-success';
                                                            $statusText = 'ปิดการขาย';
                                                            break;
                                                        case 'lost':
                                                            $statusClass = 'bg-danger';
                                                            $statusText = 'สูญเสีย';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = $opportunity['status'];
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            
                                            <?php if (empty($recentOpportunities)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center">ไม่พบข้อมูลโอกาสการขาย</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">ใบเสนอราคาล่าสุด</h5>
                                    <a href="quotations/list.php" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>เลขที่</th>
                                                <th>ลูกค้า</th>
                                                <th>มูลค่า</th>
                                                <th>สถานะ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentQuotations as $quotation): ?>
                                            <tr>
                                                <td>
                                                    <a href="quotations/view.php?id=<?php echo $quotation['id']; ?>" class="text-decoration-none">
                                                        <?php echo $quotation['quotation_no']; ?>
                                                    </a>
                                                </td>
                                                <td><?php echo $quotation['customer_name']; ?></td>
                                                <td><?php echo number_format($quotation['total_amount'], 2); ?> บาท</td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    switch ($quotation['status']) {
                                                        case 'draft':
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'ร่าง';
                                                            break;
                                                        case 'sent':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'ส่งแล้ว';
                                                            break;
                                                        case 'viewed':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'ดูแล้ว';
                                                            break;
                                                        case 'accepted':
                                                            $statusClass = 'bg-success';
                                                            $statusText = 'ยอมรับ';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'bg-danger';
                                                            $statusText = 'ปฏิเสธ';
                                                            break;
                                                        case 'expired':
                                                            $statusClass = 'bg-warning';
                                                            $statusText = 'หมดอายุ';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = $quotation['status'];
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                            
                                            <?php if (empty($recentQuotations)): ?>
                                            <tr>
                                                <td colspan="4" class="text-center">ไม่พบข้อมูลใบเสนอราคา</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activities -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title mb-0">กิจกรรมล่าสุด</h5>
                                    <a href="activities.php" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
                                </div>
                                
                                <div class="timeline">
                                    <?php foreach ($recentActivities as $activity): ?>
                                    <div class="timeline-item">
                                        <div class="timeline-item-content">
                                            <div class="timeline-item-header">
                                                <h6 class="timeline-item-title"><?php echo $activity['title']; ?></h6>
                                                <span class="timeline-item-date"><?php echo date('d/m/Y H:i', strtotime($activity['created_at'])); ?></span>
                                            </div>
                                            <p><?php echo $activity['description']; ?></p>
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo !empty($activity['user_image']) ? $activity['user_image'] : 'assets/images/default-avatar.png'; ?>" alt="User" class="rounded-circle me-2" style="width: 24px; height: 24px; object-fit: cover;">
                                                <small class="text-muted"><?php echo $activity['user_name']; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                    
                                    <?php if (empty($recentActivities)): ?>
                                    <div class="text-center py-4">
                                        <p class="text-muted">ไม่พบข้อมูลกิจกรรม</p>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <footer class="bg-white p-3 text-center">
                <div class="container">
                    <p class="mb-0 text-muted">&copy; <?php echo date('Y'); ?> ระบบติดตามงานขายและการเสนอราคา | พัฒนาโดย <a href="#" class="text-decoration-none">บริษัท ตัวอย่าง จำกัด</a></p>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Quick Action Modal -->
    <div class="modal fade" id="quickActionModal" tabindex="-1" aria-labelledby="quickActionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="quickActionModalLabel">สร้างใหม่</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group">
                        <a href="customers/create.php" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-user-plus text-primary"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">เพิ่มลูกค้าใหม่</h6>
                                    <small class="text-muted">เพิ่มข้อมูลลูกค้าใหม่เข้าระบบ</small>
                                </div>
                            </div>
                        </a>
                        <a href="sales/opportunity_create.php" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <div class="bg-success bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-chart-line text-success"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">สร้างโอกาสการขาย</h6>
                                    <small class="text-muted">บันทึกโอกาสการขายใหม่</small>
                                </div>
                            </div>
                        </a>
                        <a href="quotations/create.php" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <div class="bg-info bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-file-invoice text-info"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">สร้างใบเสนอราคา</h6>
                                    <small class="text-muted">สร้างใบเสนอราคาใหม่</small>
                                </div>
                            </div>
                        </a>
                        <a href="products/create.php" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning bg-opacity-10 p-3 rounded me-3">
                                    <i class="fas fa-box text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">เพิ่มสินค้า/บริการ</h6>
                                    <small class="text-muted">เพิ่มสินค้าหรือบริการใหม่</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Chart for Monthly Sales
        const monthlySalesCtx = document.getElementById('monthlySalesChart').getContext('2d');
        const monthlySalesChart = new Chart(monthlySalesCtx, {
            type: 'bar',
            data: {
                labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.', 'ก.ค.', 'ส.ค.', 'ก.ย.', 'ต.ค.', 'พ.ย.', 'ธ.ค.'],
                datasets: [
                    {
                        label: 'ยอดขาย (บาท)',
                        data: <?php echo json_encode(array_values($monthlySalesData['sales'])); ?>,
                        backgroundColor: 'rgba(52, 152, 219, 0.5)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'เป้าหมาย (บาท)',
                        data: <?php echo json_encode(array_values($monthlySalesData['targets'])); ?>,
                        type: 'line',
                        fill: false,
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(231, 76, 60, 1)',
                        tension: 0.4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' บาท';
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += new Intl.NumberFormat('th-TH', { style: 'currency', currency: 'THB' }).format(context.parsed.y);
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
        
        // Chart for Opportunity Status
        const opportunityStatusCtx = document.getElementById('opportunityStatusChart').getContext('2d');
        const opportunityStatusChart = new Chart(opportunityStatusCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_keys($opportunityStatusData)); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_values($opportunityStatusData)); ?>,
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.7)',  // ใหม่
                        'rgba(155, 89, 182, 0.7)',  // มีคุณสมบัติ
                        'rgba(241, 196, 15, 0.7)',  // เสนอราคา
                        'rgba(230, 126, 34, 0.7)',  // เจรจาต่อรอง
                        'rgba(46, 204, 113, 0.7)',  // ปิดการขาย
                        'rgba(231, 76, 60, 0.7)'    // สูญเสีย
                    ],
                    borderColor: [
                        'rgba(52, 152, 219, 1)',
                        'rgba(155, 89, 182, 1)',
                        'rgba(241, 196, 15, 1)',
                        'rgba(230, 126, 34, 1)',
                        'rgba(46, 204, 113, 1)',
                        'rgba(231, 76, 60, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed !== null) {
                                    label += context.parsed + ' รายการ';
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>

