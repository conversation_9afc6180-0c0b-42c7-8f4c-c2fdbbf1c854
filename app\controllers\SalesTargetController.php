<?php
/**
 * Sales Target Controller
 */

class SalesTargetController extends BaseController {
    private $salesTargetModel;
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->salesTargetModel = new SalesTarget();
        $this->userModel = new User();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $targets = $this->salesTargetModel->getPaginated($page);
        $this->setPagination($targets);
        
        $this->data['targets'] = $targets['items'];
        $this->data['page_title'] = 'รายการเป้าหมายการขาย';
        $this->view('sales_targets/index');
    }
    
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $target = $this->salesTargetModel->getById($id);
        if (!$target) {
            $this->show404();
            return;
        }
        
        $this->data['target'] = $target;
        $this->data['page_title'] = 'รายละเอียดเป้าหมายการขาย';
        $this->view('sales_targets/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $users = $this->userModel->getForDropdown();
        
        $this->data['users'] = $users;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'สร้างเป้าหมายการขายใหม่';
        $this->view('sales_targets/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=salesTarget&action=create');
            return;
        }
        
        $data = $this->post();
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->salesTargetModel->create($data);
        
        if ($id) {
            showAlert('สร้างเป้าหมายการขายเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=salesTarget&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=salesTarget&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $target = $this->salesTargetModel->getById($id);
        if (!$target) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $users = $this->userModel->getForDropdown();
        
        $this->data['target'] = $target;
        $this->data['users'] = $users;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขเป้าหมายการขาย';
        $this->view('sales_targets/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=salesTarget&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->salesTargetModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขเป้าหมายการขายเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=salesTarget&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=salesTarget&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->salesTargetModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
