<?php
/**
 * Contact Model
 * 
 * โมเดลสำหรับจัดการข้อมูลผู้ติดต่อ
 */

class Contact extends BaseModel {
    protected $table = 'contacts';
    protected $primaryKey = 'id';
    protected $fillable = [
        'customer_id',
        'first_name',
        'last_name',
        'position',
        'department',
        'email',
        'phone',
        'mobile',
        'is_primary',
        'notes',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลผู้ติดต่อของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return array
     */
    public function getByCustomerId($customerId) {
        return $this->getWhere(['customer_id' => $customerId]);
    }
    
    /**
     * ดึงข้อมูลผู้ติดต่อหลักของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return array|bool
     */
    public function getPrimaryContact($customerId) {
        return $this->getOneWhere([
            'customer_id' => $customerId,
            'is_primary' => 1
        ]);
    }
    
    /**
     * ตั้งค่าผู้ติดต่อหลัก
     * 
     * @param int $contactId ID ของผู้ติดต่อ
     * @param int $customerId ID ของลูกค้า
     * @return bool
     */
    public function setPrimaryContact($contactId, $customerId) {
        // ยกเลิกการเป็นผู้ติดต่อหลักของผู้ติดต่ออื่นๆ
        $sql = "UPDATE " . TABLE_PREFIX . $this->table . " SET is_primary = 0 WHERE customer_id = :customer_id";
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $customerId]);
        $this->db->execute();
        
        // ตั้งค่าผู้ติดต่อใหม่เป็นหลัก
        return $this->update($contactId, ['is_primary' => 1]);
    }
    
    /**
     * ค้นหาผู้ติดต่อ
     * 
     * @param string $keyword คำค้นหา
     * @return array
     */
    public function searchContacts($keyword) {
        $searchFields = ['first_name', 'last_name', 'email', 'phone', 'mobile', 'position'];
        return $this->search($keyword, $searchFields);
    }
    
    /**
     * ดึงข้อมูลผู้ติดต่อพร้อมข้อมูลลูกค้า
     * 
     * @param int $contactId ID ของผู้ติดต่อ
     * @return array|bool
     */
    public function getContactWithCustomer($contactId) {
        $sql = "SELECT c.*, cu.company_name, cu.customer_code 
                FROM " . TABLE_PREFIX . $this->table . " c
                LEFT JOIN " . TABLE_PREFIX . "customers cu ON c.customer_id = cu.id
                WHERE c.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $contactId]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลผู้ติดต่อทั้งหมดพร้อมข้อมูลลูกค้า
     * 
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function getAllContactsWithCustomer($page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total FROM " . TABLE_PREFIX . $this->table;
        $this->db->prepare($countSql);
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT c.*, cu.company_name, cu.customer_code 
                FROM " . TABLE_PREFIX . $this->table . " c
                LEFT JOIN " . TABLE_PREFIX . "customers cu ON c.customer_id = cu.id
                ORDER BY c.created_at DESC
                LIMIT :offset, :perPage";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':offset' => $offset,
            ':perPage' => $perPage
        ]);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * ตรวจสอบว่าอีเมลซ้ำหรือไม่
     * 
     * @param string $email อีเมล
     * @param int $excludeId ID ที่ต้องการยกเว้น (สำหรับการแก้ไข)
     * @return bool
     */
    public function isEmailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " WHERE email = :email";
        $params = [':email' => $email];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $result = $this->db->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * ลบผู้ติดต่อทั้งหมดของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return bool
     */
    public function deleteByCustomerId($customerId) {
        $sql = "DELETE FROM " . TABLE_PREFIX . $this->table . " WHERE customer_id = :customer_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $customerId]);
        
        return $this->db->execute();
    }
    
    /**
     * นับจำนวนผู้ติดต่อของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return int
     */
    public function countByCustomerId($customerId) {
        return $this->count(['customer_id' => $customerId]);
    }
    
    /**
     * ดึงข้อมูลผู้ติดต่อสำหรับ dropdown
     * 
     * @param int $customerId ID ของลูกค้า (ถ้ามี)
     * @return array
     */
    public function getForDropdown($customerId = null) {
        $sql = "SELECT id, CONCAT(first_name, ' ', last_name) as full_name, email 
                FROM " . TABLE_PREFIX . $this->table;
        
        $params = [];
        
        if ($customerId) {
            $sql .= " WHERE customer_id = :customer_id";
            $params[':customer_id'] = $customerId;
        }
        
        $sql .= " ORDER BY first_name, last_name";
        
        $this->db->prepare($sql);
        
        if (!empty($params)) {
            $this->db->bind($params);
        }
        
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
}
