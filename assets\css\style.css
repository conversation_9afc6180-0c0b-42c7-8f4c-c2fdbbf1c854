/**
 * Main CSS File
 * 
 * ไฟล์ CSS หลักสำหรับระบบติดตามงานขายและการเสนอราคา
 */

/* ====================== GENERAL STYLES ====================== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #2ecc71;
    --info-color: #3498db;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gray-color: #95a5a6;
    --white-color: #ffffff;
    --black-color: #000000;
    --border-color: #ddd;
    --text-color: #333;
    --text-muted: #7f8c8d;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

body {
    font-family: 'Sarabun', 'Prompt', 'Kanit', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f5f5f5;
    margin: 0;
    padding: 0;
}

a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

a:hover {
    color: var(--primary-color);
    text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-light { color: var(--light-color) !important; }
.text-dark { color: var(--dark-color) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }
.bg-white { background-color: var(--white-color) !important; }

.btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); }
.btn-secondary { background-color: var(--secondary-color); border-color: var(--secondary-color); }
.btn-success { background-color: var(--success-color); border-color: var(--success-color); }
.btn-info { background-color: var(--info-color); border-color: var(--info-color); }
.btn-warning { background-color: var(--warning-color); border-color: var(--warning-color); }
.btn-danger { background-color: var(--danger-color); border-color: var(--danger-color); }
.btn-light { background-color: var(--light-color); border-color: var(--light-color); color: var(--dark-color); }
.btn-dark { background-color: var(--dark-color); border-color: var(--dark-color); }

.btn-outline-primary { color: var(--primary-color); border-color: var(--primary-color); }
.btn-outline-secondary { color: var(--secondary-color); border-color: var(--secondary-color); }
.btn-outline-success { color: var(--success-color); border-color: var(--success-color); }
.btn-outline-info { color: var(--info-color); border-color: var(--info-color); }
.btn-outline-warning { color: var(--warning-color); border-color: var(--warning-color); }
.btn-outline-danger { color: var(--danger-color); border-color: var(--danger-color); }
.btn-outline-light { color: var(--light-color); border-color: var(--light-color); }
.btn-outline-dark { color: var(--dark-color); border-color: var(--dark-color); }

.btn-outline-primary:hover { background-color: var(--primary-color); color: var(--white-color); }
.btn-outline-secondary:hover { background-color: var(--secondary-color); color: var(--white-color); }
.btn-outline-success:hover { background-color: var(--success-color); color: var(--white-color); }
.btn-outline-info:hover { background-color: var(--info-color); color: var(--white-color); }
.btn-outline-warning:hover { background-color: var(--warning-color); color: var(--white-color); }
.btn-outline-danger:hover { background-color: var(--danger-color); color: var(--white-color); }
.btn-outline-light:hover { background-color: var(--light-color); color: var(--dark-color); }
.btn-outline-dark:hover { background-color: var(--dark-color); color: var(--white-color); }

/* ====================== LAYOUT STYLES ====================== */
.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
}

.content {
    width: 100%;
    padding: 20px;
    min-height: 100vh;
    transition: all var(--transition-speed);
}

.container-fluid {
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--white-color);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px var(--shadow-color);
    margin-bottom: 20px;
}

.card-header {
    padding: 0.75rem 1.25rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--border-color);
}

/* ====================== SIDEBAR STYLES ====================== */
#sidebar {
    min-width: 250px;
    max-width: 250px;
    background: var(--primary-color);
    color: var(--white-color);
    transition: all var(--transition-speed);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    padding: 20px;
    background: var(--dark-color);
}

#sidebar .sidebar-header h3 {
    color: var(--white-color);
    margin: 0;
}

#sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid var(--dark-color);
}

#sidebar ul p {
    color: var(--white-color);
    padding: 10px;
}

#sidebar ul li a {
    padding: 10px;
    font-size: 1.1em;
    display: block;
    color: var(--white-color);
    border-left: 3px solid transparent;
}

#sidebar ul li a:hover {
    color: var(--primary-color);
    background: var(--white-color);
    border-left: 3px solid var(--secondary-color);
}

#sidebar ul li.active > a,
#sidebar a[aria-expanded="true"] {
    color: var(--white-color);
    background: var(--dark-color);
    border-left: 3px solid var(--secondary-color);
}

#sidebar ul ul a {
    font-size: 0.9em !important;
    padding-left: 30px !important;
    background: var(--dark-color);
}

#sidebar .sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px;
    background: var(--dark-color);
    border-top: 1px solid var(--dark-color);
}

#sidebar-toggle {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white-color);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    position: fixed;
    top: 20px;
    left: 260px;
    z-index: 999;
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    justify-content: center;
}

#sidebar-toggle.active {
    left: 10px;
}

#sidebar-toggle:focus {
    outline: none;
}

/* ====================== NAVBAR STYLES ====================== */
.navbar {
    padding: 15px 10px;
    background: var(--white-color);
    border: none;
    border-radius: 0;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.navbar-btn {
    box-shadow: none;
    outline: none !important;
    border: none;
}

.navbar-brand {
    color: var(--primary-color);
    font-weight: 600;
}

.navbar-nav .nav-link {
    color: var(--text-color);
}

.navbar-nav .nav-link:hover {
    color: var(--secondary-color);
}

.navbar-nav .active > .nav-link {
    color: var(--secondary-color);
}

.dropdown-menu {
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.dropdown-item:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* ====================== DASHBOARD STYLES ====================== */
.dashboard-stats {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.stat-card {
    flex: 1 1 250px;
    margin: 10px;
    padding: 20px;
    border-radius: 5px;
    background-color: var(--white-color);
    box-shadow: 0 2px 5px var(--shadow-color);
    transition: all var(--transition-speed);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.stat-card .stat-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

.stat-card .stat-value {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-card .stat-title {
    font-size: 1rem;
    color: var(--text-muted);
}

.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 20px;
}

/* ====================== TABLE STYLES ====================== */
.table-container {
    overflow-x: auto;
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid var(--border-color);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--border-color);
    background-color: var(--light-color);
    color: var(--primary-color);
}

.table tbody + tbody {
    border-top: 2px solid var(--border-color);
}

.table-sm th,
.table-sm td {
    padding: 0.3rem;
}

.table-bordered {
    border: 1px solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* ====================== FORM STYLES ====================== */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--white-color);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    transition: border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.form-control:focus {
    color: var(--text-color);
    background-color: var(--white-color);
    border-color: var(--secondary-color);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-control-sm {
    height: calc(1.5em + 0.5rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.form-control-lg {
    height: calc(1.5em + 1rem + 2px);
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0.3rem;
}

.form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem;
}

.form-check-input {
    position: absolute;
    margin-top: 0.3rem;
    margin-left: -1.25rem;
}

.form-check-label {
    margin-bottom: 0;
}

.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: var(--text-muted);
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: var(--danger-color);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: var(--danger-color);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23e74c3c' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e74c3c' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
}

.was-validated .form-control:invalid ~ .invalid-feedback,
.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

/* ====================== BUTTON STYLES ====================== */
.btn {
    display: inline-block;
    font-weight: 400;
    color: var(--text-color);
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color var(--transition-speed) ease-in-out, background-color var(--transition-speed) ease-in-out, border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.btn:hover {
    color: var(--text-color);
    text-decoration: none;
}

.btn:focus, .btn.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-lg {
    padding: 0.5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0.3rem;
}

.btn-block {
    display: block;
    width: 100%;
}

.btn-block + .btn-block {
    margin-top: 0.5rem;
}

/* ====================== ALERT STYLES ====================== */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-primary {
    color: #1b4f72;
    background-color: #d6eaf8;
    border-color: #c6e0f5;
}

.alert-secondary {
    color: #1f2c38;
    background-color: #d8dfe2;
    border-color: #c8d1d7;
}

.alert-success {
    color: #1e7e34;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-light {
    color: #818182;
    background-color: #fefefe;
    border-color: #fdfdfe;
}

.alert-dark {
    color: #1b1e21;
    background-color: #d6d8d9;
    border-color: #c6c8ca;
}

.alert-dismissible {
    padding-right: 4rem;
}

.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1.25rem;
    color: inherit;
}

/* ====================== BADGE STYLES ====================== */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    transition: color var(--transition-speed) ease-in-out, background-color var(--transition-speed) ease-in-out, border-color var(--transition-speed) ease-in-out, box-shadow var(--transition-speed) ease-in-out;
}

.badge-primary { background-color: var(--primary-color); color: var(--white-color); }
.badge-secondary { background-color: var(--secondary-color); color: var(--white-color); }
.badge-success { background-color: var(--success-color); color: var(--white-color); }
.badge-info { background-color: var(--info-color); color: var(--white-color); }
.badge-warning { background-color: var(--warning-color); color: var(--white-color); }
.badge-danger { background-color: var(--danger-color); color: var(--white-color); }
.badge-light { background-color: var(--light-color); color: var(--dark-color); }
.badge-dark { background-color: var(--dark-color); color: var(--white-color); }

/* ====================== PAGINATION STYLES ====================== */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: var(--secondary-color);
    background-color: var(--white-color);
    border: 1px solid var(--border-color);
}

.page-link:hover {
    z-index: 2;
    color: var(--primary-color);
    text-decoration: none;
    background-color: var(--light-color);
    border-color: var(--border-color);
}

.page-link:focus {
    z-index: 3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.page-item:last-child .page-link {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.page-item.active .page-link {
    z-index: 3;
    color: var(--white-color);
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.page-item.disabled .page-link {
    color: var(--gray-color);
    pointer-events: none;
    cursor: auto;
    background-color: var(--white-color);
    border-color: var(--border-color);
}

/* ====================== MODAL STYLES ====================== */
.modal-open {
    overflow: hidden;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    display: none;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

.modal.fade .modal-dialog {
    transition: transform var(--transition-speed) ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: var(--white-color);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid var(--border-color);
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
}

.modal-footer > * {
    margin: 0.25rem;
}

/* ====================== LOGIN STYLES ====================== */
.login-page {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-color);
}

.login-form {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background-color: var(--white-color);
    border-radius: 5px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.login-form .logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-form .logo img {
    max-width: 150px;
}

.login-form h3 {
    text-align: center;
    margin-bottom: 30px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form .form-control {
    height: 45px;
    border-radius: 4px;
}

.login-form .btn {
    height: 45px;
    font-size: 16px;
}

.login-form .forgot-password {
    text-align: right;
    margin-bottom: 20px;
}

/* ====================== RESPONSIVE STYLES ====================== */
@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
}

@media (min-width: 768px) {
    .content {
        padding-left: 270px;
    }
    
    .content.active {
        padding-left: 20px;
    }
}

@media (max-width: 767.98px) {
    #sidebar {
        margin-left: -250px;
    }
    
    #sidebar.active {
        margin-left: 0;
    }
    
    #sidebar-toggle {
        left: 10px;
    }
    
    #sidebar-toggle.active {
        left: 260px;
    }
}

/* ====================== UTILITY CLASSES ====================== */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 0.25rem !important; }
.ml-2 { margin-left: 0.5rem !important; }
.ml-3 { margin-left: 1rem !important; }
.ml-4 { margin-left: 1.5rem !important; }
.ml-5 { margin-left: 3rem !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 1rem !important; }
.mr-4 { margin-right: 1.5rem !important; }
.mr-5 { margin-right: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 1rem !important; }
.pb-4 { padding-bottom: 1.5rem !important; }
.pb-5 { padding-bottom: 3rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 1rem !important; }
.pt-4 { padding-top: 1.5rem !important; }
.pt-5 { padding-top: 3rem !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 0.25rem !important; }
.pl-2 { padding-left: 0.5rem !important; }
.pl-3 { padding-left: 1rem !important; }
.pl-4 { padding-left: 1.5rem !important; }
.pl-5 { padding-left: 3rem !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 0.25rem !important; }
.pr-2 { padding-right: 0.5rem !important; }
.pr-3 { padding-right: 1rem !important; }
.pr-4 { padding-right: 1.5rem !important; }
.pr-5 { padding-right: 3rem !important; }

.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }

.rounded { border-radius: 0.25rem !important; }
.rounded-top { border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important; }
.rounded-right { border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important; }
.rounded-bottom { border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important; }
.rounded-left { border-top-left-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-0 { border-radius: 0 !important; }

.shadow-sm { box-shadow: 0 0.125rem 0.25rem var(--shadow-color) !important; }
.shadow { box-shadow: 0 0.5rem 1rem var(--shadow-color) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem var(--shadow-color) !important; }
.shadow-none { box-shadow: none !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.font-weight-light { font-weight: 300 !important; }
.font-weight-normal { font-weight: 400 !important; }
.font-weight-bold { font-weight: 700 !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-break {
    word-break: break-word !important;
    overflow-wrap: break-word !important;
}

.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.fixed-bottom {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1030;
}

.sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020;
}

/* ====================== CUSTOM STYLES ====================== */
/* Sales Pipeline */
.pipeline-container {
    display: flex;
    overflow-x: auto;
    padding: 20px 0;
}

.pipeline-stage {
    min-width: 250px;
    margin-right: 20px;
    background-color: var(--light-color);
    border-radius: 5px;
    padding: 15px;
}

.pipeline-stage-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.pipeline-stage-title {
    font-weight: 600;
    margin: 0;
}

.pipeline-card {
    background-color: var(--white-color);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px var(--shadow-color);
    cursor: pointer;
    transition: all var(--transition-speed);
}

.pipeline-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.pipeline-card-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.pipeline-card-customer {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 10px;
}

.pipeline-card-amount {
    font-weight: 600;
    color: var(--primary-color);
}

.pipeline-card-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Quotation Preview */
.quotation-preview {
    background-color: var(--white-color);
    padding: 30px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.quotation-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.quotation-logo {
    max-width: 200px;
}

.quotation-info {
    text-align: right;
}

.quotation-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.quotation-addresses {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.quotation-address {
    width: 48%;
}

.quotation-address h5 {
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

.quotation-items {
    margin-bottom: 30px;
}

.quotation-summary {
    width: 350px;
    margin-left: auto;
}

.quotation-summary-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
}

.quotation-summary-row.total {
    font-weight: 600;
    font-size: 1.2rem;
    border-top: 2px solid var(--border-color);
    padding-top: 10px;
}

.quotation-notes {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.quotation-terms {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Dashboard Widgets */
.widget {
    background-color: var(--white-color);
    border-radius: 5px;
    box-shadow: 0 2px 5px var(--shadow-color);
    margin-bottom: 20px;
    overflow: hidden;
}

.widget-header {
    padding: 15px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.widget-title {
    margin: 0;
    font-weight: 600;
}

.widget-body {
    padding: 15px;
}

.widget-footer {
    padding: 10px 15px;
    background-color: var(--light-color);
    border-top: 1px solid var(--border-color);
    text-align: right;
}

/* Activity Timeline */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-left: 50px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 16px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--secondary-color);
    border: 2px solid var(--white-color);
}

.timeline-item-content {
    background-color: var(--white-color);
    border-radius: 5px;
    padding: 15px;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.timeline-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.timeline-item-title {
    font-weight: 600;
    margin: 0;
}

.timeline-item-date {
    color: var(--text-muted);
    font-size: 0.9rem;
}

/* Notification Dropdown */
.notification-dropdown {
    min-width: 300px;
    padding: 0;
}

.notification-header {
    padding: 10px 15px;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
}

.notification-title {
    margin: 0;
    font-weight: 600;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 10px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-speed);
}

.notification-item:hover {
    background-color: var(--light-color);
}

.notification-item.unread {
    background-color: rgba(52, 152, 219, 0.1);
}

.notification-item-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.notification-item-time {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.notification-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

