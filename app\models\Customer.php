<?php
/**
 * Customer Model
 * 
 * คลาสสำหรับจัดการข้อมูลลูกค้า
 */

class Customer extends BaseModel {
    protected $table = 'customers';
    protected $fillable = [
        'name', 'address', 'phone', 'email', 'website', 'industry', 
        'size', 'type', 'status', 'created_by', 'created_at', 'updated_at'
    ];
    
    /**
     * คอนสตรักเตอร์
     */
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * สร้างลูกค้าใหม่
     * 
     * @param array $data ข้อมูลลูกค้า
     * @return int|bool
     */
    public function create($data) {
        // เพิ่มวันที่สร้างและอัปเดต
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return parent::create($data);
    }
    
    /**
     * อัปเดตลูกค้า
     * 
     * @param int $id รหัสลูกค้า
     * @param array $data ข้อมูลที่ต้องการอัปเดต
     * @return bool
     */
    public function update($id, $data) {
        // อัปเดตวันที่แก้ไข
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return parent::update($id, $data);
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลผู้สร้าง
     * 
     * @param int $id รหัสลูกค้า
     * @return array|bool
     */
    public function getCustomerWithCreator($id) {
        $sql = "SELECT c.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . $this->table . " c 
                LEFT JOIN " . TABLE_PREFIX . "users u ON c.created_by = u.id 
                WHERE c.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลลูกค้าทั้งหมดพร้อมข้อมูลผู้สร้าง
     * 
     * @return array
     */
    public function getAllWithCreator() {
        $sql = "SELECT c.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . $this->table . " c 
                LEFT JOIN " . TABLE_PREFIX . "users u ON c.created_by = u.id 
                ORDER BY c.id";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลผู้ติดต่อ
     * 
     * @param int $id รหัสลูกค้า
     * @return array
     */
    public function getCustomerWithContacts($id) {
        // ดึงข้อมูลลูกค้า
        $customer = $this->getCustomerWithCreator($id);
        
        if (!$customer) {
            return false;
        }
        
        // ดึงข้อมูลผู้ติดต่อ
        $sql = "SELECT * FROM " . TABLE_PREFIX . "contacts WHERE customer_id = :customer_id ORDER BY is_primary DESC, id";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $contacts = $this->db->fetchAll();
        
        // รวมข้อมูล
        $customer['contacts'] = $contacts;
        
        return $customer;
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลโอกาสการขาย
     * 
     * @param int $id รหัสลูกค้า
     * @return array
     */
    public function getCustomerWithOpportunities($id) {
        // ดึงข้อมูลลูกค้า
        $customer = $this->getCustomerWithCreator($id);
        
        if (!$customer) {
            return false;
        }
        
        // ดึงข้อมูลโอกาสการขาย
        $sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name 
                FROM " . TABLE_PREFIX . "opportunities o 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.customer_id = :customer_id 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $opportunities = $this->db->fetchAll();
        
        // รวมข้อมูล
        $customer['opportunities'] = $opportunities;
        
        return $customer;
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลใบเสนอราคา
     * 
     * @param int $id รหัสลูกค้า
     * @return array
     */
    public function getCustomerWithQuotations($id) {
        // ดึงข้อมูลลูกค้า
        $customer = $this->getCustomerWithCreator($id);
        
        if (!$customer) {
            return false;
        }
        
        // ดึงข้อมูลใบเสนอราคา
        $sql = "SELECT q.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "quotations q 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.customer_id = :customer_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $quotations = $this->db->fetchAll();
        
        // รวมข้อมูล
        $customer['quotations'] = $quotations;
        
        return $customer;
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลใบแจ้งหนี้
     * 
     * @param int $id รหัสลูกค้า
     * @return array
     */
    public function getCustomerWithInvoices($id) {
        // ดึงข้อมูลลูกค้า
        $customer = $this->getCustomerWithCreator($id);
        
        if (!$customer) {
            return false;
        }
        
        // ดึงข้อมูลใบแจ้งหนี้
        $sql = "SELECT i.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "invoices i 
                LEFT JOIN " . TABLE_PREFIX . "users u ON i.user_id = u.id 
                WHERE i.customer_id = :customer_id 
                ORDER BY i.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $invoices = $this->db->fetchAll();
        
        // รวมข้อมูล
        $customer['invoices'] = $invoices;
        
        return $customer;
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลกิจกรรม
     * 
     * @param int $id รหัสลูกค้า
     * @return array
     */
    public function getCustomerWithActivities($id) {
        // ดึงข้อมูลลูกค้า
        $customer = $this->getCustomerWithCreator($id);
        
        if (!$customer) {
            return false;
        }
        
        // ดึงข้อมูลกิจกรรม
        $sql = "SELECT a.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "activities a 
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.user_id = u.id 
                WHERE a.customer_id = :customer_id 
                ORDER BY a.activity_date DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $activities = $this->db->fetchAll();
        
        // รวมข้อมูล
        $customer['activities'] = $activities;
        
        return $customer;
    }
    
    /**
     * ดึงข้อมูลลูกค้าพร้อมข้อมูลทั้งหมด
     * 
     * @param int $id รหัสลูกค้า
     * @return array
     */
    public function getCustomerWithAllDetails($id) {
        // ดึงข้อมูลลูกค้า
        $customer = $this->getCustomerWithCreator($id);
        
        if (!$customer) {
            return false;
        }
        
        // ดึงข้อมูลผู้ติดต่อ
        $sql = "SELECT * FROM " . TABLE_PREFIX . "contacts WHERE customer_id = :customer_id ORDER BY is_primary DESC, id";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $contacts = $this->db->fetchAll();
        
        // ดึงข้อมูลโอกาสการขาย
        $sql = "SELECT o.*, CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name 
                FROM " . TABLE_PREFIX . "opportunities o 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.customer_id = :customer_id 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $opportunities = $this->db->fetchAll();
        
        // ดึงข้อมูลใบเสนอราคา
        $sql = "SELECT q.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "quotations q 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.customer_id = :customer_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $quotations = $this->db->fetchAll();
        
        // ดึงข้อมูลใบแจ้งหนี้
        $sql = "SELECT i.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "invoices i 
                LEFT JOIN " . TABLE_PREFIX . "users u ON i.user_id = u.id 
                WHERE i.customer_id = :customer_id 
                ORDER BY i.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $invoices = $this->db->fetchAll();
        
        // ดึงข้อมูลกิจกรรม
        $sql = "SELECT a.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "activities a 
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.user_id = u.id 
                WHERE a.customer_id = :customer_id 
                ORDER BY a.activity_date DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $id]);
        $this->db->execute();
        
        $activities = $this->db->fetchAll();
        
        // รวมข้อมูล
        $customer['contacts'] = $contacts;
        $customer['opportunities'] = $opportunities;
        $customer['quotations'] = $quotations;
        $customer['invoices'] = $invoices;
        $customer['activities'] = $activities;
        
        return $customer;
    }
    
    /**
     * ค้นหาลูกค้า
     * 
     * @param string $keyword คำค้นหา
     * @return array
     */
    public function searchCustomers($keyword) {
        return $this->search($keyword, ['name', 'email', 'phone', 'address', 'website', 'industry']);
    }
    
    /**
     * ดึงข้อมูลลูกค้าตามประเภท
     * 
     * @param string $type ประเภทลูกค้า
     * @return array
     */
    public function getCustomersByType($type) {
        return $this->getWhere(['type' => $type]);
    }
    
    /**
     * ดึงข้อมูลลูกค้าตามสถานะ
     * 
     * @param string $status สถานะลูกค้า
     * @return array
     */
    public function getCustomersByStatus($status) {
        return $this->getWhere(['status' => $status]);
    }
    
    /**
     * ดึงข้อมูลลูกค้าตามอุตสาหกรรม
     * 
     * @param string $industry อุตสาหกรรม
     * @return array
     */
    public function getCustomersByIndustry($industry) {
        return $this->getWhere(['industry' => $industry]);
    }
    
    /**
     * ดึงข้อมูลลูกค้าตามขนาดองค์กร
     * 
     * @param string $size ขนาดองค์กร
     * @return array
     */
    public function getCustomersBySize($size) {
        return $this->getWhere(['size' => $size]);
    }
    
    /**
     * นับจำนวนลูกค้าตามประเภท
     * 
     * @return array
     */
    public function countCustomersByType() {
        $sql = "SELECT type, COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " GROUP BY type";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * นับจำนวนลูกค้าตามอุตสาหกรรม
     * 
     * @return array
     */
    public function countCustomersByIndustry() {
        $sql = "SELECT industry, COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " GROUP BY industry";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * นับจำนวนลูกค้าตามขนาดองค์กร
     * 
     * @return array
     */
    public function countCustomersBySize() {
        $sql = "SELECT size, COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " GROUP BY size";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ตรวจสอบว่าอีเมลมีอยู่แล้วหรือไม่
     * 
     * @param string $email อีเมล
     * @param int $excludeId รหัสลูกค้าที่ต้องการยกเว้น (สำหรับการอัปเดต)
     * @return bool
     */
    public function isEmailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " WHERE email = :email";
        
        if ($excludeId) {
            $sql .= " AND id != :id";
        }
        
        $this->db->prepare($sql);
        $params = [':email' => $email];
        
        if ($excludeId) {
            $params[':id'] = $excludeId;
        }
        
        $this->db->bind($params);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['count'] > 0;
    }
}

