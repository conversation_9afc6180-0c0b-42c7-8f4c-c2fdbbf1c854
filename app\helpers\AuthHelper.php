<?php
/**
 * Auth Helper
 * 
 * คลาสช่วยเหลือสำหรับการจัดการการเข้าสู่ระบบและสิทธิ์
 */

class AuthHelper {
    
    /**
     * ตรวจสอบว่าผู้ใช้เข้าสู่ระบบแล้วหรือไม่
     * 
     * @return bool
     */
    public function isLoggedIn() {
        return isset($_SESSION['user']) && !empty($_SESSION['user']);
    }
    
    /**
     * ดึงข้อมูลผู้ใช้ปัจจุบัน
     * 
     * @return array|null
     */
    public function getCurrentUser() {
        return $_SESSION['user'] ?? null;
    }
    
    /**
     * ดึง ID ของผู้ใช้ปัจจุบัน
     * 
     * @return int|null
     */
    public function getCurrentUserId() {
        $user = $this->getCurrentUser();
        return $user ? $user['id'] : null;
    }
    
    /**
     * ดึงบทบาทของผู้ใช้ปัจจุบัน
     * 
     * @return string|null
     */
    public function getCurrentUserRole() {
        $user = $this->getCurrentUser();
        return $user ? $user['role_id'] : null;
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้มีบทบาทที่กำหนดหรือไม่
     * 
     * @param string $role บทบาทที่ต้องการตรวจสอบ
     * @return bool
     */
    public function hasRole($role) {
        $currentRole = $this->getCurrentUserRole();
        return $currentRole === $role;
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้เป็น Admin หรือไม่
     * 
     * @return bool
     */
    public function isAdmin() {
        return $this->hasRole(ROLE_ADMIN);
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้เป็น Manager หรือไม่
     * 
     * @return bool
     */
    public function isManager() {
        return $this->hasRole(ROLE_MANAGER);
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้เป็น Sales หรือไม่
     * 
     * @return bool
     */
    public function isSales() {
        return $this->hasRole(ROLE_SALES);
    }
    
    /**
     * ตรวจสอบสิทธิ์การเข้าถึง
     * 
     * @param string $role บทบาทของผู้ใช้
     * @param string $controller คอนโทรลเลอร์
     * @param string $action การกระทำ
     * @return bool
     */
    public function hasPermission($role, $controller, $action = null) {
        // กำหนดสิทธิ์การเข้าถึงตามบทบาท
        $permissions = $this->getPermissions();
        
        // ตรวจสอบสิทธิ์ตามบทบาท
        if (!isset($permissions[$role])) {
            return false;
        }
        
        $rolePermissions = $permissions[$role];
        
        // ถ้าเป็น admin ให้เข้าถึงได้ทุกอย่าง
        if ($role === ROLE_ADMIN) {
            return true;
        }
        
        // ตรวจสอบสิทธิ์ตามคอนโทรลเลอร์
        if (!isset($rolePermissions[$controller])) {
            return false;
        }
        
        $controllerPermissions = $rolePermissions[$controller];
        
        // ถ้าเป็น true หมายถึงเข้าถึงได้ทุก action
        if ($controllerPermissions === true) {
            return true;
        }
        
        // ถ้าเป็น array ให้ตรวจสอบ action
        if (is_array($controllerPermissions)) {
            if ($action === null) {
                return !empty($controllerPermissions);
            }
            return in_array($action, $controllerPermissions);
        }
        
        return false;
    }
    
    /**
     * กำหนดสิทธิ์การเข้าถึงตามบทบาท
     * 
     * @return array
     */
    private function getPermissions() {
        return [
            ROLE_ADMIN => [
                // Admin เข้าถึงได้ทุกอย่าง
                'dashboard' => true,
                'customer' => true,
                'contact' => true,
                'opportunity' => true,
                'activity' => true,
                'product' => true,
                'quotation' => true,
                'invoice' => true,
                'payment' => true,
                'salesTarget' => true,
                'report' => true,
                'setting' => true,
                'user' => true,
                'notification' => true
            ],
            
            ROLE_MANAGER => [
                'dashboard' => true,
                'customer' => true,
                'contact' => true,
                'opportunity' => true,
                'activity' => true,
                'product' => ['index', 'view', 'create', 'edit'],
                'quotation' => true,
                'invoice' => true,
                'payment' => true,
                'salesTarget' => true,
                'report' => true,
                'setting' => ['index', 'view'],
                'user' => ['index', 'view', 'edit'],
                'notification' => true
            ],
            
            ROLE_SALES => [
                'dashboard' => true,
                'customer' => true,
                'contact' => true,
                'opportunity' => true,
                'activity' => true,
                'product' => ['index', 'view'],
                'quotation' => true,
                'invoice' => ['index', 'view'],
                'payment' => ['index', 'view'],
                'salesTarget' => ['index', 'view'],
                'report' => ['index', 'view'],
                'setting' => ['index', 'view'],
                'user' => ['view', 'edit'], // เฉพาะข้อมูลตัวเอง
                'notification' => true
            ],
            
            ROLE_USER => [
                'dashboard' => true,
                'customer' => ['index', 'view'],
                'contact' => ['index', 'view'],
                'opportunity' => ['index', 'view'],
                'activity' => ['index', 'view'],
                'product' => ['index', 'view'],
                'quotation' => ['index', 'view'],
                'invoice' => ['index', 'view'],
                'payment' => ['index', 'view'],
                'salesTarget' => ['index', 'view'],
                'report' => ['index', 'view'],
                'setting' => ['index', 'view'],
                'user' => ['view', 'edit'], // เฉพาะข้อมูลตัวเอง
                'notification' => ['index', 'view']
            ]
        ];
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้สามารถเข้าถึงข้อมูลของผู้ใช้อื่นได้หรือไม่
     * 
     * @param int $targetUserId ID ของผู้ใช้เป้าหมาย
     * @return bool
     */
    public function canAccessUser($targetUserId) {
        $currentUser = $this->getCurrentUser();
        
        if (!$currentUser) {
            return false;
        }
        
        // Admin และ Manager เข้าถึงได้ทุกคน
        if (in_array($currentUser['role_id'], [ROLE_ADMIN, ROLE_MANAGER])) {
            return true;
        }
        
        // ผู้ใช้อื่นเข้าถึงได้เฉพาะข้อมูลตัวเอง
        return $currentUser['id'] == $targetUserId;
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้สามารถเข้าถึงข้อมูลลูกค้าได้หรือไม่
     * 
     * @param int $customerId ID ของลูกค้า
     * @return bool
     */
    public function canAccessCustomer($customerId) {
        $currentUser = $this->getCurrentUser();
        
        if (!$currentUser) {
            return false;
        }
        
        // Admin และ Manager เข้าถึงได้ทุกลูกค้า
        if (in_array($currentUser['role_id'], [ROLE_ADMIN, ROLE_MANAGER])) {
            return true;
        }
        
        // Sales และ User ตรวจสอบว่าเป็นลูกค้าที่ตัวเองดูแลหรือไม่
        $customerModel = new Customer();
        $customer = $customerModel->getById($customerId);
        
        if (!$customer) {
            return false;
        }
        
        return $customer['assigned_to'] == $currentUser['id'];
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้สามารถแก้ไขข้อมูลได้หรือไม่
     * 
     * @param string $controller คอนโทรลเลอร์
     * @return bool
     */
    public function canEdit($controller) {
        $role = $this->getCurrentUserRole();
        return $this->hasPermission($role, $controller, 'edit');
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้สามารถลบข้อมูลได้หรือไม่
     * 
     * @param string $controller คอนโทรลเลอร์
     * @return bool
     */
    public function canDelete($controller) {
        $role = $this->getCurrentUserRole();
        return $this->hasPermission($role, $controller, 'delete');
    }
    
    /**
     * ตรวจสอบว่าผู้ใช้สามารถสร้างข้อมูลใหม่ได้หรือไม่
     * 
     * @param string $controller คอนโทรลเลอร์
     * @return bool
     */
    public function canCreate($controller) {
        $role = $this->getCurrentUserRole();
        return $this->hasPermission($role, $controller, 'create');
    }
    
    /**
     * ตรวจสอบ session timeout
     * 
     * @return bool
     */
    public function isSessionValid() {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $loginTime = $_SESSION['login_time'] ?? 0;
        $currentTime = time();
        
        // ตรวจสอบว่า session หมดอายุหรือไม่
        if (($currentTime - $loginTime) > SESSION_LIFETIME) {
            $this->logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * ออกจากระบบ
     */
    public function logout() {
        session_destroy();
        
        // ลบ remember token cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
    }
    
    /**
     * ตรวจสอบ Remember Me token
     * 
     * @return bool
     */
    public function checkRememberToken() {
        if ($this->isLoggedIn()) {
            return true;
        }
        
        if (!isset($_COOKIE['remember_token'])) {
            return false;
        }
        
        $token = $_COOKIE['remember_token'];
        $userModel = new User();
        $user = $userModel->getUserByRememberToken($token);
        
        if (!$user) {
            // ลบ cookie ที่ไม่ถูกต้อง
            setcookie('remember_token', '', time() - 3600, '/');
            return false;
        }
        
        // ตรวจสอบว่า token หมดอายุหรือไม่
        if (strtotime($user['remember_token_expires']) < time()) {
            $userModel->clearRememberToken($user['id']);
            setcookie('remember_token', '', time() - 3600, '/');
            return false;
        }
        
        // สร้าง session ใหม่
        unset($user['password']);
        unset($user['remember_token']);
        unset($user['remember_token_expires']);
        
        $_SESSION['user'] = $user;
        $_SESSION['login_time'] = time();
        
        return true;
    }
}
