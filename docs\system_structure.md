# เอกสารอธิบายโครงสร้างของระบบติดตามงานขายและการเสนอราคา

## สารบัญ
1. [ภาพรวมของระบบ](#ภาพรวมของระบบ)
2. [โครงสร้างไฟล์และโฟลเดอร์](#โครงสร้างไฟล์และโฟลเดอร์)
3. [โครงสร้างฐานข้อมูล](#โครงสร้างฐานข้อมูล)
4. [สถาปัตยกรรมของระบบ](#สถาปัตยกรรมของระบบ)
5. [โมดูลและฟังก์ชันหลัก](#โมดูลและฟังก์ชันหลัก)
6. [การรักษาความปลอดภัย](#การรักษาความปลอดภัย)
7. [การเชื่อมต่อกับระบบภายนอก](#การเชื่อมต่อกับระบบภายนอก)
8. [การปรับแต่งและพัฒนาต่อ](#การปรับแต่งและพัฒนาต่อ)

## ภาพรวมของระบบ

ระบบติดตามงานขายและการเสนอราคาเป็นระบบที่พัฒนาขึ้นโดยใช้ PHP, JavaScript และ Bootstrap เพื่อช่วยในการบริหารจัดการงานขาย การติดตามโอกาสการขาย และการจัดทำใบเสนอราคา ระบบนี้ใช้สถาปัตยกรรมแบบ MVC (Model-View-Controller) เพื่อแยกส่วนการทำงานและทำให้การพัฒนาและบำรุงรักษาระบบทำได้ง่ายขึ้น

### เทคโนโลยีที่ใช้
- **ภาษาหลัก**: PHP 7.4+
- **ฐานข้อมูล**: MySQL 5.7+ / MariaDB 10.3+
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Libraries**: jQuery, DataTables, Chart.js, Select2, Flatpickr, SweetAlert2
- **เทคนิค**: AJAX, JSON, Responsive Design

### ฟีเจอร์หลัก
- การจัดการผู้ใช้งาน
- การจัดการลูกค้า
- การกำหนดเป้าหมายการขาย
- การจัดการโอกาสการขาย
- การจัดทำใบเสนอราคา
- การติดตามสถานะการขาย
- การติดตามการชำระเงิน
- รายงานและการวิเคราะห์

## โครงสร้างไฟล์และโฟลเดอร์

```
sales_tracking_system/
├── app/                    # โค้ดหลักของแอปพลิเคชัน
│   ├── config/             # ไฟล์การตั้งค่า
│   │   ├── config.php      # การตั้งค่าทั่วไป
│   │   ├── database.php    # การตั้งค่าฐานข้อมูล
│   │   └── constants.php   # ค่าคงที่
│   ├── controllers/        # Controllers
│   │   ├── AuthController.php
│   │   ├── CustomerController.php
│   │   ├── DashboardController.php
│   │   ├── OpportunityController.php
│   │   ├── PaymentController.php
│   │   ├── QuotationController.php
│   │   ├── ReportController.php
│   │   ├── SettingController.php
│   │   └── UserController.php
│   ├── models/             # Models
│   │   ├── BaseModel.php
│   │   ├── Customer.php
│   │   ├── Database.php
│   │   ├── Opportunity.php
│   │   ├── Payment.php
│   │   ├── Quotation.php
│   │   ├── Report.php
│   │   ├── Setting.php
│   │   └── User.php
│   ├── helpers/            # Helper functions
│   │   ├── auth_helper.php
│   │   ├── date_helper.php
│   │   ├── file_helper.php
│   │   ├── form_helper.php
│   │   └── string_helper.php
│   └── views/              # Views (templates)
│       ├── auth/
│       ├── customers/
│       ├── dashboard/
│       ├── opportunities/
│       ├── payments/
│       ├── quotations/
│       ├── reports/
│       ├── settings/
│       ├── users/
│       ├── layouts/
│       └── partials/
├── assets/                 # Frontend assets
│   ├── css/
│   │   ├── style.css       # Custom CSS
│   │   └── vendor/         # Third-party CSS
│   ├── js/
│   │   ├── main.js         # Custom JavaScript
│   │   └── vendor/         # Third-party JavaScript
│   ├── images/
│   │   ├── logo.png
│   │   └── icons/
│   └── fonts/
├── uploads/                # Uploaded files
│   ├── customers/
│   ├── opportunities/
│   ├── profiles/
│   └── quotations/
├── database/               # Database scripts
│   ├── sales_tracking_db.sql
│   └── migrations/
├── docs/                   # Documentation
│   ├── user_manual.md
│   ├── installation_guide.md
│   └── system_structure.md
├── logs/                   # Log files
├── vendor/                 # Third-party libraries
├── .htaccess               # Apache configuration
├── index.php               # Entry point
├── dashboard.php           # Dashboard page
└── README.md               # Project README
```

## โครงสร้างฐานข้อมูล

ระบบใช้ฐานข้อมูล MySQL/MariaDB โดยมีตารางหลักดังนี้:

### ตาราง users
เก็บข้อมูลผู้ใช้งานระบบ

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| username | VARCHAR(50) | ชื่อผู้ใช้ |
| password | VARCHAR(255) | รหัสผ่าน (เข้ารหัสด้วย bcrypt) |
| email | VARCHAR(100) | อีเมล |
| first_name | VARCHAR(50) | ชื่อ |
| last_name | VARCHAR(50) | นามสกุล |
| role | ENUM | บทบาท (admin, manager, sales) |
| profile_image | VARCHAR(255) | ที่อยู่ไฟล์รูปโปรไฟล์ |
| status | TINYINT | สถานะ (1=active, 0=inactive) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |
| last_login | DATETIME | วันที่เข้าสู่ระบบล่าสุด |

### ตาราง customers
เก็บข้อมูลลูกค้า

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| company_name | VARCHAR(100) | ชื่อบริษัท |
| business_type | VARCHAR(50) | ประเภทธุรกิจ |
| address | TEXT | ที่อยู่ |
| city | VARCHAR(50) | เมือง |
| state | VARCHAR(50) | รัฐ/จังหวัด |
| postal_code | VARCHAR(20) | รหัสไปรษณีย์ |
| country | VARCHAR(50) | ประเทศ |
| phone | VARCHAR(20) | เบอร์โทรศัพท์ |
| email | VARCHAR(100) | อีเมล |
| website | VARCHAR(100) | เว็บไซต์ |
| tax_id | VARCHAR(20) | เลขประจำตัวผู้เสียภาษี |
| notes | TEXT | หมายเหตุ |
| created_by | INT | ผู้สร้าง (Foreign Key -> users.id) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง contacts
เก็บข้อมูลผู้ติดต่อของลูกค้า

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| customer_id | INT | Foreign Key -> customers.id |
| first_name | VARCHAR(50) | ชื่อ |
| last_name | VARCHAR(50) | นามสกุล |
| position | VARCHAR(50) | ตำแหน่ง |
| department | VARCHAR(50) | แผนก |
| phone | VARCHAR(20) | เบอร์โทรศัพท์ |
| mobile | VARCHAR(20) | เบอร์มือถือ |
| email | VARCHAR(100) | อีเมล |
| is_primary | TINYINT | ผู้ติดต่อหลัก (1=yes, 0=no) |
| notes | TEXT | หมายเหตุ |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง sales_targets
เก็บข้อมูลเป้าหมายการขาย

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| user_id | INT | Foreign Key -> users.id |
| target_type | ENUM | ประเภทเป้าหมาย (individual, team) |
| period_type | ENUM | ประเภทช่วงเวลา (monthly, quarterly, yearly) |
| period_year | INT | ปี |
| period_month | INT | เดือน (1-12) |
| period_quarter | INT | ไตรมาส (1-4) |
| sales_amount | DECIMAL | เป้าหมายยอดขาย |
| new_customers | INT | เป้าหมายลูกค้าใหม่ |
| quotations | INT | เป้าหมายใบเสนอราคา |
| created_by | INT | ผู้สร้าง (Foreign Key -> users.id) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง opportunities
เก็บข้อมูลโอกาสการขาย

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| name | VARCHAR(100) | ชื่อโอกาสการขาย |
| customer_id | INT | Foreign Key -> customers.id |
| contact_id | INT | Foreign Key -> contacts.id |
| expected_value | DECIMAL | มูลค่าคาดการณ์ |
| probability | INT | ความน่าจะเป็น (%) |
| expected_close_date | DATE | วันที่คาดว่าจะปิดการขาย |
| status | ENUM | สถานะ (lead, qualified, proposal, negotiation, closed_won, closed_lost) |
| source | VARCHAR(50) | แหล่งที่มา |
| description | TEXT | รายละเอียด |
| assigned_to | INT | ผู้รับผิดชอบ (Foreign Key -> users.id) |
| created_by | INT | ผู้สร้าง (Foreign Key -> users.id) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง activities
เก็บข้อมูลกิจกรรมที่เกี่ยวข้องกับโอกาสการขาย

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| opportunity_id | INT | Foreign Key -> opportunities.id |
| type | ENUM | ประเภทกิจกรรม (call, meeting, email, task, note) |
| subject | VARCHAR(100) | หัวข้อ |
| description | TEXT | รายละเอียด |
| activity_date | DATETIME | วันที่กิจกรรม |
| status | ENUM | สถานะ (planned, completed, cancelled) |
| created_by | INT | ผู้สร้าง (Foreign Key -> users.id) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง quotations
เก็บข้อมูลใบเสนอราคา

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| quotation_number | VARCHAR(20) | เลขที่ใบเสนอราคา |
| customer_id | INT | Foreign Key -> customers.id |
| contact_id | INT | Foreign Key -> contacts.id |
| opportunity_id | INT | Foreign Key -> opportunities.id (nullable) |
| issue_date | DATE | วันที่ออกใบเสนอราคา |
| expiry_date | DATE | วันที่หมดอายุ |
| subject | VARCHAR(100) | หัวข้อ |
| introduction | TEXT | ข้อความแนะนำ |
| subtotal | DECIMAL | ยอดรวมก่อนภาษี |
| tax_rate | DECIMAL | อัตราภาษี (%) |
| tax_amount | DECIMAL | จำนวนภาษี |
| discount_type | ENUM | ประเภทส่วนลด (percentage, amount) |
| discount_value | DECIMAL | มูลค่าส่วนลด |
| total | DECIMAL | ยอดรวมทั้งสิ้น |
| terms | TEXT | เงื่อนไขการชำระเงิน |
| notes | TEXT | หมายเหตุ |
| status | ENUM | สถานะ (draft, sent, accepted, rejected, expired) |
| created_by | INT | ผู้สร้าง (Foreign Key -> users.id) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง quotation_items
เก็บข้อมูลรายการสินค้า/บริการในใบเสนอราคา

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| quotation_id | INT | Foreign Key -> quotations.id |
| item_name | VARCHAR(100) | ชื่อสินค้า/บริการ |
| description | TEXT | รายละเอียด |
| quantity | DECIMAL | จำนวน |
| unit | VARCHAR(20) | หน่วย |
| unit_price | DECIMAL | ราคาต่อหน่วย |
| discount_type | ENUM | ประเภทส่วนลด (percentage, amount) |
| discount_value | DECIMAL | มูลค่าส่วนลด |
| tax_rate | DECIMAL | อัตราภาษี (%) |
| total | DECIMAL | ยอดรวม |
| sort_order | INT | ลำดับการแสดงผล |

### ตาราง payments
เก็บข้อมูลการชำระเงิน

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| quotation_id | INT | Foreign Key -> quotations.id |
| payment_date | DATE | วันที่ชำระเงิน |
| amount | DECIMAL | จำนวนเงิน |
| payment_method | ENUM | วิธีการชำระเงิน (cash, bank_transfer, credit_card, cheque) |
| reference_number | VARCHAR(50) | หมายเลขอ้างอิง |
| notes | TEXT | หมายเหตุ |
| created_by | INT | ผู้บันทึก (Foreign Key -> users.id) |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

### ตาราง settings
เก็บการตั้งค่าระบบ

| Field | Type | Description |
|-------|------|-------------|
| id | INT | Primary Key, Auto Increment |
| setting_key | VARCHAR(50) | คีย์การตั้งค่า |
| setting_value | TEXT | ค่าการตั้งค่า |
| setting_group | VARCHAR(50) | กลุ่มการตั้งค่า |
| created_at | DATETIME | วันที่สร้าง |
| updated_at | DATETIME | วันที่อัปเดตล่าสุด |

## สถาปัตยกรรมของระบบ

ระบบใช้สถาปัตยกรรมแบบ MVC (Model-View-Controller) ซึ่งแบ่งการทำงานออกเป็น 3 ส่วนหลัก:

### Model
- รับผิดชอบในการจัดการข้อมูลและการติดต่อกับฐานข้อมูล
- ประกอบด้วยคลาสต่างๆ ที่เกี่ยวข้องกับข้อมูล เช่น User, Customer, Opportunity, Quotation
- มีการใช้ BaseModel เป็นคลาสพื้นฐานที่มีฟังก์ชันการทำงานทั่วไป เช่น การสร้าง อ่าน อัปเดต และลบข้อมูล (CRUD)

### View
- รับผิดชอบในการแสดงผลข้อมูลให้กับผู้ใช้
- ประกอบด้วยไฟล์ PHP ที่มี HTML, CSS และ JavaScript
- มีการใช้ layouts และ partials เพื่อลดการเขียนโค้ดซ้ำซ้อน
- ใช้ Bootstrap เป็นเฟรมเวิร์คหลักในการพัฒนาส่วนติดต่อผู้ใช้

### Controller
- รับผิดชอบในการรับคำขอจากผู้ใช้ ประมวลผล และส่งผลลัพธ์กลับไปยัง View
- ประกอบด้วยคลาสต่างๆ ที่จัดการกับการทำงานของระบบ เช่น AuthController, CustomerController, QuotationController
- มีการใช้ Routing เพื่อกำหนดว่าคำขอใดจะถูกส่งไปยัง Controller และ Method ใด

### การทำงานของระบบ
1. ผู้ใช้ส่งคำขอผ่านเว็บเบราว์เซอร์
2. คำขอถูกส่งไปยัง index.php ซึ่งเป็น Entry Point ของระบบ
3. Routing System วิเคราะห์ URL และกำหนด Controller และ Method ที่จะใช้
4. Controller เรียกใช้ Model เพื่อดึงหรือบันทึกข้อมูล
5. Controller ประมวลผลข้อมูลและส่งไปยัง View
6. View แสดงผลข้อมูลให้กับผู้ใช้

## โมดูลและฟังก์ชันหลัก

### โมดูลการจัดการผู้ใช้งาน
- การลงทะเบียนผู้ใช้งานใหม่
- การเข้าสู่ระบบและออกจากระบบ
- การจัดการสิทธิ์และบทบาท
- การแก้ไขข้อมูลส่วนตัว
- การเปลี่ยนรหัสผ่าน
- การกู้คืนรหัสผ่าน

### โมดูลการจัดการลูกค้า
- การเพิ่ม แก้ไข และลบข้อมูลลูกค้า
- การจัดการผู้ติดต่อของลูกค้า
- การค้นหาและกรองข้อมูลลูกค้า
- การนำเข้าและส่งออกข้อมูลลูกค้า
- การดูประวัติการติดต่อและการซื้อขาย

### โมดูลการจัดการเป้าหมายการขาย
- การกำหนดเป้าหมายการขายรายบุคคลและทีม
- การติดตามความคืบหน้าเทียบกับเป้าหมาย
- การวิเคราะห์ผลการดำเนินงาน
- การแจ้งเตือนเมื่อใกล้ถึงเป้าหมายหรือไม่บรรลุเป้าหมาย

### โมดูลการจัดการโอกาสการขาย
- การเพิ่ม แก้ไข และลบโอกาสการขาย
- การติดตามสถานะของโอกาสการขาย
- การบันทึกกิจกรรมที่เกี่ยวข้อง
- การวิเคราะห์อัตราความสำเร็จ
- การแสดง Sales Pipeline

### โมดูลการจัดการใบเสนอราคา
- การสร้างและแก้ไขใบเสนอราคา
- การเพิ่มรายการสินค้า/บริการ
- การคำนวณราคา ส่วนลด และภาษี
- การส่งใบเสนอราคาทางอีเมล
- การติดตามสถานะใบเสนอราคา
- การแปลงใบเสนอราคาเป็น PDF

### โมดูลการติดตามการชำระเงิน
- การบันทึกการชำระเงิน
- การติดตามสถานะการชำระเงิน
- การแจ้งเตือนเมื่อใกล้ถึงกำหนดชำระหรือเลยกำหนดชำระ
- การออกใบเสร็จรับเงิน

### โมดูลรายงานและการวิเคราะห์
- รายงานยอดขาย
- รายงานโอกาสการขาย
- รายงานใบเสนอราคา
- รายงานการชำระเงิน
- รายงานผลงานพนักงานขาย
- รายงานความคืบหน้าเป้าหมาย
- การส่งออกรายงานในรูปแบบต่างๆ

## การรักษาความปลอดภัย

### การป้องกันการโจมตี
- **SQL Injection**: ใช้ Prepared Statements ในการติดต่อกับฐานข้อมูล
- **Cross-Site Scripting (XSS)**: มีการกรองข้อมูลที่รับเข้ามาและแสดงผล
- **Cross-Site Request Forgery (CSRF)**: ใช้ CSRF Token ในฟอร์มทั้งหมด
- **Session Hijacking**: ใช้ Secure และ HttpOnly Cookies, มีการตรวจสอบ IP Address
- **Brute Force Attacks**: มีการจำกัดจำนวนครั้งในการเข้าสู่ระบบที่ไม่สำเร็จ

### การเข้ารหัสข้อมูล
- รหัสผ่านถูกเข้ารหัสด้วย bcrypt
- การเชื่อมต่อกับระบบใช้ HTTPS
- ข้อมูลที่สำคัญถูกเข้ารหัสในฐานข้อมูล

### การจัดการสิทธิ์
- มีการกำหนดบทบาทและสิทธิ์ที่แตกต่างกัน (Admin, Manager, Sales)
- มีการตรวจสอบสิทธิ์ก่อนการเข้าถึงหน้าและฟังก์ชันต่างๆ
- มีการบันทึกประวัติการใช้งานระบบ (Audit Log)

## การเชื่อมต่อกับระบบภายนอก

ระบบสามารถเชื่อมต่อกับระบบภายนอกผ่าน API ดังนี้:

### การส่งอีเมล
- ใช้ SMTP Server ในการส่งอีเมล
- รองรับการส่งอีเมลแบบ HTML
- รองรับการแนบไฟล์

### การชำระเงิน
- รองรับการเชื่อมต่อกับระบบชำระเงินออนไลน์ (เช่น PayPal, Stripe)
- รองรับการชำระเงินผ่านบัตรเครดิต

### การนำเข้า/ส่งออกข้อมูล
- รองรับการนำเข้าข้อมูลจากไฟล์ CSV, Excel
- รองรับการส่งออกข้อมูลเป็นไฟล์ CSV, Excel, PDF

### API สำหรับระบบภายนอก
- มี RESTful API สำหรับให้ระบบภายนอกเชื่อมต่อ
- มีการรักษาความปลอดภัยด้วย API Key และ OAuth 2.0

## การปรับแต่งและพัฒนาต่อ

### การปรับแต่งระบบ
- สามารถปรับแต่งรูปแบบเอกสาร (ใบเสนอราคา, ใบแจ้งหนี้, ใบเสร็จรับเงิน)
- สามารถปรับแต่งฟิลด์ข้อมูลเพิ่มเติม
- สามารถปรับแต่งรูปแบบรายงาน
- สามารถปรับแต่งธีมและสีของระบบ

### การพัฒนาต่อ
- ใช้ระบบ Hooks เพื่อให้สามารถเพิ่มฟังก์ชันการทำงานได้โดยไม่ต้องแก้ไขโค้ดหลัก
- มีเอกสาร API สำหรับนักพัฒนา
- มีโครงสร้างโค้ดที่เป็นระเบียบและมีความยืดหยุ่น
- มีการใช้ Dependency Injection เพื่อให้สามารถเปลี่ยนแปลงการทำงานของระบบได้ง่าย

### แนวทางการพัฒนาในอนาคต
- เพิ่มฟีเจอร์การจัดการสินค้าและบริการ
- เพิ่มฟีเจอร์การจัดการคลังสินค้า
- เพิ่มฟีเจอร์การจัดการโครงการ
- เพิ่มฟีเจอร์การจัดการทรัพยากรบุคคล
- พัฒนาแอปพลิเคชันมือถือ
- เพิ่มการวิเคราะห์ข้อมูลขั้นสูงด้วย AI

