<?php
/**
 * Quotation Model
 * 
 * คลาสสำหรับจัดการข้อมูลใบเสนอราคา
 */

class Quotation extends BaseModel {
    protected $table = 'quotations';
    protected $fillable = [
        'quotation_number', 'customer_id', 'contact_id', 'opportunity_id', 
        'subject', 'issue_date', 'valid_until', 'subtotal', 'discount_type', 
        'discount_value', 'tax', 'total', 'notes', 'terms', 'status', 
        'user_id', 'created_at', 'updated_at'
    ];
    
    /**
     * คอนสตรักเตอร์
     */
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * สร้างใบเสนอราคาใหม่
     * 
     * @param array $data ข้อมูลใบเสนอราคา
     * @return int|bool
     */
    public function create($data) {
        // สร้างเลขที่ใบเสนอราคา
        if (!isset($data['quotation_number']) || empty($data['quotation_number'])) {
            $data['quotation_number'] = $this->generateQuotationNumber();
        }
        
        // เพิ่มวันที่สร้างและอัปเดต
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // เริ่ม Transaction
        $this->db->beginTransaction();
        
        try {
            // สร้างใบเสนอราคา
            $quotationId = parent::create($data);
            
            if (!$quotationId) {
                throw new Exception("Failed to create quotation");
            }
            
            // ถ้ามีรายการสินค้า ให้บันทึกรายการสินค้า
            if (isset($data['items']) && is_array($data['items']) && !empty($data['items'])) {
                $this->saveQuotationItems($quotationId, $data['items']);
            }
            
            // ยืนยัน Transaction
            $this->db->commit();
            
            return $quotationId;
        } catch (Exception $e) {
            // ยกเลิก Transaction
            $this->db->rollBack();
            
            return false;
        }
    }
    
    /**
     * อัปเดตใบเสนอราคา
     * 
     * @param int $id รหัสใบเสนอราคา
     * @param array $data ข้อมูลที่ต้องการอัปเดต
     * @return bool
     */
    public function update($id, $data) {
        // อัปเดตวันที่แก้ไข
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        // เริ่ม Transaction
        $this->db->beginTransaction();
        
        try {
            // อัปเดตใบเสนอราคา
            $result = parent::update($id, $data);
            
            if (!$result) {
                throw new Exception("Failed to update quotation");
            }
            
            // ถ้ามีรายการสินค้า ให้อัปเดตรายการสินค้า
            if (isset($data['items']) && is_array($data['items']) && !empty($data['items'])) {
                // ลบรายการสินค้าเดิม
                $this->deleteQuotationItems($id);
                
                // บันทึกรายการสินค้าใหม่
                $this->saveQuotationItems($id, $data['items']);
            }
            
            // ยืนยัน Transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // ยกเลิก Transaction
            $this->db->rollBack();
            
            return false;
        }
    }
    
    /**
     * อัปเดตสถานะใบเสนอราคา
     * 
     * @param int $id รหัสใบเสนอราคา
     * @param string $status สถานะใบเสนอราคา
     * @return bool
     */
    public function updateStatus($id, $status) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        return parent::update($id, $data);
    }
    
    /**
     * ลบใบเสนอราคา
     * 
     * @param int $id รหัสใบเสนอราคา
     * @return bool
     */
    public function delete($id) {
        // เริ่ม Transaction
        $this->db->beginTransaction();
        
        try {
            // ลบรายการสินค้า
            $this->deleteQuotationItems($id);
            
            // ลบใบเสนอราคา
            $result = parent::delete($id);
            
            if (!$result) {
                throw new Exception("Failed to delete quotation");
            }
            
            // ยืนยัน Transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // ยกเลิก Transaction
            $this->db->rollBack();
            
            return false;
        }
    }
    
    /**
     * บันทึกรายการสินค้าในใบเสนอราคา
     * 
     * @param int $quotationId รหัสใบเสนอราคา
     * @param array $items รายการสินค้า
     * @return bool
     */
    private function saveQuotationItems($quotationId, $items) {
        $sql = "INSERT INTO " . TABLE_PREFIX . "quotation_items (quotation_id, product_id, description, quantity, unit_price, tax_rate, total) VALUES (:quotation_id, :product_id, :description, :quantity, :unit_price, :tax_rate, :total)";
        
        foreach ($items as $item) {
            $this->db->prepare($sql);
            $this->db->bind([
                ':quotation_id' => $quotationId,
                ':product_id' => isset($item['product_id']) ? $item['product_id'] : null,
                ':description' => $item['description'],
                ':quantity' => $item['quantity'],
                ':unit_price' => $item['unit_price'],
                ':tax_rate' => isset($item['tax_rate']) ? $item['tax_rate'] : 0,
                ':total' => $item['total']
            ]);
            
            $result = $this->db->execute();
            
            if (!$result) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * ลบรายการสินค้าในใบเสนอราคา
     * 
     * @param int $quotationId รหัสใบเสนอราคา
     * @return bool
     */
    private function deleteQuotationItems($quotationId) {
        $sql = "DELETE FROM " . TABLE_PREFIX . "quotation_items WHERE quotation_id = :quotation_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':quotation_id' => $quotationId]);
        
        return $this->db->execute();
    }
    
    /**
     * สร้างเลขที่ใบเสนอราคา
     * 
     * @return string
     */
    private function generateQuotationNumber() {
        $prefix = QUOTATION_PREFIX;
        $year = date('Y');
        $month = date('m');
        
        // หาเลขที่ใบเสนอราคาล่าสุด
        $sql = "SELECT MAX(CAST(SUBSTRING(quotation_number, " . (strlen($prefix) + 1) . ") AS UNSIGNED)) as last_number 
                FROM " . TABLE_PREFIX . $this->table . " 
                WHERE quotation_number LIKE :prefix";
        
        $this->db->prepare($sql);
        $this->db->bind([':prefix' => $prefix . $year . $month . '%']);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        $lastNumber = $result['last_number'] ? $result['last_number'] : 0;
        $nextNumber = $lastNumber + 1;
        
        return $prefix . $year . $month . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param int $id รหัสใบเสนอราคา
     * @return array|bool
     */
    public function getQuotationWithDetails($id) {
        $sql = "SELECT q.*, 
                c.name as customer_name, c.address as customer_address, c.phone as customer_phone, c.email as customer_email,
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                o.name as opportunity_name,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON q.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON q.opportunity_id = o.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        $this->db->execute();
        
        $quotation = $this->db->fetch();
        
        if (!$quotation) {
            return false;
        }
        
        // ดึงรายการสินค้า
        $sql = "SELECT qi.*, p.name as product_name 
                FROM " . TABLE_PREFIX . "quotation_items qi 
                LEFT JOIN " . TABLE_PREFIX . "products p ON qi.product_id = p.id 
                WHERE qi.quotation_id = :quotation_id 
                ORDER BY qi.id";
        
        $this->db->prepare($sql);
        $this->db->bind([':quotation_id' => $id]);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // รวมข้อมูล
        $quotation['items'] = $items;
        
        return $quotation;
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาทั้งหมดพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @return array
     */
    public function getAllWithDetails() {
        $sql = "SELECT q.*, 
                c.name as customer_name,
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                o.name as opportunity_name,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON q.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON q.opportunity_id = o.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาตามลูกค้า
     * 
     * @param int $customerId รหัสลูกค้า
     * @return array
     */
    public function getQuotationsByCustomer($customerId) {
        $sql = "SELECT q.*, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                o.name as opportunity_name,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON q.opportunity_id = o.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.customer_id = :customer_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $customerId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาตามโอกาสการขาย
     * 
     * @param int $opportunityId รหัสโอกาสการขาย
     * @return array
     */
    public function getQuotationsByOpportunity($opportunityId) {
        $sql = "SELECT q.*, 
                c.name as customer_name,
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON q.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.opportunity_id = :opportunity_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':opportunity_id' => $opportunityId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาตามผู้สร้าง
     * 
     * @param int $userId รหัสผู้ใช้
     * @return array
     */
    public function getQuotationsByUser($userId) {
        $sql = "SELECT q.*, 
                c.name as customer_name,
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                o.name as opportunity_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON q.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON q.opportunity_id = o.id 
                WHERE q.user_id = :user_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':user_id' => $userId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาตามสถานะ
     * 
     * @param string $status สถานะใบเสนอราคา
     * @return array
     */
    public function getQuotationsByStatus($status) {
        $sql = "SELECT q.*, 
                c.name as customer_name,
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                o.name as opportunity_name,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON q.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON q.opportunity_id = o.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.status = :status 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':status' => $status]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลใบเสนอราคาตามช่วงเวลา
     * 
     * @param string $startDate วันที่เริ่มต้น
     * @param string $endDate วันที่สิ้นสุด
     * @return array
     */
    public function getQuotationsByDateRange($startDate, $endDate) {
        $sql = "SELECT q.*, 
                c.name as customer_name,
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                o.name as opportunity_name,
                CONCAT(u.first_name, ' ', u.last_name) as created_by_name
                FROM " . TABLE_PREFIX . $this->table . " q 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON q.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON q.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON q.opportunity_id = o.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.issue_date BETWEEN :start_date AND :end_date 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * นับจำนวนใบเสนอราคาตามสถานะ
     * 
     * @return array
     */
    public function countQuotationsByStatus() {
        $sql = "SELECT status, COUNT(*) as count, SUM(total) as total_amount 
                FROM " . TABLE_PREFIX . $this->table . " 
                GROUP BY status";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * คำนวณมูลค่าใบเสนอราคาทั้งหมด
     * 
     * @return float
     */
    public function calculateTotalQuotationValue() {
        $sql = "SELECT SUM(total) as total_amount FROM " . TABLE_PREFIX . $this->table;
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าใบเสนอราคาตามสถานะ
     * 
     * @param string $status สถานะใบเสนอราคา
     * @return float
     */
    public function calculateQuotationValueByStatus($status) {
        $sql = "SELECT SUM(total) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE status = :status";
        
        $this->db->prepare($sql);
        $this->db->bind([':status' => $status]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าใบเสนอราคาตามผู้สร้าง
     * 
     * @param int $userId รหัสผู้ใช้
     * @return float
     */
    public function calculateQuotationValueByUser($userId) {
        $sql = "SELECT SUM(total) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE user_id = :user_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':user_id' => $userId]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าใบเสนอราคาตามช่วงเวลา
     * 
     * @param string $startDate วันที่เริ่มต้น
     * @param string $endDate วันที่สิ้นสุด
     * @return float
     */
    public function calculateQuotationValueByDateRange($startDate, $endDate) {
        $sql = "SELECT SUM(total) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE issue_date BETWEEN :start_date AND :end_date";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * ค้นหาใบเสนอราคา
     * 
     * @param string $keyword คำค้นหา
     * @return array
     */
    public function searchQuotations($keyword) {
        return $this->search($keyword, ['quotation_number', 'subject']);
    }
}

