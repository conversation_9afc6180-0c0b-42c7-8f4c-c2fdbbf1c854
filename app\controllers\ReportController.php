<?php
/**
 * Report Controller
 */

class ReportController extends BaseController {
    private $customerModel;
    private $opportunityModel;
    private $quotationModel;
    private $invoiceModel;
    private $paymentModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->customerModel = new Customer();
        $this->opportunityModel = new Opportunity();
        $this->quotationModel = new Quotation();
        $this->invoiceModel = new Invoice();
        $this->paymentModel = new Payment();
    }
    
    public function index() {
        $this->data['page_title'] = 'รายงาน';
        $this->view('reports/index');
    }
    
    public function sales() {
        $dateFrom = $this->get('date_from', date('Y-m-01'));
        $dateTo = $this->get('date_to', date('Y-m-t'));
        
        // ดึงข้อมูลสำหรับรายงาน
        $salesData = $this->getSalesData($dateFrom, $dateTo);
        
        $this->data['sales_data'] = $salesData;
        $this->data['date_from'] = $dateFrom;
        $this->data['date_to'] = $dateTo;
        $this->data['page_title'] = 'รายงานการขาย';
        $this->view('reports/sales');
    }
    
    public function customers() {
        $this->data['page_title'] = 'รายงานลูกค้า';
        $this->view('reports/customers');
    }
    
    public function opportunities() {
        $this->data['page_title'] = 'รายงานโอกาสการขาย';
        $this->view('reports/opportunities');
    }
    
    public function payments() {
        $this->data['page_title'] = 'รายงานการชำระเงิน';
        $this->view('reports/payments');
    }
    
    private function getSalesData($dateFrom, $dateTo) {
        // TODO: Implement actual sales data retrieval
        return [
            'total_sales' => 0,
            'total_invoices' => 0,
            'total_payments' => 0,
            'pending_invoices' => 0
        ];
    }
}
