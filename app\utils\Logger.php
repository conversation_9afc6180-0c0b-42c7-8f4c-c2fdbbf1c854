<?php
/**
 * Logger Utility
 * 
 * คลาสสำหรับจัดการการบันทึกล็อก
 */

class Logger {
    const LEVEL_DEBUG = 'DEBUG';
    const LEVEL_INFO = 'INFO';
    const LEVEL_WARNING = 'WARNING';
    const LEVEL_ERROR = 'ERROR';
    const LEVEL_CRITICAL = 'CRITICAL';
    
    private static $instance = null;
    private $logPath;
    private $maxFileSize;
    private $maxFiles;
    
    /**
     * คอนสตรักเตอร์
     */
    private function __construct() {
        $this->logPath = LOGS_PATH;
        $this->maxFileSize = 10 * 1024 * 1024; // 10MB
        $this->maxFiles = 10;
        
        // สร้างโฟลเดอร์ล็อกถ้ายังไม่มี
        if (!is_dir($this->logPath)) {
            mkdir($this->logPath, 0755, true);
        }
    }
    
    /**
     * ดึง instance ของ Logger (Singleton)
     * 
     * @return Logger
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * บันทึกล็อกระดับ DEBUG
     * 
     * @param string $message ข้อความ
     * @param array $context ข้อมูลเพิ่มเติม
     * @param string $channel ช่องทางการบันทึก
     */
    public static function debug($message, $context = [], $channel = 'app') {
        self::getInstance()->log(self::LEVEL_DEBUG, $message, $context, $channel);
    }
    
    /**
     * บันทึกล็อกระดับ INFO
     * 
     * @param string $message ข้อความ
     * @param array $context ข้อมูลเพิ่มเติม
     * @param string $channel ช่องทางการบันทึก
     */
    public static function info($message, $context = [], $channel = 'app') {
        self::getInstance()->log(self::LEVEL_INFO, $message, $context, $channel);
    }
    
    /**
     * บันทึกล็อกระดับ WARNING
     * 
     * @param string $message ข้อความ
     * @param array $context ข้อมูลเพิ่มเติม
     * @param string $channel ช่องทางการบันทึก
     */
    public static function warning($message, $context = [], $channel = 'app') {
        self::getInstance()->log(self::LEVEL_WARNING, $message, $context, $channel);
    }
    
    /**
     * บันทึกล็อกระดับ ERROR
     * 
     * @param string $message ข้อความ
     * @param array $context ข้อมูลเพิ่มเติม
     * @param string $channel ช่องทางการบันทึก
     */
    public static function error($message, $context = [], $channel = 'app') {
        self::getInstance()->log(self::LEVEL_ERROR, $message, $context, $channel);
    }
    
    /**
     * บันทึกล็อกระดับ CRITICAL
     * 
     * @param string $message ข้อความ
     * @param array $context ข้อมูลเพิ่มเติม
     * @param string $channel ช่องทางการบันทึก
     */
    public static function critical($message, $context = [], $channel = 'app') {
        self::getInstance()->log(self::LEVEL_CRITICAL, $message, $context, $channel);
    }
    
    /**
     * บันทึกล็อกกิจกรรมผู้ใช้
     * 
     * @param string $action การกระทำ
     * @param string $description คำอธิบาย
     * @param array $data ข้อมูลเพิ่มเติม
     * @param int $userId ID ของผู้ใช้
     */
    public static function activity($action, $description, $data = [], $userId = null) {
        $context = [
            'action' => $action,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'data' => $data
        ];
        
        self::getInstance()->log(self::LEVEL_INFO, $description, $context, 'activity');
    }
    
    /**
     * บันทึกล็อกการเข้าถึง
     * 
     * @param string $method HTTP method
     * @param string $uri URI ที่เข้าถึง
     * @param int $statusCode HTTP status code
     * @param float $responseTime เวลาในการตอบสนอง
     * @param int $userId ID ของผู้ใช้
     */
    public static function access($method, $uri, $statusCode, $responseTime = null, $userId = null) {
        $context = [
            'method' => $method,
            'uri' => $uri,
            'status_code' => $statusCode,
            'response_time' => $responseTime,
            'user_id' => $userId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? ''
        ];
        
        self::getInstance()->log(self::LEVEL_INFO, "Access: {$method} {$uri}", $context, 'access');
    }
    
    /**
     * บันทึกล็อกข้อผิดพลาดของระบบ
     * 
     * @param Exception $exception ข้อผิดพลาด
     * @param array $context ข้อมูลเพิ่มเติม
     */
    public static function exception($exception, $context = []) {
        $context = array_merge($context, [
            'exception_class' => get_class($exception),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ]);
        
        self::getInstance()->log(self::LEVEL_ERROR, $exception->getMessage(), $context, 'error');
    }
    
    /**
     * บันทึกล็อก
     * 
     * @param string $level ระดับล็อก
     * @param string $message ข้อความ
     * @param array $context ข้อมูลเพิ่มเติม
     * @param string $channel ช่องทางการบันทึก
     */
    private function log($level, $message, $context = [], $channel = 'app') {
        if (!LOG_ERRORS) {
            return;
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = [
            'timestamp' => $timestamp,
            'level' => $level,
            'channel' => $channel,
            'message' => $message,
            'context' => $context
        ];
        
        $logLine = $this->formatLogEntry($logEntry);
        $logFile = $this->getLogFile($channel);
        
        // ตรวจสอบขนาดไฟล์และหมุนล็อกถ้าจำเป็น
        $this->rotateLogIfNeeded($logFile);
        
        // เขียนล็อก
        file_put_contents($logFile, $logLine . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * จัดรูปแบบรายการล็อก
     * 
     * @param array $logEntry รายการล็อก
     * @return string
     */
    private function formatLogEntry($logEntry) {
        $formatted = "[{$logEntry['timestamp']}] {$logEntry['channel']}.{$logEntry['level']}: {$logEntry['message']}";
        
        if (!empty($logEntry['context'])) {
            $formatted .= ' ' . json_encode($logEntry['context'], JSON_UNESCAPED_UNICODE);
        }
        
        return $formatted;
    }
    
    /**
     * ดึงชื่อไฟล์ล็อก
     * 
     * @param string $channel ช่องทางการบันทึก
     * @return string
     */
    private function getLogFile($channel) {
        $date = date('Y-m-d');
        return $this->logPath . "/{$channel}-{$date}.log";
    }
    
    /**
     * หมุนล็อกถ้าไฟล์ใหญ่เกินไป
     * 
     * @param string $logFile ไฟล์ล็อก
     */
    private function rotateLogIfNeeded($logFile) {
        if (!file_exists($logFile)) {
            return;
        }
        
        if (filesize($logFile) >= $this->maxFileSize) {
            $this->rotateLog($logFile);
        }
    }
    
    /**
     * หมุนล็อก
     * 
     * @param string $logFile ไฟล์ล็อก
     */
    private function rotateLog($logFile) {
        $pathInfo = pathinfo($logFile);
        $baseName = $pathInfo['filename'];
        $extension = $pathInfo['extension'];
        $directory = $pathInfo['dirname'];
        
        // ย้ายไฟล์เก่า
        for ($i = $this->maxFiles - 1; $i >= 1; $i--) {
            $oldFile = "{$directory}/{$baseName}.{$i}.{$extension}";
            $newFile = "{$directory}/{$baseName}." . ($i + 1) . ".{$extension}";
            
            if (file_exists($oldFile)) {
                if ($i == $this->maxFiles - 1) {
                    unlink($oldFile); // ลบไฟล์เก่าสุด
                } else {
                    rename($oldFile, $newFile);
                }
            }
        }
        
        // ย้ายไฟล์ปัจจุบัน
        $rotatedFile = "{$directory}/{$baseName}.1.{$extension}";
        rename($logFile, $rotatedFile);
    }
    
    /**
     * ลบล็อกเก่า
     * 
     * @param int $days จำนวนวันที่เก่ากว่า
     * @param string $channel ช่องทางการบันทึก (ถ้าไม่ระบุจะลบทุกช่องทาง)
     */
    public static function cleanOldLogs($days = 30, $channel = null) {
        $instance = self::getInstance();
        $cutoffTime = time() - ($days * 24 * 60 * 60);
        
        $pattern = $channel ? "{$channel}-*.log*" : "*.log*";
        $files = glob($instance->logPath . "/{$pattern}");
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                unlink($file);
            }
        }
    }
    
    /**
     * ดึงรายการไฟล์ล็อก
     * 
     * @param string $channel ช่องทางการบันทึก
     * @return array
     */
    public static function getLogFiles($channel = null) {
        $instance = self::getInstance();
        $pattern = $channel ? "{$channel}-*.log*" : "*.log*";
        $files = glob($instance->logPath . "/{$pattern}");
        
        // เรียงตามวันที่แก้ไขล่าสุด
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        return $files;
    }
    
    /**
     * อ่านล็อกจากไฟล์
     * 
     * @param string $file ไฟล์ล็อก
     * @param int $lines จำนวนบรรทัดที่ต้องการอ่าน (จากท้าย)
     * @return array
     */
    public static function readLog($file, $lines = 100) {
        if (!file_exists($file)) {
            return [];
        }
        
        $content = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        if ($lines > 0) {
            $content = array_slice($content, -$lines);
        }
        
        $logs = [];
        foreach ($content as $line) {
            $logs[] = self::parseLogLine($line);
        }
        
        return array_reverse($logs); // แสดงล่าสุดก่อน
    }
    
    /**
     * แยกวิเคราะห์บรรทัดล็อก
     * 
     * @param string $line บรรทัดล็อก
     * @return array
     */
    private static function parseLogLine($line) {
        $pattern = '/^\[([^\]]+)\] ([^.]+)\.([^:]+): (.+)$/';
        
        if (preg_match($pattern, $line, $matches)) {
            $message = $matches[4];
            $context = [];
            
            // พยายามแยก context ออกจาก message
            if (strpos($message, ' {') !== false) {
                $parts = explode(' {', $message, 2);
                $message = $parts[0];
                $contextJson = '{' . $parts[1];
                $context = json_decode($contextJson, true) ?: [];
            }
            
            return [
                'timestamp' => $matches[1],
                'channel' => $matches[2],
                'level' => $matches[3],
                'message' => $message,
                'context' => $context,
                'raw' => $line
            ];
        }
        
        return [
            'timestamp' => '',
            'channel' => '',
            'level' => '',
            'message' => $line,
            'context' => [],
            'raw' => $line
        ];
    }
    
    /**
     * ค้นหาในล็อก
     * 
     * @param string $query คำค้นหา
     * @param string $channel ช่องทางการบันทึก
     * @param string $level ระดับล็อก
     * @param string $dateFrom วันที่เริ่มต้น
     * @param string $dateTo วันที่สิ้นสุด
     * @return array
     */
    public static function search($query, $channel = null, $level = null, $dateFrom = null, $dateTo = null) {
        $files = self::getLogFiles($channel);
        $results = [];
        
        foreach ($files as $file) {
            $logs = self::readLog($file, 0); // อ่านทั้งหมด
            
            foreach ($logs as $log) {
                // กรองตามเงื่อนไข
                if ($level && $log['level'] !== $level) {
                    continue;
                }
                
                if ($dateFrom && $log['timestamp'] < $dateFrom) {
                    continue;
                }
                
                if ($dateTo && $log['timestamp'] > $dateTo) {
                    continue;
                }
                
                // ค้นหาในข้อความ
                if (stripos($log['message'], $query) !== false || 
                    stripos(json_encode($log['context']), $query) !== false) {
                    $results[] = $log;
                }
            }
        }
        
        return $results;
    }
}
