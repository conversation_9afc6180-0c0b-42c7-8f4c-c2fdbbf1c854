<?php
/**
 * Contact Controller
 * 
 * คอนโทรลเลอร์สำหรับจัดการข้อมูลผู้ติดต่อ
 */

class ContactController extends BaseController {
    private $contactModel;
    private $customerModel;
    
    public function __construct() {
        parent::__construct();
        
        // ตรวจสอบการเข้าสู่ระบบ
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $this->contactModel = new Contact();
        $this->customerModel = new Customer();
    }
    
    /**
     * แสดงรายการผู้ติดต่อ
     */
    public function index() {
        $page = $this->get('page', 1);
        $search = $this->get('search', '');
        $customerId = $this->get('customer_id', '');
        
        $filters = [];
        if (!empty($search)) {
            $filters['keyword'] = $search;
        }
        if (!empty($customerId)) {
            $filters['customer_id'] = $customerId;
        }
        
        $contacts = $this->contactModel->getAllContactsWithCustomer($page);
        $this->setPagination($contacts);
        
        // ดึงรายการลูกค้าสำหรับ filter
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['contacts'] = $contacts['items'];
        $this->data['customers'] = $customers;
        $this->data['search'] = $search;
        $this->data['customer_id'] = $customerId;
        $this->data['page_title'] = 'รายการผู้ติดต่อ';
        
        $this->view('contacts/index');
    }
    
    /**
     * แสดงรายละเอียดผู้ติดต่อ
     */
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $contact = $this->contactModel->getContactWithCustomer($id);
        if (!$contact) {
            $this->show404();
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($contact['customer_id'])) {
            $this->show403();
            return;
        }
        
        $this->data['contact'] = $contact;
        $this->data['page_title'] = 'รายละเอียดผู้ติดต่อ: ' . $contact['first_name'] . ' ' . $contact['last_name'];
        
        $this->view('contacts/view');
    }
    
    /**
     * แสดงฟอร์มเพิ่มผู้ติดต่อใหม่
     */
    public function create() {
        if (!$this->hasPermission('contact', 'create')) {
            $this->show403();
            return;
        }
        
        $customerId = $this->get('customer_id');
        
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        // ดึงรายการลูกค้า
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['customers'] = $customers;
        $this->data['selected_customer_id'] = $customerId;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'เพิ่มผู้ติดต่อใหม่';
        
        $this->view('contacts/create');
    }
    
    /**
     * ประมวลผลการเพิ่มผู้ติดต่อใหม่
     */
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=contact&action=create');
            return;
        }
        
        $data = $this->post();
        
        // ตรวจสอบข้อมูล
        $validator = new ValidationHelper($data);
        $validator->required('customer_id', 'กรุณาเลือกลูกค้า')
                 ->required('first_name', 'กรุณากรอกชื่อ')
                 ->required('last_name', 'กรุณากรอกนามสกุล')
                 ->email('email', 'รูปแบบอีเมลไม่ถูกต้อง')
                 ->phone('phone', 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง')
                 ->phone('mobile', 'รูปแบบเบอร์มือถือไม่ถูกต้อง');
        
        if (!empty($data['email'])) {
            $validator->unique('email', 'contacts', 'email', null, 'อีเมลนี้มีอยู่ในระบบแล้ว');
        }
        
        if ($validator->hasErrors()) {
            $customers = $this->customerModel->getForDropdown();
            $this->data['customers'] = $customers;
            $this->data['errors'] = $validator->getErrors();
            $this->data['old_data'] = $data;
            $this->data['csrf_token'] = generateCSRFToken();
            $this->data['page_title'] = 'เพิ่มผู้ติดต่อใหม่';
            $this->view('contacts/create');
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึงลูกค้า
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($data['customer_id'])) {
            $this->show403();
            return;
        }
        
        // เพิ่มข้อมูลเพิ่มเติม
        $data['is_primary'] = isset($data['is_primary']) ? 1 : 0;
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $contactId = $this->contactModel->create($data);
        
        if ($contactId) {
            // ถ้าเป็นผู้ติดต่อหลัก ให้ยกเลิกผู้ติดต่อหลักคนอื่น
            if ($data['is_primary']) {
                $this->contactModel->setPrimaryContact($contactId, $data['customer_id']);
            }
            
            $this->logActivity('contact_created', 'Created new contact: ' . $data['first_name'] . ' ' . $data['last_name'], ['contact_id' => $contactId]);
            showAlert('เพิ่มผู้ติดต่อเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=contact&action=show&id=' . $contactId);
        } else {
            showAlert('เกิดข้อผิดพลาดในการเพิ่มผู้ติดต่อ', 'error');
            $this->redirect('index.php?controller=contact&action=create');
        }
    }
    
    /**
     * แสดงฟอร์มแก้ไขผู้ติดต่อ
     */
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        if (!$this->hasPermission('contact', 'edit')) {
            $this->show403();
            return;
        }
        
        $contact = $this->contactModel->getById($id);
        if (!$contact) {
            $this->show404();
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($contact['customer_id'])) {
            $this->show403();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        // ดึงรายการลูกค้า
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['contact'] = $contact;
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขผู้ติดต่อ: ' . $contact['first_name'] . ' ' . $contact['last_name'];
        
        $this->view('contacts/edit');
    }
    
    /**
     * ประมวลผลการแก้ไขผู้ติดต่อ
     */
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=contact&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        
        // ตรวจสอบข้อมูล
        $validator = new ValidationHelper($data);
        $validator->required('customer_id', 'กรุณาเลือกลูกค้า')
                 ->required('first_name', 'กรุณากรอกชื่อ')
                 ->required('last_name', 'กรุณากรอกนามสกุล')
                 ->email('email', 'รูปแบบอีเมลไม่ถูกต้อง')
                 ->phone('phone', 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง')
                 ->phone('mobile', 'รูปแบบเบอร์มือถือไม่ถูกต้อง');
        
        if (!empty($data['email'])) {
            $validator->unique('email', 'contacts', 'email', $id, 'อีเมลนี้มีอยู่ในระบบแล้ว');
        }
        
        if ($validator->hasErrors()) {
            $contact = $this->contactModel->getById($id);
            $customers = $this->customerModel->getForDropdown();
            $this->data['contact'] = $contact;
            $this->data['customers'] = $customers;
            $this->data['errors'] = $validator->getErrors();
            $this->data['old_data'] = $data;
            $this->data['csrf_token'] = generateCSRFToken();
            $this->data['page_title'] = 'แก้ไขผู้ติดต่อ: ' . $contact['first_name'] . ' ' . $contact['last_name'];
            $this->view('contacts/edit');
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึงลูกค้า
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($data['customer_id'])) {
            $this->show403();
            return;
        }
        
        // เพิ่มข้อมูลเพิ่มเติม
        $data['is_primary'] = isset($data['is_primary']) ? 1 : 0;
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->contactModel->update($id, $data);
        
        if ($success) {
            // ถ้าเป็นผู้ติดต่อหลัก ให้ยกเลิกผู้ติดต่อหลักคนอื่น
            if ($data['is_primary']) {
                $this->contactModel->setPrimaryContact($id, $data['customer_id']);
            }
            
            $this->logActivity('contact_updated', 'Updated contact: ' . $data['first_name'] . ' ' . $data['last_name'], ['contact_id' => $id]);
            showAlert('แก้ไขผู้ติดต่อเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=contact&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาดในการแก้ไขผู้ติดต่อ', 'error');
            $this->redirect('index.php?controller=contact&action=edit&id=' . $id);
        }
    }
    
    /**
     * ลบผู้ติดต่อ
     */
    public function delete($id) {
        if (!$id) {
            $this->json(['success' => false, 'message' => 'ไม่พบข้อมูลผู้ติดต่อ'], 400);
            return;
        }
        
        if (!$this->hasPermission('contact', 'delete')) {
            $this->json(['success' => false, 'message' => 'ไม่มีสิทธิ์ลบข้อมูล'], 403);
            return;
        }
        
        if (!$this->isMethod('POST')) {
            $this->json(['success' => false, 'message' => 'Method not allowed'], 405);
            return;
        }
        
        if (!$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $contact = $this->contactModel->getById($id);
        if (!$contact) {
            $this->json(['success' => false, 'message' => 'ไม่พบข้อมูลผู้ติดต่อ'], 404);
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($contact['customer_id'])) {
            $this->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้'], 403);
            return;
        }
        
        $success = $this->contactModel->delete($id);
        
        if ($success) {
            $this->logActivity('contact_deleted', 'Deleted contact: ' . $contact['first_name'] . ' ' . $contact['last_name'], ['contact_id' => $id]);
            $this->json(['success' => true, 'message' => 'ลบผู้ติดต่อเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการลบผู้ติดต่อ'], 500);
        }
    }
    
    /**
     * ค้นหาผู้ติดต่อ (สำหรับ AJAX)
     */
    public function search() {
        if (!$this->isAjax()) {
            $this->show404();
            return;
        }
        
        $query = $this->get('q', '');
        $customerId = $this->get('customer_id', '');
        $limit = $this->get('limit', 10);
        
        if (strlen($query) < 2) {
            $this->json(['results' => []]);
            return;
        }
        
        $contacts = $this->contactModel->searchContacts($query);
        $results = [];
        
        foreach (array_slice($contacts, 0, $limit) as $contact) {
            // กรองตามลูกค้าถ้าระบุ
            if ($customerId && $contact['customer_id'] != $customerId) {
                continue;
            }
            
            $results[] = [
                'id' => $contact['id'],
                'text' => $contact['first_name'] . ' ' . $contact['last_name'],
                'email' => $contact['email'],
                'phone' => $contact['phone'],
                'customer_id' => $contact['customer_id']
            ];
        }
        
        $this->json(['results' => $results]);
    }
    
    /**
     * ตั้งเป็นผู้ติดต่อหลัก
     */
    public function setPrimary($id) {
        if (!$id) {
            $this->json(['success' => false, 'message' => 'ไม่พบข้อมูลผู้ติดต่อ'], 400);
            return;
        }
        
        if (!$this->isMethod('POST')) {
            $this->json(['success' => false, 'message' => 'Method not allowed'], 405);
            return;
        }
        
        if (!$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $contact = $this->contactModel->getById($id);
        if (!$contact) {
            $this->json(['success' => false, 'message' => 'ไม่พบข้อมูลผู้ติดต่อ'], 404);
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($contact['customer_id'])) {
            $this->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้'], 403);
            return;
        }
        
        $success = $this->contactModel->setPrimaryContact($id, $contact['customer_id']);
        
        if ($success) {
            $this->logActivity('contact_set_primary', 'Set primary contact: ' . $contact['first_name'] . ' ' . $contact['last_name'], ['contact_id' => $id]);
            $this->json(['success' => true, 'message' => 'ตั้งเป็นผู้ติดต่อหลักเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการตั้งผู้ติดต่อหลัก'], 500);
        }
    }
}
