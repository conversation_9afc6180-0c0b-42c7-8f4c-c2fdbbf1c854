<?php
/**
 * Number Helper
 * 
 * คลาสช่วยเหลือสำหรับการจัดการตัวเลขและการคำนวณ
 */

class NumberHelper {
    
    /**
     * จัดรูปแบบตัวเลขเป็นเงิน
     * 
     * @param float $amount จำนวนเงิน
     * @param string $currency สกุลเงิน
     * @param int $decimals จำนวนทศนิยม
     * @return string
     */
    public static function formatCurrency($amount, $currency = 'THB', $decimals = 2) {
        $formatted = number_format($amount, $decimals);
        
        switch ($currency) {
            case 'THB':
                return $formatted . ' บาท';
            case 'USD':
                return '$' . $formatted;
            case 'EUR':
                return '€' . $formatted;
            default:
                return $formatted . ' ' . $currency;
        }
    }
    
    /**
     * จัดรูปแบบตัวเลขพร้อมคั่นหลักพัน
     * 
     * @param float $number ตัวเลข
     * @param int $decimals จำนวนทศนิยม
     * @return string
     */
    public static function formatNumber($number, $decimals = 0) {
        return number_format($number, $decimals);
    }
    
    /**
     * แปลงตัวเลขเป็นเปอร์เซ็นต์
     * 
     * @param float $number ตัวเลข
     * @param int $decimals จำนวนทศนิยม
     * @return string
     */
    public static function formatPercentage($number, $decimals = 2) {
        return number_format($number, $decimals) . '%';
    }
    
    /**
     * แปลงตัวเลขเป็นคำอ่านภาษาไทย
     * 
     * @param float $number ตัวเลข
     * @return string
     */
    public static function numberToThaiText($number) {
        $txtnum = array(
            '', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', 'แปด', 'เก้า'
        );
        
        $txtdigit = array(
            '', 'สิบ', 'ร้อย', 'พัน', 'หมื่น', 'แสน', 'ล้าน'
        );
        
        $number = number_format($number, 2, '.', '');
        $parts = explode('.', $number);
        $integer = $parts[0];
        $decimal = isset($parts[1]) ? $parts[1] : '00';
        
        $result = self::convertIntegerToThai($integer, $txtnum, $txtdigit);
        
        if ($result === '') {
            $result = 'ศูนย์';
        }
        
        $result .= 'บาท';
        
        if ($decimal !== '00') {
            $result .= self::convertDecimalToThai($decimal, $txtnum, $txtdigit);
            $result .= 'สตางค์';
        } else {
            $result .= 'ถ้วน';
        }
        
        return $result;
    }
    
    /**
     * แปลงส่วนจำนวนเต็มเป็นคำอ่านไทย
     * 
     * @param string $number ตัวเลข
     * @param array $txtnum อาร์เรย์ตัวเลข
     * @param array $txtdigit อาร์เรย์หลัก
     * @return string
     */
    private static function convertIntegerToThai($number, $txtnum, $txtdigit) {
        $number = ltrim($number, '0');
        $len = strlen($number);
        $result = '';
        
        for ($i = 0; $i < $len; $i++) {
            $digit = intval($number[$i]);
            $position = $len - $i;
            
            if ($digit !== 0) {
                if ($position === 2 && $digit === 1) {
                    $result .= 'สิบ';
                } elseif ($position === 2 && $digit === 2) {
                    $result .= 'ยี่สิบ';
                } elseif ($position === 1 && $digit === 1 && $len > 1) {
                    $result .= 'เอ็ด';
                } else {
                    $result .= $txtnum[$digit];
                    if ($position > 1) {
                        $result .= $txtdigit[$position - 1];
                    }
                }
            }
        }
        
        return $result;
    }
    
    /**
     * แปลงส่วนทศนิยมเป็นคำอ่านไทย
     * 
     * @param string $decimal ทศนิยม
     * @param array $txtnum อาร์เรย์ตัวเลข
     * @param array $txtdigit อาร์เรย์หลัก
     * @return string
     */
    private static function convertDecimalToThai($decimal, $txtnum, $txtdigit) {
        $decimal = rtrim($decimal, '0');
        
        if ($decimal === '') {
            return '';
        }
        
        $len = strlen($decimal);
        $result = '';
        
        for ($i = 0; $i < $len; $i++) {
            $digit = intval($decimal[$i]);
            $position = $len - $i;
            
            if ($digit !== 0) {
                if ($position === 2 && $digit === 1) {
                    $result .= 'สิบ';
                } elseif ($position === 2 && $digit === 2) {
                    $result .= 'ยี่สิบ';
                } elseif ($position === 1 && $digit === 1 && $len > 1) {
                    $result .= 'เอ็ด';
                } else {
                    $result .= $txtnum[$digit];
                    if ($position > 1) {
                        $result .= $txtdigit[$position - 1];
                    }
                }
            }
        }
        
        return $result;
    }
    
    /**
     * คำนวณเปอร์เซ็นต์
     * 
     * @param float $value ค่าที่ต้องการคำนวณ
     * @param float $total ค่ารวม
     * @param int $decimals จำนวนทศนิยม
     * @return float
     */
    public static function calculatePercentage($value, $total, $decimals = 2) {
        if ($total == 0) {
            return 0;
        }
        
        return round(($value / $total) * 100, $decimals);
    }
    
    /**
     * คำนวณส่วนลด
     * 
     * @param float $originalPrice ราคาเดิม
     * @param float $discountPercent เปอร์เซ็นต์ส่วนลด
     * @return array
     */
    public static function calculateDiscount($originalPrice, $discountPercent) {
        $discountAmount = ($originalPrice * $discountPercent) / 100;
        $finalPrice = $originalPrice - $discountAmount;
        
        return [
            'original_price' => $originalPrice,
            'discount_percent' => $discountPercent,
            'discount_amount' => $discountAmount,
            'final_price' => $finalPrice
        ];
    }
    
    /**
     * คำนวณภาษี
     * 
     * @param float $amount จำนวนเงิน
     * @param float $taxRate อัตราภาษี
     * @param bool $inclusive ภาษีรวมในราคาหรือไม่
     * @return array
     */
    public static function calculateTax($amount, $taxRate, $inclusive = false) {
        if ($inclusive) {
            // ภาษีรวมในราคา
            $baseAmount = $amount / (1 + ($taxRate / 100));
            $taxAmount = $amount - $baseAmount;
        } else {
            // ภาษีแยกจากราคา
            $baseAmount = $amount;
            $taxAmount = ($amount * $taxRate) / 100;
        }
        
        return [
            'base_amount' => round($baseAmount, 2),
            'tax_rate' => $taxRate,
            'tax_amount' => round($taxAmount, 2),
            'total_amount' => round($baseAmount + $taxAmount, 2)
        ];
    }
    
    /**
     * แปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
     * 
     * @param int $bytes ขนาดไฟล์ในหน่วย bytes
     * @param int $precision จำนวนทศนิยม
     * @return string
     */
    public static function formatFileSize($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * สร้างตัวเลขสุ่ม
     * 
     * @param int $min ค่าต่ำสุด
     * @param int $max ค่าสูงสุด
     * @return int
     */
    public static function randomNumber($min = 1, $max = 100) {
        return rand($min, $max);
    }
    
    /**
     * ตรวจสอบว่าเป็นเลขคู่หรือไม่
     * 
     * @param int $number ตัวเลข
     * @return bool
     */
    public static function isEven($number) {
        return $number % 2 === 0;
    }
    
    /**
     * ตรวจสอบว่าเป็นเลขคี่หรือไม่
     * 
     * @param int $number ตัวเลข
     * @return bool
     */
    public static function isOdd($number) {
        return $number % 2 !== 0;
    }
    
    /**
     * คำนวณค่าเฉลี่ย
     * 
     * @param array $numbers อาร์เรย์ของตัวเลข
     * @return float
     */
    public static function average($numbers) {
        if (empty($numbers)) {
            return 0;
        }
        
        return array_sum($numbers) / count($numbers);
    }
    
    /**
     * หาค่าสูงสุด
     * 
     * @param array $numbers อาร์เรย์ของตัวเลข
     * @return float
     */
    public static function maximum($numbers) {
        if (empty($numbers)) {
            return 0;
        }
        
        return max($numbers);
    }
    
    /**
     * หาค่าต่ำสุด
     * 
     * @param array $numbers อาร์เรย์ของตัวเลข
     * @return float
     */
    public static function minimum($numbers) {
        if (empty($numbers)) {
            return 0;
        }
        
        return min($numbers);
    }
    
    /**
     * ปัดเศษขึ้น
     * 
     * @param float $number ตัวเลข
     * @param int $precision จำนวนทศนิยม
     * @return float
     */
    public static function roundUp($number, $precision = 0) {
        $multiplier = pow(10, $precision);
        return ceil($number * $multiplier) / $multiplier;
    }
    
    /**
     * ปัดเศษลง
     * 
     * @param float $number ตัวเลข
     * @param int $precision จำนวนทศนิยม
     * @return float
     */
    public static function roundDown($number, $precision = 0) {
        $multiplier = pow(10, $precision);
        return floor($number * $multiplier) / $multiplier;
    }
    
    /**
     * แปลงเลขโรมัน
     * 
     * @param int $number ตัวเลข
     * @return string
     */
    public static function toRoman($number) {
        $map = [
            1000 => 'M', 900 => 'CM', 500 => 'D', 400 => 'CD',
            100 => 'C', 90 => 'XC', 50 => 'L', 40 => 'XL',
            10 => 'X', 9 => 'IX', 5 => 'V', 4 => 'IV', 1 => 'I'
        ];
        
        $result = '';
        
        foreach ($map as $value => $roman) {
            while ($number >= $value) {
                $result .= $roman;
                $number -= $value;
            }
        }
        
        return $result;
    }
    
    /**
     * ตรวจสอบว่าอยู่ในช่วงหรือไม่
     * 
     * @param float $number ตัวเลข
     * @param float $min ค่าต่ำสุด
     * @param float $max ค่าสูงสุด
     * @return bool
     */
    public static function inRange($number, $min, $max) {
        return $number >= $min && $number <= $max;
    }
    
    /**
     * จำกัดค่าให้อยู่ในช่วงที่กำหนด
     * 
     * @param float $number ตัวเลข
     * @param float $min ค่าต่ำสุด
     * @param float $max ค่าสูงสุด
     * @return float
     */
    public static function clamp($number, $min, $max) {
        return max($min, min($max, $number));
    }
}
