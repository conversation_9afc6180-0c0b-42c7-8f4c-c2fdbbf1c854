<?php
/**
 * Validation Helper
 * 
 * คลาสช่วยเหลือสำหรับการตรวจสอบความถูกต้องของข้อมูล
 */

class ValidationHelper {
    private $errors = [];
    private $data = [];
    
    /**
     * คอนสตรักเตอร์
     * 
     * @param array $data ข้อมูลที่ต้องการตรวจสอบ
     */
    public function __construct($data = []) {
        $this->data = $data;
        $this->errors = [];
    }
    
    /**
     * ตั้งค่าข้อมูลที่ต้องการตรวจสอบ
     * 
     * @param array $data ข้อมูล
     * @return self
     */
    public function setData($data) {
        $this->data = $data;
        return $this;
    }
    
    /**
     * ตรวจสอบว่าฟิลด์จำเป็นต้องกรอก
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function required($field, $message = null) {
        if (empty($this->data[$field])) {
            $this->errors[$field] = $message ?: "ฟิลด์ {$field} จำเป็นต้องกรอก";
        }
        return $this;
    }
    
    /**
     * ตรวจสอบความยาวขั้นต่ำ
     * 
     * @param string $field ชื่อฟิลด์
     * @param int $min ความยาวขั้นต่ำ
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function minLength($field, $min, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) < $min) {
            $this->errors[$field] = $message ?: "ฟิลด์ {$field} ต้องมีอย่างน้อย {$min} ตัวอักษร";
        }
        return $this;
    }
    
    /**
     * ตรวจสอบความยาวสูงสุด
     * 
     * @param string $field ชื่อฟิลด์
     * @param int $max ความยาวสูงสุด
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function maxLength($field, $max, $message = null) {
        if (isset($this->data[$field]) && strlen($this->data[$field]) > $max) {
            $this->errors[$field] = $message ?: "ฟิลด์ {$field} ต้องมีไม่เกิน {$max} ตัวอักษร";
        }
        return $this;
    }
    
    /**
     * ตรวจสอบรูปแบบอีเมล
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function email($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_EMAIL)) {
                $this->errors[$field] = $message ?: "รูปแบบอีเมลไม่ถูกต้อง";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบรูปแบบเบอร์โทรศัพท์
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function phone($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!preg_match('/^[0-9\-\+\(\)\s]+$/', $this->data[$field])) {
                $this->errors[$field] = $message ?: "รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบว่าเป็นตัวเลข
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function numeric($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!is_numeric($this->data[$field])) {
                $this->errors[$field] = $message ?: "ฟิลด์ {$field} ต้องเป็นตัวเลข";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบว่าเป็นจำนวนเต็ม
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function integer($field, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!filter_var($this->data[$field], FILTER_VALIDATE_INT)) {
                $this->errors[$field] = $message ?: "ฟิลด์ {$field} ต้องเป็นจำนวนเต็ม";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบค่าขั้นต่ำ
     * 
     * @param string $field ชื่อฟิลด์
     * @param float $min ค่าขั้นต่ำ
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function min($field, $min, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (is_numeric($this->data[$field]) && $this->data[$field] < $min) {
                $this->errors[$field] = $message ?: "ฟิลด์ {$field} ต้องมีค่าอย่างน้อย {$min}";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบค่าสูงสุด
     * 
     * @param string $field ชื่อฟิลด์
     * @param float $max ค่าสูงสุด
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function max($field, $max, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (is_numeric($this->data[$field]) && $this->data[$field] > $max) {
                $this->errors[$field] = $message ?: "ฟิลด์ {$field} ต้องมีค่าไม่เกิน {$max}";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบรูปแบบวันที่
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $format รูปแบบวันที่
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function date($field, $format = 'Y-m-d', $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $date = DateTime::createFromFormat($format, $this->data[$field]);
            if (!$date || $date->format($format) !== $this->data[$field]) {
                $this->errors[$field] = $message ?: "รูปแบบวันที่ไม่ถูกต้อง";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบว่าค่าตรงกัน
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $matchField ชื่อฟิลด์ที่ต้องตรงกัน
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function match($field, $matchField, $message = null) {
        if (isset($this->data[$field]) && isset($this->data[$matchField])) {
            if ($this->data[$field] !== $this->data[$matchField]) {
                $this->errors[$field] = $message ?: "ฟิลด์ {$field} และ {$matchField} ต้องตรงกัน";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบว่าค่าอยู่ในรายการที่กำหนด
     * 
     * @param string $field ชื่อฟิลด์
     * @param array $values รายการค่าที่อนุญาต
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function in($field, $values, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!in_array($this->data[$field], $values)) {
                $this->errors[$field] = $message ?: "ค่าของฟิลด์ {$field} ไม่ถูกต้อง";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบรูปแบบด้วย Regular Expression
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $pattern รูปแบบ regex
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function regex($field, $pattern, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            if (!preg_match($pattern, $this->data[$field])) {
                $this->errors[$field] = $message ?: "รูปแบบของฟิลด์ {$field} ไม่ถูกต้อง";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบความไม่ซ้ำในฐานข้อมูล
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $table ชื่อตาราง
     * @param string $column ชื่อคอลัมน์
     * @param int $excludeId ID ที่ต้องการยกเว้น
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function unique($field, $table, $column = null, $excludeId = null, $message = null) {
        if (isset($this->data[$field]) && !empty($this->data[$field])) {
            $column = $column ?: $field;
            
            $db = Database::getInstance();
            $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $table . " WHERE {$column} = :value";
            $params = [':value' => $this->data[$field]];
            
            if ($excludeId) {
                $sql .= " AND id != :exclude_id";
                $params[':exclude_id'] = $excludeId;
            }
            
            $db->prepare($sql);
            $db->bind($params);
            $db->execute();
            
            $result = $db->fetch();
            
            if ($result['count'] > 0) {
                $this->errors[$field] = $message ?: "ค่าของฟิลด์ {$field} มีอยู่ในระบบแล้ว";
            }
        }
        return $this;
    }
    
    /**
     * ตรวจสอบว่ามีข้อผิดพลาดหรือไม่
     * 
     * @return bool
     */
    public function hasErrors() {
        return !empty($this->errors);
    }
    
    /**
     * ดึงข้อผิดพลาดทั้งหมด
     * 
     * @return array
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * ดึงข้อผิดพลาดของฟิลด์เฉพาะ
     * 
     * @param string $field ชื่อฟิลด์
     * @return string|null
     */
    public function getError($field) {
        return $this->errors[$field] ?? null;
    }
    
    /**
     * ดึงข้อผิดพลาดแรก
     * 
     * @return string|null
     */
    public function getFirstError() {
        return !empty($this->errors) ? reset($this->errors) : null;
    }
    
    /**
     * เคลียร์ข้อผิดพลาด
     * 
     * @return self
     */
    public function clearErrors() {
        $this->errors = [];
        return $this;
    }
    
    /**
     * เพิ่มข้อผิดพลาดแบบกำหนดเอง
     * 
     * @param string $field ชื่อฟิลด์
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function addError($field, $message) {
        $this->errors[$field] = $message;
        return $this;
    }
    
    /**
     * ตรวจสอบไฟล์ที่อัปโหลด
     * 
     * @param string $field ชื่อฟิลด์
     * @param array $allowedTypes ประเภทไฟล์ที่อนุญาต
     * @param int $maxSize ขนาดไฟล์สูงสุด (bytes)
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function file($field, $allowedTypes = [], $maxSize = null, $message = null) {
        if (isset($_FILES[$field]) && $_FILES[$field]['error'] !== UPLOAD_ERR_NO_FILE) {
            $file = $_FILES[$field];
            
            // ตรวจสอบข้อผิดพลาดในการอัปโหลด
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $this->errors[$field] = $message ?: "เกิดข้อผิดพลาดในการอัปโหลดไฟล์";
                return $this;
            }
            
            // ตรวจสอบประเภทไฟล์
            if (!empty($allowedTypes)) {
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                if (!in_array($extension, $allowedTypes)) {
                    $this->errors[$field] = $message ?: "ประเภทไฟล์ไม่ถูกต้อง อนุญาตเฉพาะ: " . implode(', ', $allowedTypes);
                    return $this;
                }
            }
            
            // ตรวจสอบขนาดไฟล์
            if ($maxSize && $file['size'] > $maxSize) {
                $this->errors[$field] = $message ?: "ขนาดไฟล์เกินกำหนด สูงสุด: " . formatFileSize($maxSize);
                return $this;
            }
        }
        
        return $this;
    }
    
    /**
     * ตรวจสอบความถูกต้องแบบกำหนดเอง
     * 
     * @param string $field ชื่อฟิลด์
     * @param callable $callback ฟังก์ชันตรวจสอบ
     * @param string $message ข้อความข้อผิดพลาด
     * @return self
     */
    public function custom($field, $callback, $message = null) {
        if (isset($this->data[$field])) {
            $result = call_user_func($callback, $this->data[$field], $this->data);
            if (!$result) {
                $this->errors[$field] = $message ?: "ค่าของฟิลด์ {$field} ไม่ถูกต้อง";
            }
        }
        return $this;
    }
}
