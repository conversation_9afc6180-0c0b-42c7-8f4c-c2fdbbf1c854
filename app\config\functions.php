<?php
/**
 * Common Functions
 * 
 * ไฟล์ฟังก์ชันทั่วไปสำหรับระบบ
 */

/**
 * ฟังก์ชันสำหรับแสดงข้อความแจ้งเตือน
 */
function showAlert($message, $type = 'info') {
    $_SESSION['alert'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * ฟังก์ชันสำหรับแสดงข้อความแจ้งเตือนและลบออกจาก session
 */
function getAlert() {
    if (isset($_SESSION['alert'])) {
        $alert = $_SESSION['alert'];
        unset($_SESSION['alert']);
        return $alert;
    }
    return null;
}

/**
 * ฟังก์ชันสำหรับ redirect
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * ฟังก์ชันสำหรับ sanitize input
 */
function sanitize($input) {
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitize($value);
        }
        return $input;
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * ฟังก์ชันสำหรับตรวจสอบว่าเป็น AJAX request หรือไม่
 */
function isAjax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * ฟังก์ชันสำหรับส่ง JSON response
 */
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * ฟังก์ชันสำหรับสร้าง CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * ฟังก์ชันสำหรับตรวจสอบ CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * ฟังก์ชันสำหรับสร้างรหัสผ่านแบบสุ่ม
 */
function generateRandomPassword($length = 8) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

/**
 * ฟังก์ชันสำหรับแปลงขนาดไฟล์เป็นรูปแบบที่อ่านง่าย
 */
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * ฟังก์ชันสำหรับตรวจสอบนามสกุลไฟล์
 */
function isAllowedFileExtension($filename) {
    $allowedExtensions = explode(',', ALLOWED_EXTENSIONS);
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, $allowedExtensions);
}

/**
 * ฟังก์ชันสำหรับสร้างชื่อไฟล์ที่ไม่ซ้ำ
 */
function generateUniqueFilename($originalName) {
    $extension = pathinfo($originalName, PATHINFO_EXTENSION);
    $filename = pathinfo($originalName, PATHINFO_FILENAME);
    $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
    return $filename . '_' . time() . '_' . uniqid() . '.' . $extension;
}

/**
 * ฟังก์ชันสำหรับแปลงวันที่เป็นรูปแบบไทย
 */
function formatDateThai($date, $format = 'd/m/Y') {
    if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
        return '-';
    }
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

/**
 * ฟังก์ชันสำหรับแปลงตัวเลขเป็นรูปแบบเงิน
 */
function formatCurrency($amount, $currency = 'THB') {
    return number_format($amount, 2) . ' ' . $currency;
}

/**
 * ฟังก์ชันสำหรับสร้างเลขที่เอกสาร
 */
function generateDocumentNumber($prefix, $lastNumber = 0) {
    $newNumber = $lastNumber + 1;
    return $prefix . date('Ym') . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
}

/**
 * ฟังก์ชันสำหรับบันทึกล็อก
 */
function writeLog($message, $level = 'INFO', $file = null) {
    if (!LOG_ERRORS) return;
    
    $logFile = $file ?: ERROR_LOG_FILE;
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$level] $message" . PHP_EOL;
    
    if (!file_exists(dirname($logFile))) {
        mkdir(dirname($logFile), 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * ฟังก์ชันสำหรับดีบัก
 */
function debug($data, $die = false) {
    echo '<pre>';
    print_r($data);
    echo '</pre>';
    if ($die) die();
}

/**
 * ฟังก์ชันสำหรับตรวจสอบว่าเป็นอีเมลหรือไม่
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * ฟังก์ชันสำหรับตรวจสอบว่าเป็นเบอร์โทรศัพท์หรือไม่
 */
function isValidPhone($phone) {
    return preg_match('/^[0-9\-\+\(\)\s]+$/', $phone);
}

/**
 * ฟังก์ชันสำหรับสร้าง URL
 */
function url($path = '') {
    return BASE_URL . '/' . ltrim($path, '/');
}

/**
 * ฟังก์ชันสำหรับสร้าง asset URL
 */
function asset($path = '') {
    return ASSETS_URL . '/' . ltrim($path, '/');
}
