<?php
/**
 * Payment Controller
 */

class PaymentController extends BaseController {
    private $paymentModel;
    private $invoiceModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->paymentModel = new Payment();
        $this->invoiceModel = new Invoice();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $payments = $this->paymentModel->getPaginated($page);
        $this->setPagination($payments);
        
        $this->data['payments'] = $payments['items'];
        $this->data['page_title'] = 'รายการการชำระเงิน';
        $this->view('payments/index');
    }
    
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $payment = $this->paymentModel->getById($id);
        if (!$payment) {
            $this->show404();
            return;
        }
        
        $this->data['payment'] = $payment;
        $this->data['page_title'] = 'รายละเอียดการชำระเงิน';
        $this->view('payments/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $invoices = $this->invoiceModel->getForDropdown();
        
        $this->data['invoices'] = $invoices;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'บันทึกการชำระเงินใหม่';
        $this->view('payments/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=payment&action=create');
            return;
        }
        
        $data = $this->post();
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->paymentModel->create($data);
        
        if ($id) {
            showAlert('บันทึกการชำระเงินเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=payment&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=payment&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $payment = $this->paymentModel->getById($id);
        if (!$payment) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $invoices = $this->invoiceModel->getForDropdown();
        
        $this->data['payment'] = $payment;
        $this->data['invoices'] = $invoices;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขการชำระเงิน';
        $this->view('payments/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=payment&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->paymentModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขการชำระเงินเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=payment&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=payment&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->paymentModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
