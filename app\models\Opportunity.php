<?php
/**
 * Opportunity Model
 * 
 * คลาสสำหรับจัดการข้อมูลโอกาสการขาย
 */

class Opportunity extends BaseModel {
    protected $table = 'opportunities';
    protected $fillable = [
        'name', 'customer_id', 'contact_id', 'expected_amount', 'probability', 
        'source', 'stage', 'expected_close_date', 'actual_close_date', 
        'status', 'user_id', 'created_at', 'updated_at'
    ];
    
    /**
     * คอนสตรักเตอร์
     */
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * สร้างโอกาสการขายใหม่
     * 
     * @param array $data ข้อมูลโอกาสการขาย
     * @return int|bool
     */
    public function create($data) {
        // เพิ่มวันที่สร้างและอัปเดต
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return parent::create($data);
    }
    
    /**
     * อัปเดตโอกาสการขาย
     * 
     * @param int $id รหัสโอกาสการขาย
     * @param array $data ข้อมูลที่ต้องการอัปเดต
     * @return bool
     */
    public function update($id, $data) {
        // อัปเดตวันที่แก้ไข
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return parent::update($id, $data);
    }
    
    /**
     * อัปเดตสถานะโอกาสการขาย
     * 
     * @param int $id รหัสโอกาสการขาย
     * @param string $stage ขั้นตอนการขาย
     * @param string $status สถานะโอกาสการขาย
     * @return bool
     */
    public function updateStage($id, $stage, $status = null) {
        $data = [
            'stage' => $stage,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // ถ้าเป็นขั้นตอนปิดการขาย ให้อัปเดตวันที่ปิดการขายด้วย
        if ($stage == STAGE_CLOSED_WON || $stage == STAGE_CLOSED_LOST) {
            $data['actual_close_date'] = date('Y-m-d');
            
            // อัปเดตสถานะถ้ามีการระบุ
            if ($status) {
                $data['status'] = $status;
            } else {
                $data['status'] = ($stage == STAGE_CLOSED_WON) ? OPPORTUNITY_STATUS_WON : OPPORTUNITY_STATUS_LOST;
            }
        }
        
        return parent::update($id, $data);
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param int $id รหัสโอกาสการขาย
     * @return array|bool
     */
    public function getOpportunityWithDetails($id) {
        $sql = "SELECT o.*, 
                c.name as customer_name, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON o.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายทั้งหมดพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @return array
     */
    public function getAllWithDetails() {
        $sql = "SELECT o.*, 
                c.name as customer_name, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON o.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายพร้อมข้อมูลกิจกรรม
     * 
     * @param int $id รหัสโอกาสการขาย
     * @return array
     */
    public function getOpportunityWithActivities($id) {
        // ดึงข้อมูลโอกาสการขาย
        $opportunity = $this->getOpportunityWithDetails($id);
        
        if (!$opportunity) {
            return false;
        }
        
        // ดึงข้อมูลกิจกรรม
        $sql = "SELECT a.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "activities a 
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.user_id = u.id 
                WHERE a.opportunity_id = :opportunity_id 
                ORDER BY a.activity_date DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':opportunity_id' => $id]);
        $this->db->execute();
        
        $activities = $this->db->fetchAll();
        
        // รวมข้อมูล
        $opportunity['activities'] = $activities;
        
        return $opportunity;
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายพร้อมข้อมูลใบเสนอราคา
     * 
     * @param int $id รหัสโอกาสการขาย
     * @return array
     */
    public function getOpportunityWithQuotations($id) {
        // ดึงข้อมูลโอกาสการขาย
        $opportunity = $this->getOpportunityWithDetails($id);
        
        if (!$opportunity) {
            return false;
        }
        
        // ดึงข้อมูลใบเสนอราคา
        $sql = "SELECT q.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "quotations q 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.opportunity_id = :opportunity_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':opportunity_id' => $id]);
        $this->db->execute();
        
        $quotations = $this->db->fetchAll();
        
        // รวมข้อมูล
        $opportunity['quotations'] = $quotations;
        
        return $opportunity;
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายพร้อมข้อมูลทั้งหมด
     * 
     * @param int $id รหัสโอกาสการขาย
     * @return array
     */
    public function getOpportunityWithAllDetails($id) {
        // ดึงข้อมูลโอกาสการขาย
        $opportunity = $this->getOpportunityWithDetails($id);
        
        if (!$opportunity) {
            return false;
        }
        
        // ดึงข้อมูลกิจกรรม
        $sql = "SELECT a.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "activities a 
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.user_id = u.id 
                WHERE a.opportunity_id = :opportunity_id 
                ORDER BY a.activity_date DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':opportunity_id' => $id]);
        $this->db->execute();
        
        $activities = $this->db->fetchAll();
        
        // ดึงข้อมูลใบเสนอราคา
        $sql = "SELECT q.*, CONCAT(u.first_name, ' ', u.last_name) as created_by_name 
                FROM " . TABLE_PREFIX . "quotations q 
                LEFT JOIN " . TABLE_PREFIX . "users u ON q.user_id = u.id 
                WHERE q.opportunity_id = :opportunity_id 
                ORDER BY q.created_at DESC";
        
        $this->db->prepare($sql);
        $this->db->bind([':opportunity_id' => $id]);
        $this->db->execute();
        
        $quotations = $this->db->fetchAll();
        
        // รวมข้อมูล
        $opportunity['activities'] = $activities;
        $opportunity['quotations'] = $quotations;
        
        return $opportunity;
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายตามลูกค้า
     * 
     * @param int $customerId รหัสลูกค้า
     * @return array
     */
    public function getOpportunitiesByCustomer($customerId) {
        $sql = "SELECT o.*, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.customer_id = :customer_id 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([':customer_id' => $customerId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายตามผู้รับผิดชอบ
     * 
     * @param int $userId รหัสผู้ใช้
     * @return array
     */
    public function getOpportunitiesByUser($userId) {
        $sql = "SELECT o.*, 
                c.name as customer_name, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON o.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                WHERE o.user_id = :user_id 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([':user_id' => $userId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายตามขั้นตอนการขาย
     * 
     * @param string $stage ขั้นตอนการขาย
     * @return array
     */
    public function getOpportunitiesByStage($stage) {
        $sql = "SELECT o.*, 
                c.name as customer_name, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON o.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.stage = :stage 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([':stage' => $stage]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายตามสถานะ
     * 
     * @param string $status สถานะโอกาสการขาย
     * @return array
     */
    public function getOpportunitiesByStatus($status) {
        $sql = "SELECT o.*, 
                c.name as customer_name, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON o.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.status = :status 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([':status' => $status]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลโอกาสการขายที่คาดว่าจะปิดในช่วงเวลาที่กำหนด
     * 
     * @param string $startDate วันที่เริ่มต้น
     * @param string $endDate วันที่สิ้นสุด
     * @return array
     */
    public function getOpportunitiesByExpectedCloseDate($startDate, $endDate) {
        $sql = "SELECT o.*, 
                c.name as customer_name, 
                CONCAT(co.first_name, ' ', co.last_name) as contact_name,
                CONCAT(u.first_name, ' ', u.last_name) as assigned_to_name
                FROM " . TABLE_PREFIX . $this->table . " o 
                LEFT JOIN " . TABLE_PREFIX . "customers c ON o.customer_id = c.id 
                LEFT JOIN " . TABLE_PREFIX . "contacts co ON o.contact_id = co.id 
                LEFT JOIN " . TABLE_PREFIX . "users u ON o.user_id = u.id 
                WHERE o.expected_close_date BETWEEN :start_date AND :end_date 
                ORDER BY o.expected_close_date";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * นับจำนวนโอกาสการขายตามขั้นตอนการขาย
     * 
     * @return array
     */
    public function countOpportunitiesByStage() {
        $sql = "SELECT stage, COUNT(*) as count, SUM(expected_amount) as total_amount 
                FROM " . TABLE_PREFIX . $this->table . " 
                GROUP BY stage";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * นับจำนวนโอกาสการขายตามสถานะ
     * 
     * @return array
     */
    public function countOpportunitiesByStatus() {
        $sql = "SELECT status, COUNT(*) as count, SUM(expected_amount) as total_amount 
                FROM " . TABLE_PREFIX . $this->table . " 
                GROUP BY status";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * คำนวณมูลค่าโอกาสการขายทั้งหมด
     * 
     * @return float
     */
    public function calculateTotalOpportunityValue() {
        $sql = "SELECT SUM(expected_amount) as total_amount FROM " . TABLE_PREFIX . $this->table;
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าโอกาสการขายตามขั้นตอนการขาย
     * 
     * @param string $stage ขั้นตอนการขาย
     * @return float
     */
    public function calculateOpportunityValueByStage($stage) {
        $sql = "SELECT SUM(expected_amount) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE stage = :stage";
        
        $this->db->prepare($sql);
        $this->db->bind([':stage' => $stage]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าโอกาสการขายตามสถานะ
     * 
     * @param string $status สถานะโอกาสการขาย
     * @return float
     */
    public function calculateOpportunityValueByStatus($status) {
        $sql = "SELECT SUM(expected_amount) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE status = :status";
        
        $this->db->prepare($sql);
        $this->db->bind([':status' => $status]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าโอกาสการขายตามผู้รับผิดชอบ
     * 
     * @param int $userId รหัสผู้ใช้
     * @return float
     */
    public function calculateOpportunityValueByUser($userId) {
        $sql = "SELECT SUM(expected_amount) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE user_id = :user_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':user_id' => $userId]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าโอกาสการขายตามช่วงเวลา
     * 
     * @param string $startDate วันที่เริ่มต้น
     * @param string $endDate วันที่สิ้นสุด
     * @return float
     */
    public function calculateOpportunityValueByDateRange($startDate, $endDate) {
        $sql = "SELECT SUM(expected_amount) as total_amount FROM " . TABLE_PREFIX . $this->table . " WHERE expected_close_date BETWEEN :start_date AND :end_date";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['total_amount'] ? $result['total_amount'] : 0;
    }
    
    /**
     * คำนวณมูลค่าโอกาสการขายถ่วงน้ำหนักตามความน่าจะเป็น
     * 
     * @return float
     */
    public function calculateWeightedOpportunityValue() {
        $sql = "SELECT SUM(expected_amount * probability / 100) as weighted_amount FROM " . TABLE_PREFIX . $this->table;
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['weighted_amount'] ? $result['weighted_amount'] : 0;
    }
    
    /**
     * ค้นหาโอกาสการขาย
     * 
     * @param string $keyword คำค้นหา
     * @return array
     */
    public function searchOpportunities($keyword) {
        return $this->search($keyword, ['name', 'source']);
    }
}

