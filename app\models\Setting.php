<?php
/**
 * Setting Model
 * 
 * โมเดลสำหรับจัดการข้อมูลการตั้งค่าระบบ
 */

class Setting extends BaseModel {
    protected $table = 'settings';
    protected $primaryKey = 'id';
    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'description',
        'is_public',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงค่าการตั้งค่าตาม key
     * 
     * @param string $key คีย์ของการตั้งค่า
     * @param mixed $default ค่าเริ่มต้น
     * @return mixed
     */
    public function getValue($key, $default = null) {
        $setting = $this->getOneWhere(['key' => $key]);
        
        if (!$setting) {
            return $default;
        }
        
        return $this->castValue($setting['value'], $setting['type']);
    }
    
    /**
     * ตั้งค่าใหม่หรืออัปเดตค่าที่มีอยู่
     * 
     * @param string $key คีย์ของการตั้งค่า
     * @param mixed $value ค่าที่ต้องการตั้ง
     * @param string $type ประเภทข้อมูล
     * @param string $group กลุ่มการตั้งค่า
     * @param string $description คำอธิบาย
     * @param bool $isPublic เป็นการตั้งค่าสาธารณะหรือไม่
     * @return bool
     */
    public function setValue($key, $value, $type = 'string', $group = 'general', $description = '', $isPublic = false) {
        $existing = $this->getOneWhere(['key' => $key]);
        
        $data = [
            'key' => $key,
            'value' => $this->prepareValue($value, $type),
            'type' => $type,
            'group' => $group,
            'description' => $description,
            'is_public' => $isPublic ? 1 : 0
        ];
        
        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            $data['created_at'] = date('Y-m-d H:i:s');
            return $this->create($data) !== false;
        }
    }
    
    /**
     * ดึงการตั้งค่าทั้งหมดในกลุ่ม
     * 
     * @param string $group กลุ่มการตั้งค่า
     * @return array
     */
    public function getByGroup($group) {
        $settings = $this->getWhere(['group' => $group]);
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting['key']] = $this->castValue($setting['value'], $setting['type']);
        }
        
        return $result;
    }
    
    /**
     * ดึงการตั้งค่าสาธารณะทั้งหมด
     * 
     * @return array
     */
    public function getPublicSettings() {
        $settings = $this->getWhere(['is_public' => 1]);
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting['key']] = $this->castValue($setting['value'], $setting['type']);
        }
        
        return $result;
    }
    
    /**
     * ดึงการตั้งค่าทั้งหมด
     * 
     * @return array
     */
    public function getAllSettings() {
        $settings = $this->getAll();
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting['key']] = [
                'value' => $this->castValue($setting['value'], $setting['type']),
                'type' => $setting['type'],
                'group' => $setting['group'],
                'description' => $setting['description'],
                'is_public' => $setting['is_public']
            ];
        }
        
        return $result;
    }
    
    /**
     * ลบการตั้งค่า
     * 
     * @param string $key คีย์ของการตั้งค่า
     * @return bool
     */
    public function deleteSetting($key) {
        $setting = $this->getOneWhere(['key' => $key]);
        
        if ($setting) {
            return $this->delete($setting['id']);
        }
        
        return false;
    }
    
    /**
     * อัปเดตการตั้งค่าหลายรายการ
     * 
     * @param array $settings รายการการตั้งค่า
     * @return bool
     */
    public function updateMultiple($settings) {
        $success = true;
        
        foreach ($settings as $key => $value) {
            $existing = $this->getOneWhere(['key' => $key]);
            
            if ($existing) {
                $type = $existing['type'];
                $preparedValue = $this->prepareValue($value, $type);
                
                if (!$this->update($existing['id'], ['value' => $preparedValue])) {
                    $success = false;
                }
            }
        }
        
        return $success;
    }
    
    /**
     * เตรียมค่าสำหรับบันทึกในฐานข้อมูล
     * 
     * @param mixed $value ค่าที่ต้องการเตรียม
     * @param string $type ประเภทข้อมูล
     * @return string
     */
    private function prepareValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'array':
            case 'object':
                return json_encode($value);
            case 'integer':
                return (string) intval($value);
            case 'float':
                return (string) floatval($value);
            default:
                return (string) $value;
        }
    }
    
    /**
     * แปลงค่าจากฐานข้อมูลเป็นประเภทที่ถูกต้อง
     * 
     * @param string $value ค่าจากฐานข้อมูล
     * @param string $type ประเภทข้อมูล
     * @return mixed
     */
    private function castValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return $value === '1' || $value === 'true';
            case 'integer':
                return intval($value);
            case 'float':
                return floatval($value);
            case 'array':
            case 'object':
                return json_decode($value, true);
            default:
                return $value;
        }
    }
    
    /**
     * ดึงการตั้งค่าเริ่มต้นของระบบ
     * 
     * @return array
     */
    public function getDefaultSettings() {
        return [
            // การตั้งค่าทั่วไป
            'company_name' => [
                'value' => 'บริษัท ระบบติดตามงานขาย จำกัด',
                'type' => 'string',
                'group' => 'company',
                'description' => 'ชื่อบริษัท',
                'is_public' => true
            ],
            'company_address' => [
                'value' => '123 ถนนสุขุมวิท แขวงคลองตัน เขตคลองตัน กรุงเทพฯ 10110',
                'type' => 'string',
                'group' => 'company',
                'description' => 'ที่อยู่บริษัท',
                'is_public' => true
            ],
            'company_phone' => [
                'value' => '02-123-4567',
                'type' => 'string',
                'group' => 'company',
                'description' => 'เบอร์โทรศัพท์บริษัท',
                'is_public' => true
            ],
            'company_email' => [
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'company',
                'description' => 'อีเมลบริษัท',
                'is_public' => true
            ],
            'company_website' => [
                'value' => 'https://www.company.com',
                'type' => 'string',
                'group' => 'company',
                'description' => 'เว็บไซต์บริษัท',
                'is_public' => true
            ],
            'company_logo' => [
                'value' => '',
                'type' => 'string',
                'group' => 'company',
                'description' => 'โลโก้บริษัท',
                'is_public' => true
            ],
            
            // การตั้งค่าระบบ
            'system_timezone' => [
                'value' => 'Asia/Bangkok',
                'type' => 'string',
                'group' => 'system',
                'description' => 'เขตเวลาของระบบ',
                'is_public' => false
            ],
            'system_language' => [
                'value' => 'th',
                'type' => 'string',
                'group' => 'system',
                'description' => 'ภาษาของระบบ',
                'is_public' => true
            ],
            'items_per_page' => [
                'value' => 10,
                'type' => 'integer',
                'group' => 'system',
                'description' => 'จำนวนรายการต่อหน้า',
                'is_public' => false
            ],
            
            // การตั้งค่าการขาย
            'default_currency' => [
                'value' => 'THB',
                'type' => 'string',
                'group' => 'sales',
                'description' => 'สกุลเงินเริ่มต้น',
                'is_public' => true
            ],
            'vat_rate' => [
                'value' => 7,
                'type' => 'float',
                'group' => 'sales',
                'description' => 'อัตราภาษีมูลค่าเพิ่ม (%)',
                'is_public' => true
            ],
            'quotation_validity_days' => [
                'value' => 30,
                'type' => 'integer',
                'group' => 'sales',
                'description' => 'จำนวนวันที่ใบเสนอราคามีผล',
                'is_public' => false
            ],
            'invoice_payment_terms' => [
                'value' => 30,
                'type' => 'integer',
                'group' => 'sales',
                'description' => 'เงื่อนไขการชำระเงิน (วัน)',
                'is_public' => false
            ],
            
            // การตั้งค่าการแจ้งเตือน
            'notification_email_enabled' => [
                'value' => true,
                'type' => 'boolean',
                'group' => 'notification',
                'description' => 'เปิดใช้งานการแจ้งเตือนทางอีเมล',
                'is_public' => false
            ],
            'notification_activity_reminder' => [
                'value' => true,
                'type' => 'boolean',
                'group' => 'notification',
                'description' => 'เปิดใช้งานการเตือนกิจกรรม',
                'is_public' => false
            ],
            'notification_overdue_invoice' => [
                'value' => true,
                'type' => 'boolean',
                'group' => 'notification',
                'description' => 'เปิดใช้งานการเตือนใบแจ้งหนี้เลยกำหนด',
                'is_public' => false
            ]
        ];
    }
    
    /**
     * สร้างการตั้งค่าเริ่มต้น
     * 
     * @return bool
     */
    public function createDefaultSettings() {
        $defaultSettings = $this->getDefaultSettings();
        $success = true;
        
        foreach ($defaultSettings as $key => $setting) {
            if (!$this->exists(['key' => $key])) {
                $data = [
                    'key' => $key,
                    'value' => $this->prepareValue($setting['value'], $setting['type']),
                    'type' => $setting['type'],
                    'group' => $setting['group'],
                    'description' => $setting['description'],
                    'is_public' => $setting['is_public'] ? 1 : 0,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                if (!$this->create($data)) {
                    $success = false;
                }
            }
        }
        
        return $success;
    }
}
