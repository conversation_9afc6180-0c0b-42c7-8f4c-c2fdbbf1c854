<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ด | <?= $app_name ?></title>
    <link href="<?= $assets_url ?>/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?= $assets_url ?>/css/style.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Sarabun', sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stat-card .card-body {
            padding: 2rem;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        .welcome-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            margin-bottom: 2rem;
        }
        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: #333;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #eee;
        }
        .sidebar .nav-link:hover {
            background-color: #f8f9fa;
            color: #667eea;
        }
        .sidebar .nav-link.active {
            background-color: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php?controller=dashboard">
                <i class="fas fa-chart-line me-2"></i><?= $app_name ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= $user['first_name'] . ' ' . $user['last_name'] ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="index.php?controller=user&action=profile">โปรไฟล์</a></li>
                            <li><a class="dropdown-item" href="index.php?controller=setting">การตั้งค่า</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="index.php?controller=auth&action=logout">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="index.php?controller=dashboard">
                                <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=customer">
                                <i class="fas fa-users me-2"></i>ลูกค้า
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=contact">
                                <i class="fas fa-address-book me-2"></i>ผู้ติดต่อ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=opportunity">
                                <i class="fas fa-bullseye me-2"></i>โอกาสการขาย
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=quotation">
                                <i class="fas fa-file-invoice me-2"></i>ใบเสนอราคา
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=invoice">
                                <i class="fas fa-receipt me-2"></i>ใบแจ้งหนี้
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=payment">
                                <i class="fas fa-credit-card me-2"></i>การชำระเงิน
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php?controller=report">
                                <i class="fas fa-chart-bar me-2"></i>รายงาน
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <div class="container-fluid py-4">
                    <!-- Welcome Card -->
                    <div class="card welcome-card">
                        <div class="card-body">
                            <h3><i class="fas fa-hand-wave me-2"></i>สวัสดี, <?= $user['first_name'] ?>!</h3>
                            <p class="mb-0">ยินดีต้อนรับสู่ระบบติดตามงานขายและใบเสนอราคา</p>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <div class="stat-number"><?= isset($stats['customers']['total']) ? $stats['customers']['total'] : '0' ?></div>
                                    <div class="stat-label">ลูกค้าทั้งหมด</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <div class="stat-number"><?= isset($stats['opportunities']['total']) ? $stats['opportunities']['total'] : '0' ?></div>
                                    <div class="stat-label">โอกาสการขาย</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <div class="stat-number"><?= isset($stats['quotations']['total']) ? $stats['quotations']['total'] : '0' ?></div>
                                    <div class="stat-label">ใบเสนอราคา</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="card stat-card">
                                <div class="card-body text-center">
                                    <div class="stat-number"><?= isset($stats['revenue']['monthly']) ? number_format($stats['revenue']['monthly']) : '0' ?></div>
                                    <div class="stat-label">รายได้เดือนนี้ (บาท)</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activities -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-clock me-2"></i>กิจกรรมล่าสุด</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (isset($recent_activities) && !empty($recent_activities)): ?>
                                        <div class="list-group list-group-flush">
                                            <?php foreach (array_slice($recent_activities, 0, 5) as $activity): ?>
                                                <div class="list-group-item border-0 px-0">
                                                    <div class="d-flex justify-content-between">
                                                        <div>
                                                            <h6 class="mb-1"><?= htmlspecialchars($activity['subject']) ?></h6>
                                                            <p class="mb-1 text-muted small"><?= htmlspecialchars($activity['company_name']) ?></p>
                                                        </div>
                                                        <small class="text-muted"><?= date('d/m/Y', strtotime($activity['scheduled_date'])) ?></small>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted text-center py-3">ไม่มีกิจกรรมล่าสุด</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-bell me-2"></i>การแจ้งเตือน</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (isset($notifications) && !empty($notifications)): ?>
                                        <div class="list-group list-group-flush">
                                            <?php foreach (array_slice($notifications, 0, 5) as $notification): ?>
                                                <div class="list-group-item border-0 px-0">
                                                    <div class="d-flex justify-content-between">
                                                        <div>
                                                            <h6 class="mb-1"><?= htmlspecialchars($notification['title']) ?></h6>
                                                            <p class="mb-1 text-muted small"><?= htmlspecialchars($notification['message']) ?></p>
                                                        </div>
                                                        <small class="text-muted"><?= date('d/m/Y H:i', strtotime($notification['created_at'])) ?></small>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <p class="text-muted text-center py-3">ไม่มีการแจ้งเตือนใหม่</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="<?= $assets_url ?>/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome -->
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
