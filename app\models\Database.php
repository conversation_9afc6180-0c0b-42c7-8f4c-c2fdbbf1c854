<?php
/**
 * Database Class
 * 
 * คลาสสำหรับการเชื่อมต่อฐานข้อมูล
 */

class Database {
    private static $instance = null;
    private $connection;
    private $statement;
    
    /**
     * คอนสตรักเตอร์
     */
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, PDO_OPTIONS);
        } catch (PDOException $e) {
            $this->logError($e->getMessage());
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * ป้องกันการโคลนอ็อบเจ็กต์
     */
    private function __clone() {}
    
    /**
     * รับอินสแตนซ์ของคลาส (Singleton pattern)
     * 
     * @return Database
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * เตรียมคำสั่ง SQL
     * 
     * @param string $sql คำสั่ง SQL
     * @return Database
     */
    public function prepare($sql) {
        $this->statement = $this->connection->prepare($sql);
        return $this;
    }
    
    /**
     * ผูกค่าพารามิเตอร์
     * 
     * @param array $params พารามิเตอร์
     * @return Database
     */
    public function bind($params) {
        if (!empty($params)) {
            foreach ($params as $key => $value) {
                $this->statement->bindValue($key, $value);
            }
        }
        return $this;
    }
    
    /**
     * ประมวลผลคำสั่ง SQL
     * 
     * @return bool
     */
    public function execute() {
        try {
            return $this->statement->execute();
        } catch (PDOException $e) {
            $this->logError($e->getMessage());
            return false;
        }
    }
    
    /**
     * ดึงข้อมูลแถวเดียว
     * 
     * @return array|bool
     */
    public function fetch() {
        return $this->statement->fetch();
    }
    
    /**
     * ดึงข้อมูลหลายแถว
     * 
     * @return array|bool
     */
    public function fetchAll() {
        return $this->statement->fetchAll();
    }
    
    /**
     * นับจำนวนแถวที่ได้จากการคิวรี
     * 
     * @return int
     */
    public function rowCount() {
        return $this->statement->rowCount();
    }
    
    /**
     * รับ ID ล่าสุดที่เพิ่มเข้าไป
     * 
     * @return string
     */
    public function lastInsertId() {
        return $this->connection->lastInsertId();
    }
    
    /**
     * เริ่มต้น Transaction
     * 
     * @return bool
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * ยืนยัน Transaction
     * 
     * @return bool
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * ยกเลิก Transaction
     * 
     * @return bool
     */
    public function rollBack() {
        return $this->connection->rollBack();
    }
    
    /**
     * บันทึกข้อผิดพลาด
     * 
     * @param string $message ข้อความข้อผิดพลาด
     * @return void
     */
    private function logError($message) {
        if (LOG_ERRORS) {
            $logMessage = date('Y-m-d H:i:s') . ' - Database Error: ' . $message . PHP_EOL;
            error_log($logMessage, 3, ERROR_LOG_FILE);
        }
    }
    
    /**
     * ปิดการเชื่อมต่อฐานข้อมูล
     * 
     * @return void
     */
    public function close() {
        $this->connection = null;
        self::$instance = null;
    }
}

