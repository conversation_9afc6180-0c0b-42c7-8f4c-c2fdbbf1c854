<?php
/**
 * Auth Controller
 * 
 * คอนโทรลเลอร์สำหรับจัดการการเข้าสู่ระบบและออกจากระบบ
 */

class AuthController extends BaseController {
    private $userModel;
    
    public function __construct() {
        parent::__construct();
        $this->userModel = new User();
    }
    
    /**
     * แสดงหน้าเข้าสู่ระบบ
     */
    public function login() {
        // ถ้าเข้าสู่ระบบแล้ว ให้เปลี่ยนเส้นทางไปยังแดชบอร์ด
        if ($this->user) {
            $this->redirect('index.php?controller=dashboard');
        }
        
        // ถ้าเป็น POST request ให้ทำการเข้าสู่ระบบ
        if ($this->isMethod('POST')) {
            $this->processLogin();
            return;
        }
        
        // แสดงหน้าเข้าสู่ระบบ
        $this->data['csrf_token'] = generateCSRFToken();
        $this->view('auth/login');
    }
    
    /**
     * ประมวลผลการเข้าสู่ระบบ
     */
    private function processLogin() {
        // ตรวจสอบ CSRF token
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $username = $this->post('username');
        $password = $this->post('password');
        $remember = $this->post('remember');
        
        // ตรวจสอบข้อมูลที่จำเป็น
        $errors = $this->validateRequired(['username', 'password']);
        
        if (!empty($errors)) {
            showAlert('กรุณากรอกชื่อผู้ใช้และรหัสผ่าน', 'error');
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        // ตรวจสอบข้อมูลผู้ใช้
        $user = $this->userModel->getUserByUsername($username);
        
        if (!$user) {
            showAlert('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง', 'error');
            $this->logActivity('login_failed', 'Failed login attempt', ['username' => $username]);
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        // ตรวจสอบสถานะผู้ใช้
        if ($user['status'] !== USER_STATUS_ACTIVE) {
            showAlert('บัญชีผู้ใช้ถูกระงับ กรุณาติดต่อผู้ดูแลระบบ', 'error');
            $this->logActivity('login_failed', 'Login attempt with inactive account', ['username' => $username]);
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        // ตรวจสอบรหัสผ่าน
        if (!password_verify($password, $user['password'])) {
            showAlert('ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง', 'error');
            $this->logActivity('login_failed', 'Failed login attempt - wrong password', ['username' => $username]);
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        // เข้าสู่ระบบสำเร็จ
        $this->createUserSession($user);
        
        // อัปเดตข้อมูลการเข้าสู่ระบบล่าสุด
        $this->userModel->updateLastLogin($user['id']);
        
        // บันทึกล็อก
        $this->logActivity('login_success', 'User logged in successfully', ['user_id' => $user['id']]);
        
        // ตั้งค่า Remember Me (ถ้าเลือก)
        if ($remember) {
            $this->setRememberToken($user['id']);
        }
        
        showAlert('เข้าสู่ระบบสำเร็จ', 'success');
        
        // เปลี่ยนเส้นทางไปยังหน้าที่ต้องการหรือแดชบอร์ด
        $redirectUrl = $this->get('redirect') ?: 'index.php?controller=dashboard';
        $this->redirect($redirectUrl);
    }
    
    /**
     * สร้าง session สำหรับผู้ใช้
     * 
     * @param array $user ข้อมูลผู้ใช้
     */
    private function createUserSession($user) {
        // ลบข้อมูลที่ไม่จำเป็นออกจาก session
        unset($user['password']);
        
        $_SESSION['user'] = $user;
        $_SESSION['login_time'] = time();
        
        // ตั้งค่า session security
        session_regenerate_id(true);
    }
    
    /**
     * ตั้งค่า Remember Me token
     * 
     * @param int $userId ID ของผู้ใช้
     */
    private function setRememberToken($userId) {
        $token = bin2hex(random_bytes(32));
        $expiry = time() + (30 * 24 * 60 * 60); // 30 วัน
        
        // บันทึก token ในฐานข้อมูล
        $this->userModel->setRememberToken($userId, $token, date('Y-m-d H:i:s', $expiry));
        
        // ตั้งค่า cookie
        setcookie('remember_token', $token, $expiry, '/', '', false, true);
    }
    
    /**
     * ออกจากระบบ
     */
    public function logout() {
        if ($this->user) {
            // บันทึกล็อก
            $this->logActivity('logout', 'User logged out', ['user_id' => $this->user['id']]);
            
            // ลบ Remember Me token
            $this->clearRememberToken();
        }
        
        // ลบ session
        session_destroy();
        
        // ลบ cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }
        
        showAlert('ออกจากระบบเรียบร้อยแล้ว', 'success');
        $this->redirect('index.php?controller=auth&action=login');
    }
    
    /**
     * ลบ Remember Me token
     */
    private function clearRememberToken() {
        if ($this->user) {
            $this->userModel->clearRememberToken($this->user['id']);
        }
    }
    
    /**
     * แสดงหน้าลืมรหัสผ่าน
     */
    public function forgotPassword() {
        if ($this->isMethod('POST')) {
            $this->processForgotPassword();
            return;
        }
        
        $this->data['csrf_token'] = generateCSRFToken();
        $this->view('auth/forgot_password');
    }
    
    /**
     * ประมวลผลการลืมรหัสผ่าน
     */
    private function processForgotPassword() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=auth&action=forgotPassword');
            return;
        }
        
        $email = $this->post('email');
        
        if (empty($email)) {
            showAlert('กรุณากรอกอีเมล', 'error');
            $this->redirect('index.php?controller=auth&action=forgotPassword');
            return;
        }
        
        if (!isValidEmail($email)) {
            showAlert('รูปแบบอีเมลไม่ถูกต้อง', 'error');
            $this->redirect('index.php?controller=auth&action=forgotPassword');
            return;
        }
        
        $user = $this->userModel->getUserByEmail($email);
        
        if (!$user) {
            showAlert('ไม่พบอีเมลในระบบ', 'error');
            $this->redirect('index.php?controller=auth&action=forgotPassword');
            return;
        }
        
        // สร้าง reset token
        $resetToken = bin2hex(random_bytes(32));
        $expiry = date('Y-m-d H:i:s', time() + 3600); // 1 ชั่วโมง
        
        $this->userModel->setPasswordResetToken($user['id'], $resetToken, $expiry);
        
        // ส่งอีเมลรีเซ็ตรหัสผ่าน (ในที่นี้จะแสดงข้อความแทน)
        $resetUrl = BASE_URL . "/index.php?controller=auth&action=resetPassword&token=" . $resetToken;
        
        // TODO: ส่งอีเมลจริง
        showAlert('ลิงก์รีเซ็ตรหัสผ่านถูกส่งไปยังอีเมลของคุณแล้ว', 'success');
        
        $this->logActivity('password_reset_requested', 'Password reset requested', ['user_id' => $user['id']]);
        
        $this->redirect('index.php?controller=auth&action=login');
    }
    
    /**
     * แสดงหน้ารีเซ็ตรหัสผ่าน
     */
    public function resetPassword() {
        $token = $this->get('token');
        
        if (empty($token)) {
            showAlert('Token ไม่ถูกต้อง', 'error');
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        // ตรวจสอบ token
        $user = $this->userModel->getUserByResetToken($token);
        
        if (!$user) {
            showAlert('Token ไม่ถูกต้องหรือหมดอายุแล้ว', 'error');
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processResetPassword($user);
            return;
        }
        
        $this->data['token'] = $token;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->view('auth/reset_password');
    }
    
    /**
     * ประมวลผลการรีเซ็ตรหัสผ่าน
     */
    private function processResetPassword($user) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $password = $this->post('password');
        $confirmPassword = $this->post('confirm_password');
        
        if (empty($password) || empty($confirmPassword)) {
            showAlert('กรุณากรอกรหัสผ่านและยืนยันรหัสผ่าน', 'error');
            $this->redirect('index.php?controller=auth&action=resetPassword&token=' . $this->post('token'));
            return;
        }
        
        if ($password !== $confirmPassword) {
            showAlert('รหัสผ่านและยืนยันรหัสผ่านไม่ตรงกัน', 'error');
            $this->redirect('index.php?controller=auth&action=resetPassword&token=' . $this->post('token'));
            return;
        }
        
        if (strlen($password) < 6) {
            showAlert('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร', 'error');
            $this->redirect('index.php?controller=auth&action=resetPassword&token=' . $this->post('token'));
            return;
        }
        
        // อัปเดตรหัสผ่าน
        $hashedPassword = password_hash($password, PASSWORD_HASH_ALGO, ['cost' => PASSWORD_HASH_COST]);
        $this->userModel->updatePassword($user['id'], $hashedPassword);
        
        // ลบ reset token
        $this->userModel->clearPasswordResetToken($user['id']);
        
        $this->logActivity('password_reset_completed', 'Password reset completed', ['user_id' => $user['id']]);
        
        showAlert('รีเซ็ตรหัสผ่านเรียบร้อยแล้ว กรุณาเข้าสู่ระบบด้วยรหัสผ่านใหม่', 'success');
        $this->redirect('index.php?controller=auth&action=login');
    }
}
