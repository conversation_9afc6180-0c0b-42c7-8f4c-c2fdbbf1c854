<?php
/**
 * Form Helper
 * 
 * คลาสช่วยเหลือสำหรับการสร้างฟอร์ม HTML
 */

class FormHelper {
    
    /**
     * สร้าง input field
     * 
     * @param string $type ประเภท input
     * @param string $name ชื่อ field
     * @param mixed $value ค่าเริ่มต้น
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function input($type, $name, $value = '', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        
        return "<input type=\"{$type}\" name=\"{$name}\" value=\"{$value}\" {$attrs}>";
    }
    
    /**
     * สร้าง text input
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่าเริ่มต้น
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function text($name, $value = '', $attributes = []) {
        return self::input('text', $name, $value, $attributes);
    }
    
    /**
     * สร้าง email input
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่าเริ่มต้น
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function email($name, $value = '', $attributes = []) {
        return self::input('email', $name, $value, $attributes);
    }
    
    /**
     * สร้าง password input
     * 
     * @param string $name ชื่อ field
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function password($name, $attributes = []) {
        return self::input('password', $name, '', $attributes);
    }
    
    /**
     * สร้าง hidden input
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่า
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function hidden($name, $value = '', $attributes = []) {
        return self::input('hidden', $name, $value, $attributes);
    }
    
    /**
     * สร้าง number input
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่าเริ่มต้น
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function number($name, $value = '', $attributes = []) {
        return self::input('number', $name, $value, $attributes);
    }
    
    /**
     * สร้าง date input
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่าเริ่มต้น
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function date($name, $value = '', $attributes = []) {
        return self::input('date', $name, $value, $attributes);
    }
    
    /**
     * สร้าง textarea
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่าเริ่มต้น
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function textarea($name, $value = '', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        
        return "<textarea name=\"{$name}\" {$attrs}>{$value}</textarea>";
    }
    
    /**
     * สร้าง select dropdown
     * 
     * @param string $name ชื่อ field
     * @param array $options ตัวเลือก
     * @param mixed $selected ค่าที่เลือก
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function select($name, $options = [], $selected = '', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $html = "<select name=\"{$name}\" {$attrs}>";
        
        foreach ($options as $value => $text) {
            $selectedAttr = ($value == $selected) ? ' selected' : '';
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
            $html .= "<option value=\"{$value}\"{$selectedAttr}>{$text}</option>";
        }
        
        $html .= "</select>";
        return $html;
    }
    
    /**
     * สร้าง checkbox
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่า
     * @param bool $checked เลือกหรือไม่
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function checkbox($name, $value = '1', $checked = false, $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $checkedAttr = $checked ? ' checked' : '';
        $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        
        return "<input type=\"checkbox\" name=\"{$name}\" value=\"{$value}\"{$checkedAttr} {$attrs}>";
    }
    
    /**
     * สร้าง radio button
     * 
     * @param string $name ชื่อ field
     * @param mixed $value ค่า
     * @param bool $checked เลือกหรือไม่
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function radio($name, $value, $checked = false, $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $checkedAttr = $checked ? ' checked' : '';
        $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
        
        return "<input type=\"radio\" name=\"{$name}\" value=\"{$value}\"{$checkedAttr} {$attrs}>";
    }
    
    /**
     * สร้าง file input
     * 
     * @param string $name ชื่อ field
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function file($name, $attributes = []) {
        return self::input('file', $name, '', $attributes);
    }
    
    /**
     * สร้าง submit button
     * 
     * @param string $text ข้อความบนปุ่ม
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function submit($text = 'บันทึก', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
        
        return "<input type=\"submit\" value=\"{$text}\" {$attrs}>";
    }
    
    /**
     * สร้าง button
     * 
     * @param string $text ข้อความบนปุ่ม
     * @param string $type ประเภทปุ่ม
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function button($text, $type = 'button', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
        
        return "<button type=\"{$type}\" {$attrs}>{$text}</button>";
    }
    
    /**
     * สร้าง label
     * 
     * @param string $for ชื่อ field ที่เชื่อมโยง
     * @param string $text ข้อความ label
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function label($for, $text, $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
        
        return "<label for=\"{$for}\" {$attrs}>{$text}</label>";
    }
    
    /**
     * สร้าง CSRF token field
     * 
     * @return string
     */
    public static function csrfToken() {
        $token = generateCSRFToken();
        return self::hidden('csrf_token', $token);
    }
    
    /**
     * สร้าง form opening tag
     * 
     * @param string $action URL action
     * @param string $method HTTP method
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function open($action = '', $method = 'POST', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $method = strtoupper($method);
        
        return "<form action=\"{$action}\" method=\"{$method}\" {$attrs}>";
    }
    
    /**
     * สร้าง form closing tag
     * 
     * @return string
     */
    public static function close() {
        return "</form>";
    }
    
    /**
     * สร้าง Bootstrap form group
     * 
     * @param string $label ข้อความ label
     * @param string $input HTML input
     * @param string $error ข้อความข้อผิดพลาด
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function group($label, $input, $error = '', $attributes = []) {
        $attrs = self::buildAttributes($attributes);
        $errorClass = $error ? ' has-error' : '';
        $errorHtml = $error ? "<div class=\"text-danger small mt-1\">{$error}</div>" : '';
        
        return "<div class=\"form-group{$errorClass}\" {$attrs}>
                    <label class=\"form-label\">{$label}</label>
                    {$input}
                    {$errorHtml}
                </div>";
    }
    
    /**
     * สร้าง Bootstrap input group
     * 
     * @param string $input HTML input
     * @param string $prepend ข้อความหน้า input
     * @param string $append ข้อความหลัง input
     * @return string
     */
    public static function inputGroup($input, $prepend = '', $append = '') {
        $prependHtml = $prepend ? "<span class=\"input-group-text\">{$prepend}</span>" : '';
        $appendHtml = $append ? "<span class=\"input-group-text\">{$append}</span>" : '';
        
        return "<div class=\"input-group\">
                    {$prependHtml}
                    {$input}
                    {$appendHtml}
                </div>";
    }
    
    /**
     * สร้างคุณสมบัติ HTML
     * 
     * @param array $attributes คุณสมบัติ
     * @return string
     */
    private static function buildAttributes($attributes) {
        $attrs = [];
        
        foreach ($attributes as $key => $value) {
            if ($value === null || $value === false) {
                continue;
            }
            
            if ($value === true) {
                $attrs[] = $key;
            } else {
                $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                $attrs[] = "{$key}=\"{$value}\"";
            }
        }
        
        return implode(' ', $attrs);
    }
    
    /**
     * สร้าง select สำหรับเดือน
     * 
     * @param string $name ชื่อ field
     * @param mixed $selected ค่าที่เลือก
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function monthSelect($name, $selected = '', $attributes = []) {
        $months = DateHelper::getThaiMonths();
        return self::select($name, $months, $selected, $attributes);
    }
    
    /**
     * สร้าง select สำหรับปี
     * 
     * @param string $name ชื่อ field
     * @param mixed $selected ค่าที่เลือก
     * @param int $startYear ปีเริ่มต้น
     * @param int $endYear ปีสิ้นสุด
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function yearSelect($name, $selected = '', $startYear = null, $endYear = null, $attributes = []) {
        $currentYear = date('Y');
        $startYear = $startYear ?: ($currentYear - 10);
        $endYear = $endYear ?: ($currentYear + 10);
        
        $years = [];
        for ($year = $startYear; $year <= $endYear; $year++) {
            $years[$year] = $year + 543; // แปลงเป็น พ.ศ.
        }
        
        return self::select($name, $years, $selected, $attributes);
    }
    
    /**
     * สร้าง select สำหรับสถานะ
     * 
     * @param string $name ชื่อ field
     * @param array $statuses รายการสถานะ
     * @param mixed $selected ค่าที่เลือก
     * @param array $attributes คุณสมบัติเพิ่มเติม
     * @return string
     */
    public static function statusSelect($name, $statuses, $selected = '', $attributes = []) {
        return self::select($name, $statuses, $selected, $attributes);
    }
}
