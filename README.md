# ระบบติดตามงานขายและการเสนอราคา (Sales Tracking and Quotation System)

ระบบติดตามงานขายและการเสนอราคาเป็นแอปพลิเคชันเว็บที่พัฒนาขึ้นเพื่อช่วยในการจัดการกระบวนการขาย การติดตามโอกาสการขาย และการสร้างใบเสนอราคา โดยใช้เทคโนโลยี PHP, JavaScript และ Bootstrap

## คุณสมบัติหลัก

### 1. การจัดการผู้ใช้งาน
- ระบบล็อกอิน/ล็อกเอาท์
- การจัดการสิทธิ์ผู้ใช้งาน (<PERSON><PERSON>, Manager, Sales, User)
- การจัดการโปรไฟล์ผู้ใช้

### 2. การจัดการลูกค้า
- เพิ่ม/แก้ไข/ลบข้อมูลลูกค้า
- จัดการข้อมูลผู้ติดต่อ
- ประวัติการติดต่อและกิจกรรม

### 3. การกำหนดเป้าหมายการขาย
- ตั้งเป้าหมายการขายรายบุคคลหรือรายแผนก
- ติดตามความคืบหน้าเทียบกับเป้าหมาย
- รายงานผลการดำเนินงาน

### 4. การจัดการโอกาสการขาย
- บันทึกและติดตามโอกาสการขาย
- จัดการขั้นตอนการขาย (Sales Pipeline)
- คาดการณ์ยอดขาย

### 5. การจัดทำใบเสนอราคา
- สร้างใบเสนอราคาอัตโนมัติ
- จัดการรายการสินค้า/บริการ
- ติดตามสถานะใบเสนอราคา

### 6. การติดตามการชำระเงิน
- ออกใบแจ้งหนี้
- บันทึกการชำระเงิน
- ติดตามสถานะการชำระเงิน

### 7. การจัดทำรายงาน
- รายงานยอดขาย
- รายงานการติดตามเป้าหมาย
- รายงานวิเคราะห์ลูกค้า

## ความต้องการของระบบ

### ความต้องการด้านเซิร์ฟเวอร์
- PHP 7.4 หรือสูงกว่า
- MySQL 5.7 หรือสูงกว่า
- Apache/Nginx Web Server

### ความต้องการด้านไลบรารี
- Bootstrap 5
- jQuery 3.6
- Chart.js (สำหรับแสดงกราฟ)
- DataTables (สำหรับแสดงตาราง)
- Select2 (สำหรับ dropdown ที่มีการค้นหา)
- SweetAlert2 (สำหรับแสดงการแจ้งเตือน)
- Moment.js (สำหรับจัดการวันที่และเวลา)

## การติดตั้ง

1. ดาวน์โหลดหรือโคลนโปรเจคไปยังเซิร์ฟเวอร์ของคุณ
   ```
   git clone https://github.com/yourusername/sales-tracking-system.git
   ```

2. สร้างฐานข้อมูล MySQL และนำเข้าไฟล์ SQL
   ```
   mysql -u username -p database_name < database/sales_tracking_db.sql
   ```

3. แก้ไขไฟล์การตั้งค่าฐานข้อมูลที่ `app/config/database.php`
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'your_database_name');
   define('DB_USER', 'your_database_user');
   define('DB_PASS', 'your_database_password');
   ```

4. แก้ไขไฟล์การตั้งค่าทั่วไปที่ `app/config/config.php` ตามความเหมาะสม

5. ตั้งค่าสิทธิ์การเข้าถึงไฟล์และโฟลเดอร์
   ```
   chmod -R 755 /path/to/sales_tracking_system
   chmod -R 777 /path/to/sales_tracking_system/uploads
   chmod -R 777 /path/to/sales_tracking_system/logs
   ```

6. เข้าถึงระบบผ่านเว็บเบราว์เซอร์
   ```
   http://your-domain.com/sales_tracking_system
   ```

7. เข้าสู่ระบบด้วยบัญชีผู้ดูแลระบบเริ่มต้น
   ```
   ชื่อผู้ใช้: admin
   รหัสผ่าน: admin123
   ```

## โครงสร้างไฟล์

```
sales_tracking_system/
├── app/
│   ├── config/          # ไฟล์การตั้งค่า
│   ├── controllers/     # คอนโทรลเลอร์
│   ├── helpers/         # คลาสช่วยเหลือ
│   ├── models/          # โมเดล
│   └── utils/           # ยูทิลิตี้
├── assets/
│   ├── css/             # ไฟล์ CSS
│   ├── fonts/           # ฟอนต์
│   ├── images/          # รูปภาพ
│   ├── js/              # ไฟล์ JavaScript
│   └── plugins/         # ปลั๊กอิน
├── database/
│   ├── migrations/      # ไฟล์การอัปเกรดฐานข้อมูล
│   └── seeds/           # ข้อมูลเริ่มต้น
├── docs/                # เอกสาร
├── logs/                # ไฟล์ล็อก
├── tests/               # ไฟล์ทดสอบ
├── uploads/             # ไฟล์ที่อัปโหลด
├── views/               # ไฟล์ view
├── index.php            # ไฟล์หลัก
└── README.md            # ไฟล์นี้
```

## การใช้งาน

### การเข้าสู่ระบบ
1. เข้าถึงระบบผ่าน URL ของเว็บไซต์
2. กรอกชื่อผู้ใช้และรหัสผ่าน
3. คลิกปุ่ม "เข้าสู่ระบบ"

### การจัดการลูกค้า
1. คลิกที่เมนู "ลูกค้า" ในแถบนำทาง
2. คลิกปุ่ม "เพิ่มลูกค้าใหม่" เพื่อเพิ่มลูกค้า
3. กรอกข้อมูลลูกค้าและคลิกปุ่ม "บันทึก"
4. คลิกที่ชื่อลูกค้าเพื่อดูรายละเอียดและจัดการข้อมูลผู้ติดต่อ

### การจัดการโอกาสการขาย
1. คลิกที่เมนู "โอกาสการขาย" ในแถบนำทาง
2. คลิกปุ่ม "เพิ่มโอกาสการขายใหม่" เพื่อเพิ่มโอกาสการขาย
3. เลือกลูกค้า กรอกข้อมูลโอกาสการขาย และคลิกปุ่ม "บันทึก"
4. คลิกที่ชื่อโอกาสการขายเพื่อดูรายละเอียดและอัปเดตสถานะ

### การสร้างใบเสนอราคา
1. คลิกที่เมนู "ใบเสนอราคา" ในแถบนำทาง
2. คลิกปุ่ม "สร้างใบเสนอราคาใหม่" เพื่อสร้างใบเสนอราคา
3. เลือกลูกค้า โอกาสการขาย และกรอกข้อมูลใบเสนอราคา
4. เพิ่มรายการสินค้า/บริการและคลิกปุ่ม "บันทึก"
5. คลิกที่เลขที่ใบเสนอราคาเพื่อดูรายละเอียดและพิมพ์

### การออกใบแจ้งหนี้
1. คลิกที่เมนู "ใบแจ้งหนี้" ในแถบนำทาง
2. คลิกปุ่ม "สร้างใบแจ้งหนี้ใหม่" เพื่อสร้างใบแจ้งหนี้
3. เลือกลูกค้า ใบเสนอราคา และกรอกข้อมูลใบแจ้งหนี้
4. คลิกปุ่ม "บันทึก" เพื่อสร้างใบแจ้งหนี้
5. คลิกที่เลขที่ใบแจ้งหนี้เพื่อดูรายละเอียดและพิมพ์

### การบันทึกการชำระเงิน
1. คลิกที่เมนู "การชำระเงิน" ในแถบนำทาง
2. คลิกปุ่ม "บันทึกการชำระเงินใหม่" เพื่อบันทึกการชำระเงิน
3. เลือกใบแจ้งหนี้ กรอกข้อมูลการชำระเงิน และคลิกปุ่ม "บันทึก"
4. คลิกที่เลขที่การชำระเงินเพื่อดูรายละเอียดและพิมพ์ใบเสร็จรับเงิน

### การดูรายงาน
1. คลิกที่เมนู "รายงาน" ในแถบนำทาง
2. เลือกประเภทรายงานที่ต้องการดู
3. กำหนดช่วงเวลาและตัวกรองอื่นๆ ตามต้องการ
4. คลิกปุ่ม "แสดงรายงาน" เพื่อดูรายงาน
5. คลิกปุ่ม "ส่งออก" เพื่อส่งออกรายงานในรูปแบบ PDF หรือ Excel

## การสนับสนุน

หากคุณพบปัญหาหรือมีคำถามเกี่ยวกับการใช้งานระบบ กรุณาติดต่อทีมสนับสนุนของเราได้ที่:
- อีเมล: <EMAIL>
- โทรศัพท์: 02-123-4567

## ลิขสิทธิ์

ระบบติดตามงานขายและการเสนอราคานี้เป็นซอฟต์แวร์ที่มีลิขสิทธิ์ © 2023 บริษัท ระบบติดตามงานขาย จำกัด
สงวนลิขสิทธิ์ทุกประการ

