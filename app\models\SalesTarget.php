<?php
/**
 * SalesTarget Model
 * 
 * โมเดลสำหรับจัดการข้อมูลเป้าหมายการขาย
 */

class SalesTarget extends BaseModel {
    protected $table = 'sales_targets';
    protected $primaryKey = 'id';
    protected $fillable = [
        'user_id',
        'department_id',
        'target_type',
        'period_type',
        'year',
        'month',
        'quarter',
        'target_amount',
        'achieved_amount',
        'target_quantity',
        'achieved_quantity',
        'start_date',
        'end_date',
        'status',
        'notes',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลเป้าหมายของผู้ใช้
     * 
     * @param int $userId ID ของผู้ใช้
     * @param string $periodType ประเภทช่วงเวลา (monthly, quarterly, yearly)
     * @param int $year ปี
     * @param int $month เดือน (สำหรับ monthly)
     * @param int $quarter ไตรมาส (สำหรับ quarterly)
     * @return array
     */
    public function getUserTargets($userId, $periodType = null, $year = null, $month = null, $quarter = null) {
        $conditions = ['user_id' => $userId];
        
        if ($periodType) {
            $conditions['period_type'] = $periodType;
        }
        
        if ($year) {
            $conditions['year'] = $year;
        }
        
        if ($month && $periodType === 'monthly') {
            $conditions['month'] = $month;
        }
        
        if ($quarter && $periodType === 'quarterly') {
            $conditions['quarter'] = $quarter;
        }
        
        return $this->getWhere($conditions);
    }
    
    /**
     * ดึงข้อมูลเป้าหมายของแผนก
     * 
     * @param int $departmentId ID ของแผนก
     * @param string $periodType ประเภทช่วงเวลา
     * @param int $year ปี
     * @param int $month เดือน
     * @param int $quarter ไตรมาส
     * @return array
     */
    public function getDepartmentTargets($departmentId, $periodType = null, $year = null, $month = null, $quarter = null) {
        $conditions = ['department_id' => $departmentId];
        
        if ($periodType) {
            $conditions['period_type'] = $periodType;
        }
        
        if ($year) {
            $conditions['year'] = $year;
        }
        
        if ($month && $periodType === 'monthly') {
            $conditions['month'] = $month;
        }
        
        if ($quarter && $periodType === 'quarterly') {
            $conditions['quarter'] = $quarter;
        }
        
        return $this->getWhere($conditions);
    }
    
    /**
     * ดึงข้อมูลเป้าหมายพร้อมข้อมูลผู้ใช้
     * 
     * @param int $targetId ID ของเป้าหมาย
     * @return array|bool
     */
    public function getTargetWithUser($targetId) {
        $sql = "SELECT st.*, u.first_name, u.last_name, u.email, d.name as department_name
                FROM " . TABLE_PREFIX . $this->table . " st
                LEFT JOIN " . TABLE_PREFIX . "users u ON st.user_id = u.id
                LEFT JOIN " . TABLE_PREFIX . "departments d ON st.department_id = d.id
                WHERE st.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $targetId]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลเป้าหมายทั้งหมดพร้อมข้อมูลผู้ใช้
     * 
     * @param array $filters ตัวกรอง
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function getAllTargetsWithUser($filters = [], $page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // สร้าง WHERE clause
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['user_id'])) {
            $whereClause[] = "st.user_id = :user_id";
            $params[':user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['department_id'])) {
            $whereClause[] = "st.department_id = :department_id";
            $params[':department_id'] = $filters['department_id'];
        }
        
        if (!empty($filters['period_type'])) {
            $whereClause[] = "st.period_type = :period_type";
            $params[':period_type'] = $filters['period_type'];
        }
        
        if (!empty($filters['year'])) {
            $whereClause[] = "st.year = :year";
            $params[':year'] = $filters['year'];
        }
        
        if (!empty($filters['status'])) {
            $whereClause[] = "st.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total 
                     FROM " . TABLE_PREFIX . $this->table . " st " . $whereStr;
        
        $this->db->prepare($countSql);
        if (!empty($params)) {
            $this->db->bind($params);
        }
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT st.*, u.first_name, u.last_name, d.name as department_name
                FROM " . TABLE_PREFIX . $this->table . " st
                LEFT JOIN " . TABLE_PREFIX . "users u ON st.user_id = u.id
                LEFT JOIN " . TABLE_PREFIX . "departments d ON st.department_id = d.id
                " . $whereStr . "
                ORDER BY st.year DESC, st.month DESC, st.quarter DESC
                LIMIT :offset, :perPage";
        
        $params[':offset'] = $offset;
        $params[':perPage'] = $perPage;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * อัปเดตผลการดำเนินงาน
     * 
     * @param int $targetId ID ของเป้าหมาย
     * @param float $achievedAmount ยอดที่บรรลุ
     * @param int $achievedQuantity จำนวนที่บรรลุ
     * @return bool
     */
    public function updateAchievement($targetId, $achievedAmount = null, $achievedQuantity = null) {
        $data = [];
        
        if ($achievedAmount !== null) {
            $data['achieved_amount'] = $achievedAmount;
        }
        
        if ($achievedQuantity !== null) {
            $data['achieved_quantity'] = $achievedQuantity;
        }
        
        if (empty($data)) {
            return false;
        }
        
        return $this->update($targetId, $data);
    }
    
    /**
     * คำนวณเปอร์เซ็นต์การบรรลุเป้าหมาย
     * 
     * @param array $target ข้อมูลเป้าหมาย
     * @return array
     */
    public function calculateAchievementPercentage($target) {
        $amountPercentage = 0;
        $quantityPercentage = 0;
        
        if ($target['target_amount'] > 0) {
            $amountPercentage = ($target['achieved_amount'] / $target['target_amount']) * 100;
        }
        
        if ($target['target_quantity'] > 0) {
            $quantityPercentage = ($target['achieved_quantity'] / $target['target_quantity']) * 100;
        }
        
        return [
            'amount_percentage' => round($amountPercentage, 2),
            'quantity_percentage' => round($quantityPercentage, 2)
        ];
    }
    
    /**
     * ดึงข้อมูลเป้าหมายที่ใกล้ครบกำหนด
     * 
     * @param int $days จำนวนวันข้างหน้า
     * @return array
     */
    public function getUpcomingTargets($days = 30) {
        $sql = "SELECT st.*, u.first_name, u.last_name, d.name as department_name
                FROM " . TABLE_PREFIX . $this->table . " st
                LEFT JOIN " . TABLE_PREFIX . "users u ON st.user_id = u.id
                LEFT JOIN " . TABLE_PREFIX . "departments d ON st.department_id = d.id
                WHERE st.status = :status 
                AND st.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)
                ORDER BY st.end_date ASC";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':status' => TARGET_STATUS_ACTIVE,
            ':days' => $days
        ]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงสถิติเป้าหมายการขาย
     * 
     * @param array $filters ตัวกรอง
     * @return array
     */
    public function getTargetStats($filters = []) {
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['user_id'])) {
            $whereClause[] = "user_id = :user_id";
            $params[':user_id'] = $filters['user_id'];
        }
        
        if (!empty($filters['department_id'])) {
            $whereClause[] = "department_id = :department_id";
            $params[':department_id'] = $filters['department_id'];
        }
        
        if (!empty($filters['year'])) {
            $whereClause[] = "year = :year";
            $params[':year'] = $filters['year'];
        }
        
        if (!empty($filters['period_type'])) {
            $whereClause[] = "period_type = :period_type";
            $params[':period_type'] = $filters['period_type'];
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        $sql = "SELECT 
                    COUNT(*) as total_targets,
                    SUM(CASE WHEN status = :active THEN 1 ELSE 0 END) as active_targets,
                    SUM(CASE WHEN status = :completed THEN 1 ELSE 0 END) as completed_targets,
                    SUM(target_amount) as total_target_amount,
                    SUM(achieved_amount) as total_achieved_amount,
                    SUM(target_quantity) as total_target_quantity,
                    SUM(achieved_quantity) as total_achieved_quantity,
                    AVG(CASE WHEN target_amount > 0 THEN (achieved_amount / target_amount) * 100 ELSE 0 END) as avg_amount_percentage,
                    AVG(CASE WHEN target_quantity > 0 THEN (achieved_quantity / target_quantity) * 100 ELSE 0 END) as avg_quantity_percentage
                FROM " . TABLE_PREFIX . $this->table . " " . $whereStr;
        
        $params[':active'] = TARGET_STATUS_ACTIVE;
        $params[':completed'] = TARGET_STATUS_COMPLETED;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetch();
    }
}
