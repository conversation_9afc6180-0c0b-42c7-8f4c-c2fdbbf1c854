<?php
/**
 * Activity Controller
 * 
 * คอนโทรลเลอร์สำหรับจัดการข้อมูลกิจกรรม
 */

class ActivityController extends BaseController {
    private $activityModel;
    private $customerModel;
    private $opportunityModel;
    private $contactModel;
    
    public function __construct() {
        parent::__construct();
        
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $this->activityModel = new Activity();
        $this->customerModel = new Customer();
        $this->opportunityModel = new Opportunity();
        $this->contactModel = new Contact();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $activities = $this->activityModel->getPaginated($page);
        $this->setPagination($activities);
        
        $this->data['activities'] = $activities['items'];
        $this->data['page_title'] = 'รายการกิจกรรม';
        
        $this->view('activities/index');
    }
    
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $activity = $this->activityModel->getById($id);
        if (!$activity) {
            $this->show404();
            return;
        }
        
        $this->data['activity'] = $activity;
        $this->data['page_title'] = 'รายละเอียดกิจกรรม';
        
        $this->view('activities/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        $opportunities = $this->opportunityModel->getForDropdown();
        
        $this->data['customers'] = $customers;
        $this->data['opportunities'] = $opportunities;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'เพิ่มกิจกรรมใหม่';
        
        $this->view('activities/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=activity&action=create');
            return;
        }
        
        $data = $this->post();
        $data['assigned_to'] = $this->user['id'];
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->activityModel->create($data);
        
        if ($id) {
            showAlert('เพิ่มกิจกรรมเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=activity&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=activity&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $activity = $this->activityModel->getById($id);
        if (!$activity) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        $opportunities = $this->opportunityModel->getForDropdown();
        
        $this->data['activity'] = $activity;
        $this->data['customers'] = $customers;
        $this->data['opportunities'] = $opportunities;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขกิจกรรม';
        
        $this->view('activities/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=activity&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->activityModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขกิจกรรมเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=activity&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=activity&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->activityModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
    
    public function complete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $outcome = $this->post('outcome', '');
        $success = $this->activityModel->updateStatus($id, ACTIVITY_STATUS_COMPLETED, $outcome);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'อัปเดตสถานะเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
