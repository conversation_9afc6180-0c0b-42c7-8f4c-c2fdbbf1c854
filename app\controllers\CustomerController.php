<?php
/**
 * Customer Controller
 * 
 * คอนโทรลเลอร์สำหรับจัดการข้อมูลลูกค้า
 */

class CustomerController extends BaseController {
    private $customerModel;
    private $contactModel;
    
    public function __construct() {
        parent::__construct();
        
        // ตรวจสอบการเข้าสู่ระบบ
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $this->customerModel = new Customer();
        $this->contactModel = new Contact();
    }
    
    /**
     * แสดงรายการลูกค้า
     */
    public function index() {
        $page = $this->get('page', 1);
        $search = $this->get('search', '');
        $status = $this->get('status', '');
        
        $filters = [];
        if (!empty($search)) {
            $filters['keyword'] = $search;
        }
        if (!empty($status)) {
            $filters['status'] = $status;
        }
        
        $customers = $this->customerModel->getAllCustomersWithStats($filters, $page);
        $this->setPagination($customers);
        
        $this->data['customers'] = $customers['items'];
        $this->data['search'] = $search;
        $this->data['status'] = $status;
        $this->data['page_title'] = 'รายการลูกค้า';
        
        $this->view('customers/index');
    }
    
    /**
     * แสดงรายละเอียดลูกค้า
     */
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $customer = $this->customerModel->getCustomerWithStats($id);
        if (!$customer) {
            $this->show404();
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($id)) {
            $this->show403();
            return;
        }
        
        // ดึงข้อมูลผู้ติดต่อ
        $contacts = $this->contactModel->getByCustomerId($id);
        
        // ดึงข้อมูลโอกาสการขาย
        $opportunityModel = new Opportunity();
        $opportunities = $opportunityModel->getByCustomerId($id);
        
        // ดึงข้อมูลกิจกรรม
        $activityModel = new Activity();
        $activities = $activityModel->getByCustomerId($id);
        
        $this->data['customer'] = $customer;
        $this->data['contacts'] = $contacts;
        $this->data['opportunities'] = $opportunities;
        $this->data['activities'] = $activities;
        $this->data['page_title'] = 'รายละเอียดลูกค้า: ' . $customer['company_name'];
        
        $this->view('customers/view');
    }
    
    /**
     * แสดงฟอร์มเพิ่มลูกค้าใหม่
     */
    public function create() {
        if (!$this->hasPermission('customer', 'create')) {
            $this->show403();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'เพิ่มลูกค้าใหม่';
        
        $this->view('customers/create');
    }
    
    /**
     * ประมวลผลการเพิ่มลูกค้าใหม่
     */
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=customer&action=create');
            return;
        }
        
        $data = $this->post();
        
        // ตรวจสอบข้อมูล
        $validator = new ValidationHelper($data);
        $validator->required('company_name', 'กรุณากรอกชื่อบริษัท')
                 ->required('customer_type', 'กรุณาเลือกประเภทลูกค้า')
                 ->email('email', 'รูปแบบอีเมลไม่ถูกต้อง')
                 ->phone('phone', 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง')
                 ->unique('company_name', 'customers', 'company_name', null, 'ชื่อบริษัทนี้มีอยู่ในระบบแล้ว');
        
        if ($validator->hasErrors()) {
            $this->data['errors'] = $validator->getErrors();
            $this->data['old_data'] = $data;
            $this->data['csrf_token'] = generateCSRFToken();
            $this->data['page_title'] = 'เพิ่มลูกค้าใหม่';
            $this->view('customers/create');
            return;
        }
        
        // เพิ่มข้อมูลเพิ่มเติม
        $data['customer_code'] = $this->customerModel->generateCustomerCode();
        $data['assigned_to'] = $this->user['id'];
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $customerId = $this->customerModel->create($data);
        
        if ($customerId) {
            $this->logActivity('customer_created', 'Created new customer: ' . $data['company_name'], ['customer_id' => $customerId]);
            showAlert('เพิ่มลูกค้าเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=customer&action=show&id=' . $customerId);
        } else {
            showAlert('เกิดข้อผิดพลาดในการเพิ่มลูกค้า', 'error');
            $this->redirect('index.php?controller=customer&action=create');
        }
    }
    
    /**
     * แสดงฟอร์มแก้ไขลูกค้า
     */
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        if (!$this->hasPermission('customer', 'edit')) {
            $this->show403();
            return;
        }
        
        $customer = $this->customerModel->getById($id);
        if (!$customer) {
            $this->show404();
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($id)) {
            $this->show403();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $this->data['customer'] = $customer;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขลูกค้า: ' . $customer['company_name'];
        
        $this->view('customers/edit');
    }
    
    /**
     * ประมวลผลการแก้ไขลูกค้า
     */
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=customer&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        
        // ตรวจสอบข้อมูล
        $validator = new ValidationHelper($data);
        $validator->required('company_name', 'กรุณากรอกชื่อบริษัท')
                 ->required('customer_type', 'กรุณาเลือกประเภทลูกค้า')
                 ->email('email', 'รูปแบบอีเมลไม่ถูกต้อง')
                 ->phone('phone', 'รูปแบบเบอร์โทรศัพท์ไม่ถูกต้อง')
                 ->unique('company_name', 'customers', 'company_name', $id, 'ชื่อบริษัทนี้มีอยู่ในระบบแล้ว');
        
        if ($validator->hasErrors()) {
            $customer = $this->customerModel->getById($id);
            $this->data['customer'] = $customer;
            $this->data['errors'] = $validator->getErrors();
            $this->data['old_data'] = $data;
            $this->data['csrf_token'] = generateCSRFToken();
            $this->data['page_title'] = 'แก้ไขลูกค้า: ' . $customer['company_name'];
            $this->view('customers/edit');
            return;
        }
        
        // เพิ่มข้อมูลเพิ่มเติม
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->customerModel->update($id, $data);
        
        if ($success) {
            $this->logActivity('customer_updated', 'Updated customer: ' . $data['company_name'], ['customer_id' => $id]);
            showAlert('แก้ไขลูกค้าเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=customer&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาดในการแก้ไขลูกค้า', 'error');
            $this->redirect('index.php?controller=customer&action=edit&id=' . $id);
        }
    }
    
    /**
     * ลบลูกค้า
     */
    public function delete($id) {
        if (!$id) {
            $this->json(['success' => false, 'message' => 'ไม่พบข้อมูลลูกค้า'], 400);
            return;
        }
        
        if (!$this->hasPermission('customer', 'delete')) {
            $this->json(['success' => false, 'message' => 'ไม่มีสิทธิ์ลบข้อมูล'], 403);
            return;
        }
        
        if (!$this->isMethod('POST')) {
            $this->json(['success' => false, 'message' => 'Method not allowed'], 405);
            return;
        }
        
        if (!$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $customer = $this->customerModel->getById($id);
        if (!$customer) {
            $this->json(['success' => false, 'message' => 'ไม่พบข้อมูลลูกค้า'], 404);
            return;
        }
        
        // ตรวจสอบสิทธิ์การเข้าถึง
        $authHelper = new AuthHelper();
        if (!$authHelper->canAccessCustomer($id)) {
            $this->json(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึงข้อมูลนี้'], 403);
            return;
        }
        
        // ตรวจสอบว่ามีข้อมูลที่เกี่ยวข้องหรือไม่
        $opportunityModel = new Opportunity();
        $hasOpportunities = $opportunityModel->count(['customer_id' => $id]) > 0;
        
        if ($hasOpportunities) {
            $this->json(['success' => false, 'message' => 'ไม่สามารถลบลูกค้าได้ เนื่องจากมีข้อมูลโอกาสการขายที่เกี่ยวข้อง'], 400);
            return;
        }
        
        $success = $this->customerModel->delete($id);
        
        if ($success) {
            // ลบข้อมูลผู้ติดต่อที่เกี่ยวข้อง
            $this->contactModel->deleteByCustomerId($id);
            
            $this->logActivity('customer_deleted', 'Deleted customer: ' . $customer['company_name'], ['customer_id' => $id]);
            $this->json(['success' => true, 'message' => 'ลบลูกค้าเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาดในการลบลูกค้า'], 500);
        }
    }
    
    /**
     * ค้นหาลูกค้า (สำหรับ AJAX)
     */
    public function search() {
        if (!$this->isAjax()) {
            $this->show404();
            return;
        }
        
        $query = $this->get('q', '');
        $limit = $this->get('limit', 10);
        
        if (strlen($query) < 2) {
            $this->json(['results' => []]);
            return;
        }
        
        $customers = $this->customerModel->searchCustomers($query);
        $results = [];
        
        foreach (array_slice($customers, 0, $limit) as $customer) {
            $results[] = [
                'id' => $customer['id'],
                'text' => $customer['company_name'],
                'customer_code' => $customer['customer_code'],
                'email' => $customer['email'],
                'phone' => $customer['phone']
            ];
        }
        
        $this->json(['results' => $results]);
    }
    
    /**
     * ส่งออกข้อมูลลูกค้า
     */
    public function export() {
        if (!$this->hasPermission('customer', 'export')) {
            $this->show403();
            return;
        }
        
        $format = $this->get('format', 'excel');
        $customers = $this->customerModel->getAll();
        
        // TODO: Implement export functionality
        showAlert('ฟีเจอร์ส่งออกข้อมูลยังไม่พร้อมใช้งาน', 'info');
        $this->redirect('index.php?controller=customer');
    }
}
