<?php
/**
 * Product Model
 * 
 * โมเดลสำหรับจัดการข้อมูลสินค้า/บริการ
 */

class Product extends BaseModel {
    protected $table = 'products';
    protected $primaryKey = 'id';
    protected $fillable = [
        'product_code',
        'name',
        'description',
        'category_id',
        'unit',
        'price',
        'cost',
        'stock_quantity',
        'min_stock_level',
        'status',
        'image',
        'specifications',
        'warranty_period',
        'supplier_id',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลสินค้าที่ใช้งานได้
     * 
     * @return array
     */
    public function getActiveProducts() {
        return $this->getWhere(['status' => PRODUCT_STATUS_ACTIVE]);
    }
    
    /**
     * ดึงข้อมูลสินค้าตามหมวดหมู่
     * 
     * @param int $categoryId ID ของหมวดหมู่
     * @return array
     */
    public function getByCategoryId($categoryId) {
        return $this->getWhere(['category_id' => $categoryId]);
    }
    
    /**
     * ค้นหาสินค้า
     * 
     * @param string $keyword คำค้นหา
     * @return array
     */
    public function searchProducts($keyword) {
        $searchFields = ['product_code', 'name', 'description'];
        return $this->search($keyword, $searchFields);
    }
    
    /**
     * ดึงข้อมูลสินค้าพร้อมข้อมูลหมวดหมู่
     * 
     * @param int $productId ID ของสินค้า
     * @return array|bool
     */
    public function getProductWithCategory($productId) {
        $sql = "SELECT p.*, c.name as category_name, s.name as supplier_name
                FROM " . TABLE_PREFIX . $this->table . " p
                LEFT JOIN " . TABLE_PREFIX . "product_categories c ON p.category_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "suppliers s ON p.supplier_id = s.id
                WHERE p.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $productId]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลสินค้าทั้งหมดพร้อมข้อมูลหมวดหมู่
     * 
     * @param array $filters ตัวกรอง
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function getAllProductsWithCategory($filters = [], $page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // สร้าง WHERE clause
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['category_id'])) {
            $whereClause[] = "p.category_id = :category_id";
            $params[':category_id'] = $filters['category_id'];
        }
        
        if (!empty($filters['status'])) {
            $whereClause[] = "p.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['keyword'])) {
            $whereClause[] = "(p.product_code LIKE :keyword OR p.name LIKE :keyword OR p.description LIKE :keyword)";
            $params[':keyword'] = '%' . $filters['keyword'] . '%';
        }
        
        if (!empty($filters['low_stock'])) {
            $whereClause[] = "p.stock_quantity <= p.min_stock_level";
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total 
                     FROM " . TABLE_PREFIX . $this->table . " p " . $whereStr;
        
        $this->db->prepare($countSql);
        if (!empty($params)) {
            $this->db->bind($params);
        }
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT p.*, c.name as category_name, s.name as supplier_name
                FROM " . TABLE_PREFIX . $this->table . " p
                LEFT JOIN " . TABLE_PREFIX . "product_categories c ON p.category_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "suppliers s ON p.supplier_id = s.id
                " . $whereStr . "
                ORDER BY p.name ASC
                LIMIT :offset, :perPage";
        
        $params[':offset'] = $offset;
        $params[':perPage'] = $perPage;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * ตรวจสอบว่ารหัสสินค้าซ้ำหรือไม่
     * 
     * @param string $productCode รหัสสินค้า
     * @param int $excludeId ID ที่ต้องการยกเว้น (สำหรับการแก้ไข)
     * @return bool
     */
    public function isProductCodeExists($productCode, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " WHERE product_code = :product_code";
        $params = [':product_code' => $productCode];
        
        if ($excludeId) {
            $sql .= " AND id != :exclude_id";
            $params[':exclude_id'] = $excludeId;
        }
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $result = $this->db->fetch();
        return $result['count'] > 0;
    }
    
    /**
     * อัปเดตสต็อกสินค้า
     * 
     * @param int $productId ID ของสินค้า
     * @param int $quantity จำนวนที่เปลี่ยนแปลง (บวกหรือลบ)
     * @return bool
     */
    public function updateStock($productId, $quantity) {
        $sql = "UPDATE " . TABLE_PREFIX . $this->table . " 
                SET stock_quantity = stock_quantity + :quantity 
                WHERE id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':quantity' => $quantity,
            ':id' => $productId
        ]);
        
        return $this->db->execute();
    }
    
    /**
     * ดึงข้อมูลสินค้าที่สต็อกต่ำ
     * 
     * @return array
     */
    public function getLowStockProducts() {
        $sql = "SELECT p.*, c.name as category_name
                FROM " . TABLE_PREFIX . $this->table . " p
                LEFT JOIN " . TABLE_PREFIX . "product_categories c ON p.category_id = c.id
                WHERE p.stock_quantity <= p.min_stock_level 
                AND p.status = :status
                ORDER BY p.stock_quantity ASC";
        
        $this->db->prepare($sql);
        $this->db->bind([':status' => PRODUCT_STATUS_ACTIVE]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลสินค้าสำหรับ dropdown
     * 
     * @param bool $activeOnly เฉพาะสินค้าที่ใช้งานได้
     * @return array
     */
    public function getForDropdown($activeOnly = true) {
        $sql = "SELECT id, product_code, name, price, unit 
                FROM " . TABLE_PREFIX . $this->table;
        
        $params = [];
        
        if ($activeOnly) {
            $sql .= " WHERE status = :status";
            $params[':status'] = PRODUCT_STATUS_ACTIVE;
        }
        
        $sql .= " ORDER BY name ASC";
        
        $this->db->prepare($sql);
        
        if (!empty($params)) {
            $this->db->bind($params);
        }
        
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * คำนวณกำไรขั้นต้น
     * 
     * @param float $price ราคาขาย
     * @param float $cost ต้นทุน
     * @return array
     */
    public function calculateProfit($price, $cost) {
        $profit = $price - $cost;
        $profitMargin = $cost > 0 ? ($profit / $cost) * 100 : 0;
        $profitPercentage = $price > 0 ? ($profit / $price) * 100 : 0;
        
        return [
            'profit' => $profit,
            'profit_margin' => round($profitMargin, 2),
            'profit_percentage' => round($profitPercentage, 2)
        ];
    }
    
    /**
     * ดึงสถิติสินค้า
     * 
     * @return array
     */
    public function getProductStats() {
        $sql = "SELECT 
                    COUNT(*) as total_products,
                    SUM(CASE WHEN status = :active_status THEN 1 ELSE 0 END) as active_products,
                    SUM(CASE WHEN status = :inactive_status THEN 1 ELSE 0 END) as inactive_products,
                    SUM(CASE WHEN stock_quantity <= min_stock_level THEN 1 ELSE 0 END) as low_stock_products,
                    AVG(price) as average_price,
                    SUM(stock_quantity * cost) as total_inventory_value
                FROM " . TABLE_PREFIX . $this->table;
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':active_status' => PRODUCT_STATUS_ACTIVE,
            ':inactive_status' => PRODUCT_STATUS_INACTIVE
        ]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
}
