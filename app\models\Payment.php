<?php
/**
 * Payment Model
 * 
 * โมเดลสำหรับจัดการข้อมูลการชำระเงิน
 */

class Payment extends BaseModel {
    protected $table = 'payments';
    protected $primaryKey = 'id';
    protected $fillable = [
        'payment_number',
        'invoice_id',
        'customer_id',
        'payment_date',
        'amount',
        'payment_method',
        'reference_number',
        'bank_name',
        'account_number',
        'notes',
        'receipt_image',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลการชำระเงินของใบแจ้งหนี้
     * 
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @return array
     */
    public function getByInvoiceId($invoiceId) {
        return $this->getWhere(['invoice_id' => $invoiceId]);
    }
    
    /**
     * ดึงข้อมูลการชำระเงินของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return array
     */
    public function getByCustomerId($customerId) {
        return $this->getWhere(['customer_id' => $customerId]);
    }
    
    /**
     * ดึงข้อมูลการชำระเงินพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param int $paymentId ID ของการชำระเงิน
     * @return array|bool
     */
    public function getPaymentWithRelations($paymentId) {
        $sql = "SELECT p.*, i.invoice_number, i.total_amount as invoice_total, i.due_date,
                       c.company_name, c.address, c.phone as customer_phone, c.email as customer_email,
                       u.first_name as creator_first_name, u.last_name as creator_last_name
                FROM " . TABLE_PREFIX . $this->table . " p
                LEFT JOIN " . TABLE_PREFIX . "invoices i ON p.invoice_id = i.id
                LEFT JOIN " . TABLE_PREFIX . "customers c ON p.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "users u ON p.created_by = u.id
                WHERE p.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $paymentId]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลการชำระเงินทั้งหมดพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param array $filters ตัวกรอง
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function getAllPaymentsWithRelations($filters = [], $page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // สร้าง WHERE clause
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['customer_id'])) {
            $whereClause[] = "p.customer_id = :customer_id";
            $params[':customer_id'] = $filters['customer_id'];
        }
        
        if (!empty($filters['invoice_id'])) {
            $whereClause[] = "p.invoice_id = :invoice_id";
            $params[':invoice_id'] = $filters['invoice_id'];
        }
        
        if (!empty($filters['payment_method'])) {
            $whereClause[] = "p.payment_method = :payment_method";
            $params[':payment_method'] = $filters['payment_method'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereClause[] = "p.payment_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause[] = "p.payment_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['keyword'])) {
            $whereClause[] = "(p.payment_number LIKE :keyword OR i.invoice_number LIKE :keyword OR c.company_name LIKE :keyword)";
            $params[':keyword'] = '%' . $filters['keyword'] . '%';
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total 
                     FROM " . TABLE_PREFIX . $this->table . " p
                     LEFT JOIN " . TABLE_PREFIX . "invoices i ON p.invoice_id = i.id
                     LEFT JOIN " . TABLE_PREFIX . "customers c ON p.customer_id = c.id " . $whereStr;
        
        $this->db->prepare($countSql);
        if (!empty($params)) {
            $this->db->bind($params);
        }
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT p.*, i.invoice_number, i.total_amount as invoice_total, c.company_name
                FROM " . TABLE_PREFIX . $this->table . " p
                LEFT JOIN " . TABLE_PREFIX . "invoices i ON p.invoice_id = i.id
                LEFT JOIN " . TABLE_PREFIX . "customers c ON p.customer_id = c.id
                " . $whereStr . "
                ORDER BY p.payment_date DESC
                LIMIT :offset, :perPage";
        
        $params[':offset'] = $offset;
        $params[':perPage'] = $perPage;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * สร้างเลขที่การชำระเงินใหม่
     * 
     * @return string
     */
    public function generatePaymentNumber() {
        $sql = "SELECT payment_number FROM " . TABLE_PREFIX . $this->table . " 
                WHERE payment_number LIKE :prefix 
                ORDER BY payment_number DESC LIMIT 1";
        
        $prefix = RECEIPT_PREFIX . date('Ym') . '%';
        
        $this->db->prepare($sql);
        $this->db->bind([':prefix' => $prefix]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        if ($result) {
            $lastNumber = intval(substr($result['payment_number'], -4));
            return generateDocumentNumber(RECEIPT_PREFIX, $lastNumber);
        } else {
            return generateDocumentNumber(RECEIPT_PREFIX, 0);
        }
    }
    
    /**
     * คำนวณยอดรวมการชำระเงินของใบแจ้งหนี้
     * 
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @return float
     */
    public function getTotalPaidByInvoice($invoiceId) {
        $sql = "SELECT SUM(amount) as total_paid FROM " . TABLE_PREFIX . $this->table . " 
                WHERE invoice_id = :invoice_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':invoice_id' => $invoiceId]);
        $this->db->execute();
        
        $result = $this->db->fetch();
        return $result['total_paid'] ?: 0;
    }
    
    /**
     * ดึงข้อมูลการชำระเงินล่าสุด
     * 
     * @param int $limit จำนวนรายการ
     * @return array
     */
    public function getRecentPayments($limit = 10) {
        $sql = "SELECT p.*, i.invoice_number, c.company_name
                FROM " . TABLE_PREFIX . $this->table . " p
                LEFT JOIN " . TABLE_PREFIX . "invoices i ON p.invoice_id = i.id
                LEFT JOIN " . TABLE_PREFIX . "customers c ON p.customer_id = c.id
                ORDER BY p.payment_date DESC, p.created_at DESC
                LIMIT :limit";
        
        $this->db->prepare($sql);
        $this->db->bind([':limit' => $limit]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงสถิติการชำระเงิน
     * 
     * @param array $filters ตัวกรอง
     * @return array
     */
    public function getPaymentStats($filters = []) {
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereClause[] = "payment_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause[] = "payment_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        $sql = "SELECT 
                    COUNT(*) as total_payments,
                    SUM(amount) as total_amount,
                    AVG(amount) as average_amount,
                    SUM(CASE WHEN payment_method = :cash THEN amount ELSE 0 END) as cash_amount,
                    SUM(CASE WHEN payment_method = :bank_transfer THEN amount ELSE 0 END) as bank_transfer_amount,
                    SUM(CASE WHEN payment_method = :credit_card THEN amount ELSE 0 END) as credit_card_amount,
                    SUM(CASE WHEN payment_method = :cheque THEN amount ELSE 0 END) as cheque_amount
                FROM " . TABLE_PREFIX . $this->table . " " . $whereStr;
        
        $params[':cash'] = PAYMENT_METHOD_CASH;
        $params[':bank_transfer'] = PAYMENT_METHOD_BANK_TRANSFER;
        $params[':credit_card'] = PAYMENT_METHOD_CREDIT_CARD;
        $params[':cheque'] = PAYMENT_METHOD_CHEQUE;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลการชำระเงินตามวิธีการชำระ
     * 
     * @param array $filters ตัวกรอง
     * @return array
     */
    public function getPaymentsByMethod($filters = []) {
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $whereClause[] = "payment_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause[] = "payment_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        $sql = "SELECT payment_method, COUNT(*) as count, SUM(amount) as total_amount
                FROM " . TABLE_PREFIX . $this->table . " " . $whereStr . "
                GROUP BY payment_method
                ORDER BY total_amount DESC";
        
        $this->db->prepare($sql);
        
        if (!empty($params)) {
            $this->db->bind($params);
        }
        
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
}
