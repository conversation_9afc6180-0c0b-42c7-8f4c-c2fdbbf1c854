<?php
/**
 * Base Controller Class
 * 
 * คลาสพื้นฐานสำหรับคอนโทรลเลอร์ทั้งหมด
 */

abstract class BaseController {
    protected $data = [];
    protected $user = null;
    
    /**
     * คอนสตรักเตอร์
     */
    public function __construct() {
        // ตรวจสอบการเข้าสู่ระบบ
        if (isset($_SESSION['user'])) {
            $this->user = $_SESSION['user'];
            $this->data['user'] = $this->user;
        }
        
        // ตั้งค่าข้อมูลพื้นฐาน
        $this->data['app_name'] = APP_NAME;
        $this->data['base_url'] = BASE_URL;
        $this->data['assets_url'] = ASSETS_URL;
        
        // ดึงการแจ้งเตือน
        $this->loadAlert();
        
        // ดึงการตั้งค่าสาธารณะ
        $this->loadPublicSettings();
    }
    
    /**
     * โหลดข้อความแจ้งเตือน
     */
    protected function loadAlert() {
        $alert = getAlert();
        if ($alert) {
            $this->data['alert'] = $alert;
        }
    }
    
    /**
     * โหลดการตั้งค่าสาธารณะ
     */
    protected function loadPublicSettings() {
        $settingModel = new Setting();
        $this->data['settings'] = $settingModel->getPublicSettings();
    }
    
    /**
     * แสดงหน้าเว็บ
     * 
     * @param string $view ชื่อไฟล์ view
     * @param array $data ข้อมูลที่ส่งไปยัง view
     */
    protected function view($view, $data = []) {
        // รวมข้อมูลที่ส่งมากับข้อมูลพื้นฐาน
        $this->data = array_merge($this->data, $data);
        
        // แยกตัวแปรออกมาใช้ใน view
        extract($this->data);
        
        // ตรวจสอบว่าไฟล์ view มีอยู่หรือไม่
        $viewFile = VIEWS_PATH . '/' . $view . '.php';
        
        if (file_exists($viewFile)) {
            include $viewFile;
        } else {
            // แสดงข้อผิดพลาดถ้าไม่พบไฟล์ view
            $this->showError("View file not found: {$view}");
        }
    }
    
    /**
     * ส่งข้อมูล JSON
     * 
     * @param array $data ข้อมูลที่ต้องการส่ง
     * @param int $status HTTP status code
     */
    protected function json($data, $status = 200) {
        jsonResponse($data, $status);
    }
    
    /**
     * เปลี่ยนเส้นทาง
     * 
     * @param string $url URL ที่ต้องการเปลี่ยนเส้นทางไป
     */
    protected function redirect($url) {
        redirect($url);
    }
    
    /**
     * ตรวจสอบสิทธิ์การเข้าถึง
     * 
     * @param string $permission สิทธิ์ที่ต้องการตรวจสอบ
     * @return bool
     */
    protected function hasPermission($permission) {
        if (!$this->user) {
            return false;
        }
        
        $authHelper = new AuthHelper();
        return $authHelper->hasPermission($this->user['role_id'], $permission);
    }
    
    /**
     * ตรวจสอบว่าเป็น AJAX request หรือไม่
     * 
     * @return bool
     */
    protected function isAjax() {
        return isAjax();
    }
    
    /**
     * ตรวจสอบ CSRF token
     * 
     * @return bool
     */
    protected function validateCSRF() {
        $token = $_POST['csrf_token'] ?? $_GET['csrf_token'] ?? '';
        return verifyCSRFToken($token);
    }
    
    /**
     * ตรวจสอบ HTTP method
     * 
     * @param string $method HTTP method ที่ต้องการ
     * @return bool
     */
    protected function isMethod($method) {
        return strtoupper($_SERVER['REQUEST_METHOD']) === strtoupper($method);
    }
    
    /**
     * ดึงข้อมูลจาก POST
     * 
     * @param string $key คีย์ของข้อมูล
     * @param mixed $default ค่าเริ่มต้น
     * @return mixed
     */
    protected function post($key = null, $default = null) {
        if ($key === null) {
            return $_POST;
        }
        
        return isset($_POST[$key]) ? sanitize($_POST[$key]) : $default;
    }
    
    /**
     * ดึงข้อมูลจาก GET
     * 
     * @param string $key คีย์ของข้อมูล
     * @param mixed $default ค่าเริ่มต้น
     * @return mixed
     */
    protected function get($key = null, $default = null) {
        if ($key === null) {
            return $_GET;
        }
        
        return isset($_GET[$key]) ? sanitize($_GET[$key]) : $default;
    }
    
    /**
     * ดึงข้อมูลจาก REQUEST
     * 
     * @param string $key คีย์ของข้อมูล
     * @param mixed $default ค่าเริ่มต้น
     * @return mixed
     */
    protected function input($key = null, $default = null) {
        if ($key === null) {
            return array_merge($_GET, $_POST);
        }
        
        if (isset($_POST[$key])) {
            return sanitize($_POST[$key]);
        }
        
        if (isset($_GET[$key])) {
            return sanitize($_GET[$key]);
        }
        
        return $default;
    }
    
    /**
     * ตรวจสอบข้อมูลที่จำเป็น
     * 
     * @param array $required รายการฟิลด์ที่จำเป็น
     * @param array $data ข้อมูลที่ต้องการตรวจสอบ
     * @return array ข้อผิดพลาด (ถ้ามี)
     */
    protected function validateRequired($required, $data = null) {
        if ($data === null) {
            $data = $this->input();
        }
        
        $errors = [];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                $errors[$field] = "ฟิลด์ {$field} จำเป็นต้องกรอก";
            }
        }
        
        return $errors;
    }
    
    /**
     * แสดงข้อผิดพลาด
     * 
     * @param string $message ข้อความข้อผิดพลาด
     * @param int $code รหัสข้อผิดพลาด
     */
    protected function showError($message, $code = 500) {
        http_response_code($code);
        
        if ($this->isAjax()) {
            $this->json(['error' => $message], $code);
        } else {
            $this->data['error_message'] = $message;
            $this->data['error_code'] = $code;
            $this->view('errors/error');
        }
    }
    
    /**
     * แสดงหน้า 404
     */
    protected function show404() {
        $this->showError('ไม่พบหน้าที่ต้องการ', 404);
    }
    
    /**
     * แสดงหน้า 403
     */
    protected function show403() {
        $this->showError('ไม่มีสิทธิ์เข้าถึง', 403);
    }
    
    /**
     * บันทึกล็อกกิจกรรม
     * 
     * @param string $action การกระทำ
     * @param string $description คำอธิบาย
     * @param array $data ข้อมูลเพิ่มเติม
     */
    protected function logActivity($action, $description, $data = []) {
        $logData = [
            'user_id' => $this->user ? $this->user['id'] : null,
            'action' => $action,
            'description' => $description,
            'data' => json_encode($data),
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        writeLog(json_encode($logData), 'ACTIVITY', ACTIVITY_LOG_FILE);
    }
    
    /**
     * ตั้งค่าข้อมูลสำหรับ pagination
     * 
     * @param array $paginationData ข้อมูล pagination
     */
    protected function setPagination($paginationData) {
        $this->data['pagination'] = $paginationData;
        
        // สร้าง URL สำหรับ pagination
        $currentUrl = $_SERVER['REQUEST_URI'];
        $urlParts = parse_url($currentUrl);
        parse_str($urlParts['query'] ?? '', $queryParams);
        
        // ลบ page parameter ออก
        unset($queryParams['page']);
        
        $baseUrl = $urlParts['path'];
        if (!empty($queryParams)) {
            $baseUrl .= '?' . http_build_query($queryParams) . '&page=';
        } else {
            $baseUrl .= '?page=';
        }
        
        $this->data['pagination_base_url'] = $baseUrl;
    }
}
