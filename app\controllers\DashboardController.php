<?php
/**
 * Dashboard Controller
 * 
 * คอนโทรลเลอร์สำหรับแสดงหน้าแดชบอร์ด
 */

class DashboardController extends BaseController {
    private $customerModel;
    private $opportunityModel;
    private $quotationModel;
    private $invoiceModel;
    private $paymentModel;
    private $activityModel;
    private $salesTargetModel;
    
    public function __construct() {
        parent::__construct();
        
        // ตรวจสอบการเข้าสู่ระบบ
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $this->customerModel = new Customer();
        $this->opportunityModel = new Opportunity();
        $this->quotationModel = new Quotation();
        $this->invoiceModel = new Invoice();
        $this->paymentModel = new Payment();
        $this->activityModel = new Activity();
        $this->salesTargetModel = new SalesTarget();
    }
    
    /**
     * แสดงหน้าแดชบอร์ดหลัก
     */
    public function index() {
        // ดึงข้อมูลสถิติต่างๆ
        $this->data['stats'] = $this->getDashboardStats();
        
        // ดึงข้อมูลกราฟ
        $this->data['charts'] = $this->getChartData();
        
        // ดึงข้อมูลกิจกรรมล่าสุด
        $this->data['recent_activities'] = $this->getRecentActivities();
        
        // ดึงข้อมูลการแจ้งเตือน
        $this->data['notifications'] = $this->getNotifications();
        
        // ดึงข้อมูลเป้าหมายการขาย
        $this->data['sales_targets'] = $this->getSalesTargets();
        
        $this->view('dashboard/index');
    }
    
    /**
     * ดึงข้อมูลสถิติสำหรับแดชบอร์ด
     * 
     * @return array
     */
    private function getDashboardStats() {
        $currentMonth = date('Y-m');
        $currentYear = date('Y');
        
        // สถิติลูกค้า
        $totalCustomers = $this->customerModel->count();
        $activeCustomers = $this->customerModel->count(['status' => CUSTOMER_STATUS_ACTIVE]);
        $newCustomersThisMonth = $this->customerModel->count([
            'status' => CUSTOMER_STATUS_ACTIVE,
            'created_at >=' => $currentMonth . '-01'
        ]);
        
        // สถิติโอกาสการขาย
        $totalOpportunities = $this->opportunityModel->count();
        $openOpportunities = $this->opportunityModel->count(['status' => OPPORTUNITY_STATUS_OPEN]);
        $wonOpportunities = $this->opportunityModel->count(['status' => OPPORTUNITY_STATUS_WON]);
        $lostOpportunities = $this->opportunityModel->count(['status' => OPPORTUNITY_STATUS_LOST]);
        
        // สถิติใบเสนอราคา
        $totalQuotations = $this->quotationModel->count();
        $pendingQuotations = $this->quotationModel->count(['status' => QUOTATION_STATUS_SENT]);
        $acceptedQuotations = $this->quotationModel->count(['status' => QUOTATION_STATUS_ACCEPTED]);
        
        // สถิติใบแจ้งหนี้
        $totalInvoices = $this->invoiceModel->count();
        $pendingInvoices = $this->invoiceModel->count(['status' => INVOICE_STATUS_PENDING]);
        $paidInvoices = $this->invoiceModel->count(['status' => INVOICE_STATUS_PAID]);
        $overdueInvoices = $this->invoiceModel->count(['status' => INVOICE_STATUS_OVERDUE]);
        
        // สถิติการชำระเงิน
        $totalPayments = $this->paymentModel->count();
        $paymentsThisMonth = $this->paymentModel->count([
            'payment_date >=' => $currentMonth . '-01'
        ]);
        
        // คำนวณยอดเงิน
        $totalRevenue = $this->getTotalRevenue();
        $monthlyRevenue = $this->getMonthlyRevenue($currentMonth);
        $yearlyRevenue = $this->getYearlyRevenue($currentYear);
        
        return [
            'customers' => [
                'total' => $totalCustomers,
                'active' => $activeCustomers,
                'new_this_month' => $newCustomersThisMonth
            ],
            'opportunities' => [
                'total' => $totalOpportunities,
                'open' => $openOpportunities,
                'won' => $wonOpportunities,
                'lost' => $lostOpportunities,
                'win_rate' => $totalOpportunities > 0 ? round(($wonOpportunities / $totalOpportunities) * 100, 2) : 0
            ],
            'quotations' => [
                'total' => $totalQuotations,
                'pending' => $pendingQuotations,
                'accepted' => $acceptedQuotations,
                'acceptance_rate' => $totalQuotations > 0 ? round(($acceptedQuotations / $totalQuotations) * 100, 2) : 0
            ],
            'invoices' => [
                'total' => $totalInvoices,
                'pending' => $pendingInvoices,
                'paid' => $paidInvoices,
                'overdue' => $overdueInvoices
            ],
            'payments' => [
                'total' => $totalPayments,
                'this_month' => $paymentsThisMonth
            ],
            'revenue' => [
                'total' => $totalRevenue,
                'monthly' => $monthlyRevenue,
                'yearly' => $yearlyRevenue
            ]
        ];
    }
    
    /**
     * ดึงข้อมูลสำหรับกราฟ
     * 
     * @return array
     */
    private function getChartData() {
        return [
            'monthly_sales' => $this->getMonthlySalesData(),
            'opportunity_pipeline' => $this->getOpportunityPipelineData(),
            'payment_methods' => $this->getPaymentMethodsData(),
            'customer_growth' => $this->getCustomerGrowthData()
        ];
    }
    
    /**
     * ดึงข้อมูลยอดขายรายเดือน
     * 
     * @return array
     */
    private function getMonthlySalesData() {
        $currentYear = date('Y');
        $months = [];
        $sales = [];
        
        for ($i = 1; $i <= 12; $i++) {
            $month = sprintf('%s-%02d', $currentYear, $i);
            $months[] = date('M', strtotime($month . '-01'));
            
            $monthlyRevenue = $this->getMonthlyRevenue($month);
            $sales[] = $monthlyRevenue;
        }
        
        return [
            'labels' => $months,
            'data' => $sales
        ];
    }
    
    /**
     * ดึงข้อมูล pipeline โอกาสการขาย
     * 
     * @return array
     */
    private function getOpportunityPipelineData() {
        $stages = [
            STAGE_LEAD => 'Lead',
            STAGE_QUALIFICATION => 'Qualification',
            STAGE_PROPOSAL => 'Proposal',
            STAGE_NEGOTIATION => 'Negotiation',
            STAGE_CLOSED_WON => 'Closed Won',
            STAGE_CLOSED_LOST => 'Closed Lost'
        ];
        
        $data = [];
        foreach ($stages as $stage => $label) {
            $count = $this->opportunityModel->count(['stage' => $stage]);
            $data[] = [
                'label' => $label,
                'value' => $count
            ];
        }
        
        return $data;
    }
    
    /**
     * ดึงข้อมูลวิธีการชำระเงิน
     * 
     * @return array
     */
    private function getPaymentMethodsData() {
        $currentMonth = date('Y-m');
        return $this->paymentModel->getPaymentsByMethod([
            'date_from' => $currentMonth . '-01',
            'date_to' => date('Y-m-t')
        ]);
    }
    
    /**
     * ดึงข้อมูลการเติบโตของลูกค้า
     * 
     * @return array
     */
    private function getCustomerGrowthData() {
        $months = [];
        $growth = [];
        
        for ($i = 11; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-{$i} months"));
            $months[] = date('M Y', strtotime($month . '-01'));
            
            $count = $this->customerModel->count([
                'created_at <=' => $month . '-31'
            ]);
            $growth[] = $count;
        }
        
        return [
            'labels' => $months,
            'data' => $growth
        ];
    }
    
    /**
     * ดึงข้อมูลกิจกรรมล่าสุด
     * 
     * @return array
     */
    private function getRecentActivities() {
        $userId = $this->user['role_id'] === ROLE_ADMIN ? null : $this->user['id'];
        return $this->activityModel->getUpcomingActivities($userId, 7);
    }
    
    /**
     * ดึงข้อมูลการแจ้งเตือน
     * 
     * @return array
     */
    private function getNotifications() {
        $notificationModel = new Notification();
        return $notificationModel->getUnreadNotifications($this->user['id']);
    }
    
    /**
     * ดึงข้อมูลเป้าหมายการขาย
     * 
     * @return array
     */
    private function getSalesTargets() {
        $currentYear = date('Y');
        $currentMonth = date('n');
        
        $userId = $this->user['role_id'] === ROLE_ADMIN ? null : $this->user['id'];
        
        if ($userId) {
            $monthlyTarget = $this->salesTargetModel->getUserTargets(
                $userId, 'monthly', $currentYear, $currentMonth
            );
            
            $yearlyTarget = $this->salesTargetModel->getUserTargets(
                $userId, 'yearly', $currentYear
            );
        } else {
            $monthlyTarget = [];
            $yearlyTarget = [];
        }
        
        return [
            'monthly' => $monthlyTarget,
            'yearly' => $yearlyTarget
        ];
    }
    
    /**
     * คำนวณยอดรายได้รวม
     * 
     * @return float
     */
    private function getTotalRevenue() {
        $stats = $this->paymentModel->getPaymentStats();
        return $stats['total_amount'] ?: 0;
    }
    
    /**
     * คำนวณยอดรายได้รายเดือน
     * 
     * @param string $month เดือนในรูปแบบ Y-m
     * @return float
     */
    private function getMonthlyRevenue($month) {
        $stats = $this->paymentModel->getPaymentStats([
            'date_from' => $month . '-01',
            'date_to' => date('Y-m-t', strtotime($month . '-01'))
        ]);
        return $stats['total_amount'] ?: 0;
    }
    
    /**
     * คำนวณยอดรายได้รายปี
     * 
     * @param string $year ปี
     * @return float
     */
    private function getYearlyRevenue($year) {
        $stats = $this->paymentModel->getPaymentStats([
            'date_from' => $year . '-01-01',
            'date_to' => $year . '-12-31'
        ]);
        return $stats['total_amount'] ?: 0;
    }
    
    /**
     * ดึงข้อมูลสำหรับ widget
     */
    public function widget() {
        $widget = $this->get('widget');
        
        switch ($widget) {
            case 'recent_activities':
                $this->json($this->getRecentActivities());
                break;
                
            case 'notifications':
                $this->json($this->getNotifications());
                break;
                
            case 'sales_chart':
                $this->json($this->getMonthlySalesData());
                break;
                
            case 'pipeline_chart':
                $this->json($this->getOpportunityPipelineData());
                break;
                
            default:
                $this->json(['error' => 'Invalid widget'], 400);
        }
    }
}
