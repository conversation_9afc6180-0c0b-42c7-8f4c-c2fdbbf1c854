<?php
/**
 * Opportunities Page
 * 
 * หน้าจัดการโอกาสการขายสำหรับระบบติดตามงานขายและการเสนอราคา
 */

// เริ่มต้น Session
session_start();

// ตรวจสอบว่าผู้ใช้ล็อกอินแล้วหรือไม่
if (!isset($_SESSION['user_id'])) {
    // ถ้ายังไม่ได้ล็อกอินให้ redirect ไปที่หน้าล็อกอิน
    header('Location: ../auth/login.php');
    exit;
}

// โหลดไฟล์การตั้งค่า
require_once '../app/config/config.php';
require_once '../app/config/database.php';
require_once '../app/config/functions.php';

// โหลดโมเดลที่จำเป็น
require_once '../app/models/User.php';
require_once '../app/models/Customer.php';
require_once '../app/models/Opportunity.php';

// สร้างอินสแตนซ์ของโมเดล
$userModel = new User($conn);
$customerModel = new Customer($conn);
$opportunityModel = new Opportunity($conn);

// ดึงข้อมูลผู้ใช้ปัจจุบัน
$currentUser = $userModel->getUserById($_SESSION['user_id']);

// กำหนดค่าเริ่มต้นสำหรับการค้นหาและการกรอง
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'created_at';
$order = isset($_GET['order']) ? $_GET['order'] : 'desc';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// ดึงข้อมูลโอกาสการขาย
$opportunities = $opportunityModel->getOpportunities($search, $status, $sort, $order, $limit, $offset);
$totalOpportunities = $opportunityModel->getTotalOpportunities($search, $status);
$totalPages = ceil($totalOpportunities / $limit);

// ดึงข้อมูลลูกค้าสำหรับตัวกรอง
$customers = $customerModel->getAllCustomers();

// กำหนดหัวข้อหน้า
$pageTitle = 'โอกาสการขาย';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ระบบติดตามงานขายและการเสนอราคา</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>ระบบติดตามงานขาย</h3>
            </div>
            
            <ul class="list-unstyled components">
                <li>
                    <a href="../dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> แดชบอร์ด
                    </a>
                </li>
                <li class="active">
                    <a href="#salesSubmenu" data-bs-toggle="collapse" aria-expanded="true" class="dropdown-toggle">
                        <i class="fas fa-chart-line me-2"></i> งานขาย
                    </a>
                    <ul class="collapse show list-unstyled" id="salesSubmenu">
                        <li>
                            <a href="targets.php">เป้าหมายการขาย</a>
                        </li>
                        <li class="active">
                            <a href="opportunities.php">โอกาสการขาย</a>
                        </li>
                        <li>
                            <a href="pipeline.php">Pipeline การขาย</a>
                        </li>
                        <li>
                            <a href="forecasts.php">การคาดการณ์ยอดขาย</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#quotationSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-file-invoice me-2"></i> ใบเสนอราคา
                    </a>
                    <ul class="collapse list-unstyled" id="quotationSubmenu">
                        <li>
                            <a href="../quotations/create.php">สร้างใบเสนอราคา</a>
                        </li>
                        <li>
                            <a href="../quotations/list.php">รายการใบเสนอราคา</a>
                        </li>
                        <li>
                            <a href="../quotations/templates.php">เทมเพลตใบเสนอราคา</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#customerSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-users me-2"></i> ลูกค้า
                    </a>
                    <ul class="collapse list-unstyled" id="customerSubmenu">
                        <li>
                            <a href="../customers/list.php">รายชื่อลูกค้า</a>
                        </li>
                        <li>
                            <a href="../customers/create.php">เพิ่มลูกค้าใหม่</a>
                        </li>
                        <li>
                            <a href="../customers/groups.php">กลุ่มลูกค้า</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#productSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-box me-2"></i> สินค้าและบริการ
                    </a>
                    <ul class="collapse list-unstyled" id="productSubmenu">
                        <li>
                            <a href="../products/list.php">รายการสินค้า</a>
                        </li>
                        <li>
                            <a href="../products/create.php">เพิ่มสินค้าใหม่</a>
                        </li>
                        <li>
                            <a href="../products/categories.php">หมวดหมู่สินค้า</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#paymentSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-money-bill-wave me-2"></i> การชำระเงิน
                    </a>
                    <ul class="collapse list-unstyled" id="paymentSubmenu">
                        <li>
                            <a href="../payments/list.php">รายการชำระเงิน</a>
                        </li>
                        <li>
                            <a href="../payments/create.php">บันทึกการชำระเงิน</a>
                        </li>
                        <li>
                            <a href="../payments/invoices.php">ใบแจ้งหนี้</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#reportSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-chart-bar me-2"></i> รายงาน
                    </a>
                    <ul class="collapse list-unstyled" id="reportSubmenu">
                        <li>
                            <a href="../reports/sales.php">รายงานยอดขาย</a>
                        </li>
                        <li>
                            <a href="../reports/performance.php">รายงานประสิทธิภาพ</a>
                        </li>
                        <li>
                            <a href="../reports/customers.php">รายงานลูกค้า</a>
                        </li>
                        <li>
                            <a href="../reports/products.php">รายงานสินค้า</a>
                        </li>
                    </ul>
                </li>
                <?php if ($_SESSION['role'] === 'admin'): ?>
                <li>
                    <a href="#settingSubmenu" data-bs-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <i class="fas fa-cog me-2"></i> ตั้งค่า
                    </a>
                    <ul class="collapse list-unstyled" id="settingSubmenu">
                        <li>
                            <a href="../settings/users.php">ผู้ใช้งาน</a>
                        </li>
                        <li>
                            <a href="../settings/roles.php">สิทธิ์การใช้งาน</a>
                        </li>
                        <li>
                            <a href="../settings/company.php">ข้อมูลบริษัท</a>
                        </li>
                        <li>
                            <a href="../settings/system.php">ตั้งค่าระบบ</a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
            
            <div class="sidebar-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-white-50">เข้าสู่ระบบล่าสุด:</small><br>
                        <small class="text-white-50"><?php echo date('d/m/Y H:i', strtotime($currentUser['last_login'])); ?></small>
                    </div>
                    <a href="../auth/logout.php" class="btn btn-sm btn-outline-light">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Page Content -->
        <div id="content">
            <!-- Navbar -->
            <nav class="navbar navbar-expand-lg navbar-light bg-white">
                <div class="container-fluid">
                    <button type="button" id="sidebar-toggle" class="btn btn-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                    
                    <div class="ms-auto d-flex align-items-center">
                        <!-- Notifications -->
                        <div class="dropdown me-3">
                            <a class="btn btn-light position-relative" href="#" role="button" id="notificationDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    3
                                    <span class="visually-hidden">unread notifications</span>
                                </span>
                            </a>
                            <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
                                <div class="notification-header">
                                    <h6 class="notification-title">การแจ้งเตือน</h6>
                                </div>
                                <div class="notification-list">
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">มีใบเสนอราคาใหม่ #QT-2023-0042</div>
                                        <div class="notification-item-time">เมื่อ 5 นาทีที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">ลูกค้ายืนยันการชำระเงิน #INV-2023-0036</div>
                                        <div class="notification-item-time">เมื่อ 2 ชั่วโมงที่แล้ว</div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-item-title">มีโอกาสการขายใหม่จาก บริษัท ABC จำกัด</div>
                                        <div class="notification-item-time">เมื่อ 3 ชั่วโมงที่แล้ว</div>
                                    </a>
                                </div>
                                <div class="notification-footer">
                                    <a href="../notifications.php" class="text-primary">ดูการแจ้งเตือนทั้งหมด</a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- User Profile -->
                        <div class="dropdown">
                            <a class="btn btn-light dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="<?php echo !empty($currentUser['profile_image']) ? $currentUser['profile_image'] : '../assets/images/default-avatar.png'; ?>" alt="Profile" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                <?php echo $currentUser['name']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="../profile.php"><i class="fas fa-user me-2"></i> โปรไฟล์</a></li>
                                <li><a class="dropdown-item" href="../settings/preferences.php"><i class="fas fa-cog me-2"></i> ตั้งค่า</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="../auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i> ออกจากระบบ</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>
            
            <!-- Page Content -->
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">โอกาสการขาย</h1>
                    <a href="opportunity_create.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> สร้างโอกาสการขายใหม่
                    </a>
                </div>
                
                <!-- Filter and Search -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body">
                        <form method="get" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="row g-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                    <input type="text" class="form-control" name="search" placeholder="ค้นหาโอกาสการขาย..." value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <select class="form-select" name="status">
                                    <option value="">-- สถานะทั้งหมด --</option>
                                    <option value="new" <?php echo $status === 'new' ? 'selected' : ''; ?>>ใหม่</option>
                                    <option value="qualified" <?php echo $status === 'qualified' ? 'selected' : ''; ?>>มีคุณสมบัติ</option>
                                    <option value="proposal" <?php echo $status === 'proposal' ? 'selected' : ''; ?>>เสนอราคา</option>
                                    <option value="negotiation" <?php echo $status === 'negotiation' ? 'selected' : ''; ?>>เจรจาต่อรอง</option>
                                    <option value="won" <?php echo $status === 'won' ? 'selected' : ''; ?>>ปิดการขาย</option>
                                    <option value="lost" <?php echo $status === 'lost' ? 'selected' : ''; ?>>สูญเสีย</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <select class="form-select" name="sort">
                                    <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>เรียงตามวันที่สร้าง</option>
                                    <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>เรียงตามชื่อ</option>
                                    <option value="value" <?php echo $sort === 'value' ? 'selected' : ''; ?>>เรียงตามมูลค่า</option>
                                    <option value="expected_close_date" <?php echo $sort === 'expected_close_date' ? 'selected' : ''; ?>>เรียงตามวันที่คาดว่าจะปิดการขาย</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <div class="d-flex">
                                    <select class="form-select me-2" name="order">
                                        <option value="desc" <?php echo $order === 'desc' ? 'selected' : ''; ?>>มากไปน้อย</option>
                                        <option value="asc" <?php echo $order === 'asc' ? 'selected' : ''; ?>>น้อยไปมาก</option>
                                    </select>
                                    
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Opportunities List -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ชื่อโอกาสการขาย</th>
                                        <th>ลูกค้า</th>
                                        <th>มูลค่า</th>
                                        <th>สถานะ</th>
                                        <th>วันที่คาดว่าจะปิดการขาย</th>
                                        <th>ความน่าจะเป็น</th>
                                        <th>ผู้รับผิดชอบ</th>
                                        <th>การดำเนินการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($opportunities)): ?>
                                        <?php foreach ($opportunities as $opportunity): ?>
                                            <tr>
                                                <td>
                                                    <a href="opportunity_detail.php?id=<?php echo $opportunity['id']; ?>" class="text-decoration-none fw-medium">
                                                        <?php echo htmlspecialchars($opportunity['name']); ?>
                                                    </a>
                                                    <div class="small text-muted">
                                                        สร้างเมื่อ: <?php echo date('d/m/Y', strtotime($opportunity['created_at'])); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <a href="../customers/view.php?id=<?php echo $opportunity['customer_id']; ?>" class="text-decoration-none">
                                                        <?php echo htmlspecialchars($opportunity['customer_name']); ?>
                                                    </a>
                                                </td>
                                                <td><?php echo number_format($opportunity['value'], 2); ?> บาท</td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    
                                                    switch ($opportunity['status']) {
                                                        case 'new':
                                                            $statusClass = 'bg-info';
                                                            $statusText = 'ใหม่';
                                                            break;
                                                        case 'qualified':
                                                            $statusClass = 'bg-primary';
                                                            $statusText = 'มีคุณสมบัติ';
                                                            break;
                                                        case 'proposal':
                                                            $statusClass = 'bg-warning';
                                                            $statusText = 'เสนอราคา';
                                                            break;
                                                        case 'negotiation':
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = 'เจรจาต่อรอง';
                                                            break;
                                                        case 'won':
                                                            $statusClass = 'bg-success';
                                                            $statusText = 'ปิดการขาย';
                                                            break;
                                                        case 'lost':
                                                            $statusClass = 'bg-danger';
                                                            $statusText = 'สูญเสีย';
                                                            break;
                                                        default:
                                                            $statusClass = 'bg-secondary';
                                                            $statusText = $opportunity['status'];
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                                                </td>
                                                <td><?php echo date('d/m/Y', strtotime($opportunity['expected_close_date'])); ?></td>
                                                <td>
                                                    <div class="progress" style="height: 6px;">
                                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $opportunity['probability']; ?>%;" aria-valuenow="<?php echo $opportunity['probability']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                    <small class="mt-1 d-block"><?php echo $opportunity['probability']; ?>%</small>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <img src="<?php echo !empty($opportunity['user_image']) ? $opportunity['user_image'] : '../assets/images/default-avatar.png'; ?>" alt="User" class="rounded-circle me-2" style="width: 24px; height: 24px; object-fit: cover;">
                                                        <span><?php echo htmlspecialchars($opportunity['user_name']); ?></span>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton<?php echo $opportunity['id']; ?>" data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton<?php echo $opportunity['id']; ?>">
                                                            <li><a class="dropdown-item" href="opportunity_detail.php?id=<?php echo $opportunity['id']; ?>"><i class="fas fa-eye me-2"></i> ดูรายละเอียด</a></li>
                                                            <li><a class="dropdown-item" href="opportunity_edit.php?id=<?php echo $opportunity['id']; ?>"><i class="fas fa-edit me-2"></i> แก้ไข</a></li>
                                                            <li><a class="dropdown-item" href="../quotations/create.php?opportunity_id=<?php echo $opportunity['id']; ?>"><i class="fas fa-file-invoice me-2"></i> สร้างใบเสนอราคา</a></li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li><a class="dropdown-item text-danger" href="#" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $opportunity['id']; ?>"><i class="fas fa-trash-alt me-2"></i> ลบ</a></li>
                                                        </ul>
                                                    </div>
                                                    
                                                    <!-- Delete Modal -->
                                                    <div class="modal fade" id="deleteModal<?php echo $opportunity['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $opportunity['id']; ?>" aria-hidden="true">
                                                        <div class="modal-dialog">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="deleteModalLabel<?php echo $opportunity['id']; ?>">ยืนยันการลบ</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <p>คุณแน่ใจหรือไม่ว่าต้องการลบโอกาสการขาย "<?php echo htmlspecialchars($opportunity['name']); ?>"?</p>
                                                                    <p class="text-danger"><small>การดำเนินการนี้ไม่สามารถเปลี่ยนกลับได้</small></p>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                                                    <a href="opportunity_delete.php?id=<?php echo $opportunity['id']; ?>" class="btn btn-danger">ลบ</a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-4">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                                    <h5>ไม่พบข้อมูลโอกาสการขาย</h5>
                                                    <p class="text-muted">ลองเปลี่ยนเงื่อนไขการค้นหาหรือสร้างโอกาสการขายใหม่</p>
                                                    <a href="opportunity_create.php" class="btn btn-primary mt-2">
                                                        <i class="fas fa-plus me-1"></i> สร้างโอกาสการขายใหม่
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>&sort=<?php echo urlencode($sort); ?>&order=<?php echo urlencode($order); ?>" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>&sort=<?php echo urlencode($sort); ?>&order=<?php echo urlencode($order); ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; ?>
                        
                        <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status); ?>&sort=<?php echo urlencode($sort); ?>&order=<?php echo urlencode($order); ?>" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
            
            <!-- Footer -->
            <footer class="bg-white p-3 text-center">
                <div class="container">
                    <p class="mb-0 text-muted">&copy; <?php echo date('Y'); ?> ระบบติดตามงานขายและการเสนอราคา | พัฒนาโดย <a href="#" class="text-decoration-none">บริษัท ตัวอย่าง จำกัด</a></p>
                </div>
            </footer>
        </div>
    </div>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../assets/js/main.js"></script>
</body>
</html>

