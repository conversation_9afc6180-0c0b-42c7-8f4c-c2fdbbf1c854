/**
 * Main JavaScript File
 * 
 * ไฟล์ JavaScript หลักสำหรับระบบติดตามงานขายและการเสนอราคา
 */

// เมื่อ DOM โหลดเสร็จสมบูรณ์
document.addEventListener('DOMContentLoaded', function() {
    // เริ่มต้นการทำงานของระบบ
    initApp();
});

/**
 * เริ่มต้นการทำงานของแอปพลิเคชัน
 */
function initApp() {
    // เริ่มต้นการทำงานของ Sidebar
    initSidebar();
    
    // เริ่มต้นการทำงานของ Tooltips
    initTooltips();
    
    // เริ่มต้นการทำงานของ Popovers
    initPopovers();
    
    // เริ่มต้นการทำงานของ Dropdowns
    initDropdowns();
    
    // เริ่มต้นการทำงานของ Modals
    initModals();
    
    // เริ่มต้นการทำงานของ DataTables
    initDataTables();
    
    // เริ่มต้นการทำงานของ Select2
    initSelect2();
    
    // เริ่มต้นการทำงานของ DatePicker
    initDatePicker();
    
    // เริ่มต้นการทำงานของ Charts
    initCharts();
    
    // เริ่มต้นการทำงานของ Form Validation
    initFormValidation();
    
    // เริ่มต้นการทำงานของ Custom File Input
    initCustomFileInput();
    
    // เริ่มต้นการทำงานของ Notifications
    initNotifications();
}

/**
 * เริ่มต้นการทำงานของ Sidebar
 */
function initSidebar() {
    // ปุ่มเปิด/ปิด Sidebar
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            const content = document.querySelector('.content');
            
            if (sidebar && content) {
                sidebar.classList.toggle('active');
                content.classList.toggle('active');
                sidebarToggle.classList.toggle('active');
            }
        });
    }
    
    // เลือกเมนูที่กำลังใช้งาน
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('#sidebar ul li a');
    
    sidebarLinks.forEach(function(link) {
        if (link.getAttribute('href') === currentPath) {
            link.parentElement.classList.add('active');
            
            // ถ้าอยู่ใน Dropdown ให้เปิด Dropdown นั้น
            const dropdown = link.closest('.collapse');
            if (dropdown) {
                dropdown.classList.add('show');
                const dropdownToggle = document.querySelector(`[data-bs-target="#${dropdown.id}"]`);
                if (dropdownToggle) {
                    dropdownToggle.setAttribute('aria-expanded', 'true');
                }
            }
        }
    });
}

/**
 * เริ่มต้นการทำงานของ Tooltips
 */
function initTooltips() {
    // ตรวจสอบว่ามี Bootstrap Tooltip หรือไม่
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Tooltip !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
}

/**
 * เริ่มต้นการทำงานของ Popovers
 */
function initPopovers() {
    // ตรวจสอบว่ามี Bootstrap Popover หรือไม่
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Popover !== 'undefined') {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
}

/**
 * เริ่มต้นการทำงานของ Dropdowns
 */
function initDropdowns() {
    // ตรวจสอบว่ามี Bootstrap Dropdown หรือไม่
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Dropdown !== 'undefined') {
        const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
        dropdownElementList.map(function(dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl);
        });
    }
}

/**
 * เริ่มต้นการทำงานของ Modals
 */
function initModals() {
    // ตรวจสอบว่ามี Bootstrap Modal หรือไม่
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
        // จัดการกับ Modal ที่ต้องแสดงทันทีเมื่อโหลดหน้า
        const autoShowModals = document.querySelectorAll('.modal[data-auto-show="true"]');
        autoShowModals.forEach(function(modalEl) {
            const modal = new bootstrap.Modal(modalEl);
            modal.show();
        });
        
        // จัดการกับการส่งข้อมูลฟอร์มใน Modal
        document.querySelectorAll('.modal form').forEach(function(form) {
            form.addEventListener('submit', function(event) {
                // ถ้าต้องการส่งข้อมูลแบบ Ajax
                if (form.getAttribute('data-ajax') === 'true') {
                    event.preventDefault();
                    submitFormAjax(form);
                }
            });
        });
    }
}

/**
 * เริ่มต้นการทำงานของ DataTables
 */
function initDataTables() {
    // ตรวจสอบว่ามี DataTable หรือไม่
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.datatable').each(function() {
            const table = $(this);
            const options = {
                language: {
                    url: 'assets/plugins/datatables/i18n/th.json'
                },
                responsive: true,
                processing: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "ทั้งหมด"]]
            };
            
            // ถ้ามีการกำหนดค่า options เพิ่มเติม
            if (table.data('options')) {
                Object.assign(options, table.data('options'));
            }
            
            table.DataTable(options);
        });
    }
}

/**
 * เริ่มต้นการทำงานของ Select2
 */
function initSelect2() {
    // ตรวจสอบว่ามี Select2 หรือไม่
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').each(function() {
            const select = $(this);
            const options = {
                language: 'th',
                width: '100%',
                placeholder: select.data('placeholder') || 'เลือก...',
                allowClear: select.data('allow-clear') || false
            };
            
            // ถ้ามีการกำหนดค่า options เพิ่มเติม
            if (select.data('options')) {
                Object.assign(options, select.data('options'));
            }
            
            select.select2(options);
        });
    }
}

/**
 * เริ่มต้นการทำงานของ DatePicker
 */
function initDatePicker() {
    // ตรวจสอบว่ามี Flatpickr หรือไม่
    if (typeof flatpickr !== 'undefined') {
        const datepickers = document.querySelectorAll('.datepicker');
        datepickers.forEach(function(el) {
            const options = {
                dateFormat: 'd/m/Y',
                locale: 'th',
                allowInput: true,
                altInput: true,
                altFormat: 'd F Y',
                disableMobile: true
            };
            
            // ถ้ามีการกำหนดค่า options เพิ่มเติม
            if (el.dataset.options) {
                try {
                    const customOptions = JSON.parse(el.dataset.options);
                    Object.assign(options, customOptions);
                } catch (e) {
                    console.error('Invalid JSON in data-options', e);
                }
            }
            
            flatpickr(el, options);
        });
    }
}

/**
 * เริ่มต้นการทำงานของ Charts
 */
function initCharts() {
    // ตรวจสอบว่ามี Chart.js หรือไม่
    if (typeof Chart !== 'undefined') {
        // กำหนดค่าเริ่มต้นสำหรับ Chart.js
        Chart.defaults.font.family = "'Sarabun', 'Prompt', 'Kanit', sans-serif";
        Chart.defaults.color = '#666';
        
        // สร้าง Chart ตามที่กำหนดใน HTML
        document.querySelectorAll('[data-chart]').forEach(function(el) {
            try {
                const type = el.dataset.chartType || 'line';
                const options = JSON.parse(el.dataset.chartOptions || '{}');
                const data = JSON.parse(el.dataset.chartData || '{}');
                
                new Chart(el, {
                    type: type,
                    data: data,
                    options: options
                });
            } catch (e) {
                console.error('Error initializing chart', e);
            }
        });
    }
}

/**
 * เริ่มต้นการทำงานของ Form Validation
 */
function initFormValidation() {
    // ตรวจสอบฟอร์มที่ต้องการตรวจสอบความถูกต้อง
    document.querySelectorAll('form[data-validate="true"]').forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!validateForm(form)) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
}

/**
 * ตรวจสอบความถูกต้องของฟอร์ม
 * 
 * @param {HTMLFormElement} form ฟอร์มที่ต้องการตรวจสอบ
 * @returns {boolean} ผลการตรวจสอบ
 */
function validateForm(form) {
    let isValid = true;
    
    // ตรวจสอบฟิลด์ที่จำเป็น
    form.querySelectorAll('[required]').forEach(function(field) {
        if (!field.value.trim()) {
            isValid = false;
            field.classList.add('is-invalid');
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    // ตรวจสอบอีเมล
    form.querySelectorAll('[type="email"]').forEach(function(field) {
        if (field.value.trim() && !validateEmail(field.value)) {
            isValid = false;
            field.classList.add('is-invalid');
        }
    });
    
    // ตรวจสอบรหัสผ่าน
    form.querySelectorAll('[data-validate="password"]').forEach(function(field) {
        if (field.value.trim() && !validatePassword(field.value)) {
            isValid = false;
            field.classList.add('is-invalid');
        }
    });
    
    // ตรวจสอบการยืนยันรหัสผ่าน
    form.querySelectorAll('[data-validate="confirm-password"]').forEach(function(field) {
        const passwordField = document.getElementById(field.dataset.match);
        if (passwordField && field.value !== passwordField.value) {
            isValid = false;
            field.classList.add('is-invalid');
        }
    });
    
    return isValid;
}

/**
 * ตรวจสอบความถูกต้องของอีเมล
 * 
 * @param {string} email อีเมลที่ต้องการตรวจสอบ
 * @returns {boolean} ผลการตรวจสอบ
 */
function validateEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

/**
 * ตรวจสอบความถูกต้องของรหัสผ่าน
 * 
 * @param {string} password รหัสผ่านที่ต้องการตรวจสอบ
 * @returns {boolean} ผลการตรวจสอบ
 */
function validatePassword(password) {
    // รหัสผ่านต้องมีความยาวอย่างน้อย 8 ตัวอักษร
    // และต้องมีตัวอักษรพิมพ์ใหญ่ ตัวอักษรพิมพ์เล็ก และตัวเลข อย่างน้อย 1 ตัว
    const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return re.test(String(password));
}

/**
 * เริ่มต้นการทำงานของ Custom File Input
 */
function initCustomFileInput() {
    // จัดการกับ Custom File Input
    document.querySelectorAll('.custom-file-input').forEach(function(input) {
        input.addEventListener('change', function() {
            const fileName = this.files[0]?.name || 'เลือกไฟล์';
            const label = this.nextElementSibling;
            if (label) {
                label.textContent = fileName;
            }
        });
    });
}

/**
 * เริ่มต้นการทำงานของ Notifications
 */
function initNotifications() {
    // จัดการกับการแสดงการแจ้งเตือน
    document.querySelectorAll('[data-notification]').forEach(function(el) {
        el.addEventListener('click', function() {
            const type = this.dataset.notificationType || 'info';
            const message = this.dataset.notificationMessage || '';
            const title = this.dataset.notificationTitle || '';
            
            showNotification(type, message, title);
        });
    });
}

/**
 * แสดงการแจ้งเตือน
 * 
 * @param {string} type ประเภทของการแจ้งเตือน (success, info, warning, error)
 * @param {string} message ข้อความแจ้งเตือน
 * @param {string} title หัวข้อแจ้งเตือน
 */
function showNotification(type, message, title = '') {
    // ตรวจสอบว่ามี SweetAlert2 หรือไม่
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: type,
            title: title,
            text: message,
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
        });
    } else {
        // ถ้าไม่มี SweetAlert2 ให้ใช้ alert ธรรมดา
        alert(`${title ? title + ': ' : ''}${message}`);
    }
}

/**
 * ส่งข้อมูลฟอร์มแบบ Ajax
 * 
 * @param {HTMLFormElement} form ฟอร์มที่ต้องการส่ง
 */
function submitFormAjax(form) {
    const url = form.getAttribute('action') || window.location.href;
    const method = form.getAttribute('method') || 'POST';
    const formData = new FormData(form);
    
    // แสดง Loading
    const submitBtn = form.querySelector('[type="submit"]');
    if (submitBtn) {
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> กำลังดำเนินการ...';
    }
    
    // ส่งข้อมูล
    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        // คืนค่าปุ่ม Submit
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
        
        // จัดการกับผลลัพธ์
        if (data.success) {
            // แสดงข้อความสำเร็จ
            showNotification('success', data.message, 'สำเร็จ');
            
            // ถ้ามีการระบุ URL ให้เปลี่ยนเส้นทาง
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1000);
            }
            
            // ถ้าอยู่ใน Modal ให้ปิด Modal
            const modal = form.closest('.modal');
            if (modal && typeof bootstrap !== 'undefined' && typeof bootstrap.Modal !== 'undefined') {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
            
            // ล้างฟอร์ม
            if (data.clearForm) {
                form.reset();
            }
            
            // รีโหลดหน้า
            if (data.reload) {
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }
        } else {
            // แสดงข้อความผิดพลาด
            showNotification('error', data.message || 'เกิดข้อผิดพลาด', 'ผิดพลาด');
            
            // แสดงข้อผิดพลาดในฟอร์ม
            if (data.errors) {
                for (const field in data.errors) {
                    const input = form.querySelector(`[name="${field}"]`);
                    if (input) {
                        input.classList.add('is-invalid');
                        
                        // แสดงข้อความผิดพลาด
                        const feedback = input.nextElementSibling;
                        if (feedback && feedback.classList.contains('invalid-feedback')) {
                            feedback.textContent = data.errors[field];
                        } else {
                            const div = document.createElement('div');
                            div.classList.add('invalid-feedback');
                            div.textContent = data.errors[field];
                            input.parentNode.insertBefore(div, input.nextSibling);
                        }
                    }
                }
            }
        }
    })
    .catch(error => {
        // คืนค่าปุ่ม Submit
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
        
        // แสดงข้อความผิดพลาด
        showNotification('error', 'เกิดข้อผิดพลาดในการส่งข้อมูล', 'ผิดพลาด');
        console.error('Error submitting form:', error);
    });
}

/**
 * โหลดข้อมูลแบบ Ajax
 * 
 * @param {string} url URL ที่ต้องการโหลดข้อมูล
 * @param {object} params พารามิเตอร์ที่ต้องการส่ง
 * @param {function} callback ฟังก์ชันที่จะเรียกเมื่อโหลดข้อมูลสำเร็จ
 * @param {string} method วิธีการส่งข้อมูล (GET, POST)
 */
function loadData(url, params = {}, callback, method = 'GET') {
    // สร้าง URL สำหรับ GET
    if (method.toUpperCase() === 'GET' && Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString();
        url = `${url}${url.includes('?') ? '&' : '?'}${queryString}`;
    }
    
    // สร้าง options สำหรับ fetch
    const options = {
        method: method.toUpperCase(),
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    };
    
    // เพิ่ม body สำหรับ POST
    if (method.toUpperCase() === 'POST' && Object.keys(params).length > 0) {
        options.body = new FormData();
        for (const key in params) {
            options.body.append(key, params[key]);
        }
    }
    
    // ส่งคำขอ
    fetch(url, options)
    .then(response => response.json())
    .then(data => {
        if (typeof callback === 'function') {
            callback(data);
        }
    })
    .catch(error => {
        console.error('Error loading data:', error);
        showNotification('error', 'เกิดข้อผิดพลาดในการโหลดข้อมูล', 'ผิดพลาด');
    });
}

/**
 * โหลดเนื้อหาแบบ Ajax
 * 
 * @param {string} url URL ที่ต้องการโหลดเนื้อหา
 * @param {string} targetSelector ตัวเลือก CSS ของ element ที่ต้องการแสดงเนื้อหา
 * @param {object} params พารามิเตอร์ที่ต้องการส่ง
 */
function loadContent(url, targetSelector, params = {}) {
    const target = document.querySelector(targetSelector);
    if (!target) {
        console.error(`Target element not found: ${targetSelector}`);
        return;
    }
    
    // แสดง Loading
    target.innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">กำลังโหลด...</span></div><p class="mt-2">กำลังโหลดข้อมูล...</p></div>';
    
    // สร้าง URL สำหรับ GET
    if (Object.keys(params).length > 0) {
        const queryString = new URLSearchParams(params).toString();
        url = `${url}${url.includes('?') ? '&' : '?'}${queryString}`;
    }
    
    // ส่งคำขอ
    fetch(url, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.text())
    .then(html => {
        target.innerHTML = html;
        
        // เริ่มต้นการทำงานของ JavaScript ในเนื้อหาที่โหลดมา
        initContentScripts(target);
    })
    .catch(error => {
        console.error('Error loading content:', error);
        target.innerHTML = '<div class="alert alert-danger">เกิดข้อผิดพลาดในการโหลดข้อมูล</div>';
    });
}

/**
 * เริ่มต้นการทำงานของ JavaScript ในเนื้อหาที่โหลดมา
 * 
 * @param {HTMLElement} container container ที่มีเนื้อหาที่โหลดมา
 */
function initContentScripts(container) {
    // เริ่มต้นการทำงานของ Tooltips
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Tooltip !== 'undefined') {
        const tooltipTriggerList = [].slice.call(container.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // เริ่มต้นการทำงานของ Popovers
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Popover !== 'undefined') {
        const popoverTriggerList = [].slice.call(container.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
    
    // เริ่มต้นการทำงานของ DataTables
    if (typeof $.fn.DataTable !== 'undefined') {
        $(container).find('.datatable').each(function() {
            const table = $(this);
            const options = {
                language: {
                    url: 'assets/plugins/datatables/i18n/th.json'
                },
                responsive: true,
                processing: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "ทั้งหมด"]]
            };
            
            // ถ้ามีการกำหนดค่า options เพิ่มเติม
            if (table.data('options')) {
                Object.assign(options, table.data('options'));
            }
            
            table.DataTable(options);
        });
    }
    
    // เริ่มต้นการทำงานของ Select2
    if (typeof $.fn.select2 !== 'undefined') {
        $(container).find('.select2').each(function() {
            const select = $(this);
            const options = {
                language: 'th',
                width: '100%',
                placeholder: select.data('placeholder') || 'เลือก...',
                allowClear: select.data('allow-clear') || false
            };
            
            // ถ้ามีการกำหนดค่า options เพิ่มเติม
            if (select.data('options')) {
                Object.assign(options, select.data('options'));
            }
            
            select.select2(options);
        });
    }
    
    // เริ่มต้นการทำงานของ DatePicker
    if (typeof flatpickr !== 'undefined') {
        container.querySelectorAll('.datepicker').forEach(function(el) {
            const options = {
                dateFormat: 'd/m/Y',
                locale: 'th',
                allowInput: true,
                altInput: true,
                altFormat: 'd F Y',
                disableMobile: true
            };
            
            // ถ้ามีการกำหนดค่า options เพิ่มเติม
            if (el.dataset.options) {
                try {
                    const customOptions = JSON.parse(el.dataset.options);
                    Object.assign(options, customOptions);
                } catch (e) {
                    console.error('Invalid JSON in data-options', e);
                }
            }
            
            flatpickr(el, options);
        });
    }
    
    // เริ่มต้นการทำงานของ Charts
    if (typeof Chart !== 'undefined') {
        container.querySelectorAll('[data-chart]').forEach(function(el) {
            try {
                const type = el.dataset.chartType || 'line';
                const options = JSON.parse(el.dataset.chartOptions || '{}');
                const data = JSON.parse(el.dataset.chartData || '{}');
                
                new Chart(el, {
                    type: type,
                    data: data,
                    options: options
                });
            } catch (e) {
                console.error('Error initializing chart', e);
            }
        });
    }
    
    // เริ่มต้นการทำงานของ Form Validation
    container.querySelectorAll('form[data-validate="true"]').forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!validateForm(form)) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        });
    });
    
    // จัดการกับ Custom File Input
    container.querySelectorAll('.custom-file-input').forEach(function(input) {
        input.addEventListener('change', function() {
            const fileName = this.files[0]?.name || 'เลือกไฟล์';
            const label = this.nextElementSibling;
            if (label) {
                label.textContent = fileName;
            }
        });
    });
    
    // จัดการกับการแสดงการแจ้งเตือน
    container.querySelectorAll('[data-notification]').forEach(function(el) {
        el.addEventListener('click', function() {
            const type = this.dataset.notificationType || 'info';
            const message = this.dataset.notificationMessage || '';
            const title = this.dataset.notificationTitle || '';
            
            showNotification(type, message, title);
        });
    });
}

/**
 * ลบข้อมูล
 * 
 * @param {string} url URL สำหรับลบข้อมูล
 * @param {string} title หัวข้อของกล่องยืนยัน
 * @param {string} text ข้อความของกล่องยืนยัน
 * @param {function} callback ฟังก์ชันที่จะเรียกเมื่อลบข้อมูลสำเร็จ
 */
function deleteData(url, title = 'คุณแน่ใจหรือไม่?', text = 'ข้อมูลที่ถูกลบจะไม่สามารถกู้คืนได้', callback) {
    // ตรวจสอบว่ามี SweetAlert2 หรือไม่
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            title: title,
            text: text,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#e74c3c',
            cancelButtonColor: '#7f8c8d',
            confirmButtonText: 'ใช่, ลบข้อมูล',
            cancelButtonText: 'ยกเลิก'
        }).then((result) => {
            if (result.isConfirmed) {
                // ส่งคำขอลบข้อมูล
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: '_method=DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        Swal.fire(
                            'ลบข้อมูลสำเร็จ!',
                            data.message || 'ข้อมูลถูกลบเรียบร้อยแล้ว',
                            'success'
                        );
                        
                        if (typeof callback === 'function') {
                            callback(data);
                        }
                    } else {
                        Swal.fire(
                            'เกิดข้อผิดพลาด!',
                            data.message || 'ไม่สามารถลบข้อมูลได้',
                            'error'
                        );
                    }
                })
                .catch(error => {
                    console.error('Error deleting data:', error);
                    Swal.fire(
                        'เกิดข้อผิดพลาด!',
                        'ไม่สามารถลบข้อมูลได้',
                        'error'
                    );
                });
            }
        });
    } else {
        // ถ้าไม่มี SweetAlert2 ให้ใช้ confirm ธรรมดา
        if (confirm(`${title}\n${text}`)) {
            // ส่งคำขอลบข้อมูล
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: '_method=DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('ลบข้อมูลสำเร็จ!');
                    
                    if (typeof callback === 'function') {
                        callback(data);
                    }
                } else {
                    alert('เกิดข้อผิดพลาด! ไม่สามารถลบข้อมูลได้');
                }
            })
            .catch(error => {
                console.error('Error deleting data:', error);
                alert('เกิดข้อผิดพลาด! ไม่สามารถลบข้อมูลได้');
            });
        }
    }
}

/**
 * แปลงตัวเลขเป็นรูปแบบเงิน
 * 
 * @param {number} number ตัวเลขที่ต้องการแปลง
 * @param {number} decimals จำนวนทศนิยม
 * @param {string} decimalSeparator ตัวคั่นทศนิยม
 * @param {string} thousandsSeparator ตัวคั่นหลักพัน
 * @returns {string} ตัวเลขในรูปแบบเงิน
 */
function formatMoney(number, decimals = 2, decimalSeparator = '.', thousandsSeparator = ',') {
    const fixedNumber = parseFloat(number).toFixed(decimals);
    const [integerPart, decimalPart] = fixedNumber.split('.');
    
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
    
    return decimals > 0 ? `${formattedIntegerPart}${decimalSeparator}${decimalPart}` : formattedIntegerPart;
}

/**
 * แปลงวันที่เป็นรูปแบบที่กำหนด
 * 
 * @param {string|Date} date วันที่ที่ต้องการแปลง
 * @param {string} format รูปแบบวันที่
 * @returns {string} วันที่ในรูปแบบที่กำหนด
 */
function formatDate(date, format = 'DD/MM/YYYY') {
    // ตรวจสอบว่ามี Moment.js หรือไม่
    if (typeof moment !== 'undefined') {
        return moment(date).format(format);
    } else {
        // ถ้าไม่มี Moment.js ให้ใช้ Date ธรรมดา
        const d = new Date(date);
        
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        
        return `${day}/${month}/${year}`;
    }
}

