<?php
/**
 * File Helper
 * 
 * คลาสช่วยเหลือสำหรับการจัดการไฟล์
 */

class FileHelper {
    
    /**
     * อัปโหลดไฟล์
     * 
     * @param array $file ข้อมูลไฟล์จาก $_FILES
     * @param string $destination โฟลเดอร์ปลายทาง
     * @param array $allowedTypes ประเภทไฟล์ที่อนุญาต
     * @param int $maxSize ขนาดไฟล์สูงสุด (bytes)
     * @return array
     */
    public static function upload($file, $destination, $allowedTypes = [], $maxSize = null) {
        $result = [
            'success' => false,
            'message' => '',
            'filename' => '',
            'path' => ''
        ];
        
        // ตรวจสอบข้อผิดพลาดในการอัปโหลด
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $result['message'] = self::getUploadErrorMessage($file['error']);
            return $result;
        }
        
        // ตรวจสอบขนาดไฟล์
        if ($maxSize && $file['size'] > $maxSize) {
            $result['message'] = 'ขนาดไฟล์เกินกำหนด สูงสุด: ' . formatFileSize($maxSize);
            return $result;
        }
        
        // ตรวจสอบประเภทไฟล์
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!empty($allowedTypes) && !in_array($extension, $allowedTypes)) {
            $result['message'] = 'ประเภทไฟล์ไม่ถูกต้อง อนุญาตเฉพาะ: ' . implode(', ', $allowedTypes);
            return $result;
        }
        
        // สร้างโฟลเดอร์ถ้ายังไม่มี
        if (!is_dir($destination)) {
            if (!mkdir($destination, 0755, true)) {
                $result['message'] = 'ไม่สามารถสร้างโฟลเดอร์ปลายทางได้';
                return $result;
            }
        }
        
        // สร้างชื่อไฟล์ใหม่
        $filename = self::generateUniqueFilename($file['name']);
        $filepath = $destination . '/' . $filename;
        
        // ย้ายไฟล์
        if (move_uploaded_file($file['tmp_name'], $filepath)) {
            $result['success'] = true;
            $result['message'] = 'อัปโหลดไฟล์เรียบร้อยแล้ว';
            $result['filename'] = $filename;
            $result['path'] = $filepath;
        } else {
            $result['message'] = 'เกิดข้อผิดพลาดในการอัปโหลดไฟล์';
        }
        
        return $result;
    }
    
    /**
     * ลบไฟล์
     * 
     * @param string $filepath เส้นทางไฟล์
     * @return bool
     */
    public static function delete($filepath) {
        if (file_exists($filepath)) {
            return unlink($filepath);
        }
        return false;
    }
    
    /**
     * คัดลอกไฟล์
     * 
     * @param string $source ไฟล์ต้นทาง
     * @param string $destination ไฟล์ปลายทาง
     * @return bool
     */
    public static function copy($source, $destination) {
        if (!file_exists($source)) {
            return false;
        }
        
        $destinationDir = dirname($destination);
        if (!is_dir($destinationDir)) {
            mkdir($destinationDir, 0755, true);
        }
        
        return copy($source, $destination);
    }
    
    /**
     * ย้ายไฟล์
     * 
     * @param string $source ไฟล์ต้นทาง
     * @param string $destination ไฟล์ปลายทาง
     * @return bool
     */
    public static function move($source, $destination) {
        if (!file_exists($source)) {
            return false;
        }
        
        $destinationDir = dirname($destination);
        if (!is_dir($destinationDir)) {
            mkdir($destinationDir, 0755, true);
        }
        
        return rename($source, $destination);
    }
    
    /**
     * สร้างชื่อไฟล์ที่ไม่ซ้ำ
     * 
     * @param string $originalName ชื่อไฟล์เดิม
     * @return string
     */
    public static function generateUniqueFilename($originalName) {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $filename = pathinfo($originalName, PATHINFO_FILENAME);
        
        // ทำความสะอาดชื่อไฟล์
        $filename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $filename);
        $filename = preg_replace('/_+/', '_', $filename);
        $filename = trim($filename, '_');
        
        if (empty($filename)) {
            $filename = 'file';
        }
        
        return $filename . '_' . time() . '_' . uniqid() . '.' . $extension;
    }
    
    /**
     * ดึงข้อความข้อผิดพลาดการอัปโหลด
     * 
     * @param int $errorCode รหัสข้อผิดพลาด
     * @return string
     */
    private static function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'ขนาดไฟล์เกินกำหนดในการตั้งค่าเซิร์ฟเวอร์';
            case UPLOAD_ERR_FORM_SIZE:
                return 'ขนาดไฟล์เกินกำหนดในฟอร์ม';
            case UPLOAD_ERR_PARTIAL:
                return 'ไฟล์อัปโหลดไม่สมบูรณ์';
            case UPLOAD_ERR_NO_FILE:
                return 'ไม่มีไฟล์ที่อัปโหลด';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'ไม่พบโฟลเดอร์ชั่วคราว';
            case UPLOAD_ERR_CANT_WRITE:
                return 'ไม่สามารถเขียนไฟล์ลงดิสก์ได้';
            case UPLOAD_ERR_EXTENSION:
                return 'การอัปโหลดถูกหยุดโดย extension';
            default:
                return 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ';
        }
    }
    
    /**
     * ตรวจสอบว่าเป็นรูปภาพหรือไม่
     * 
     * @param string $filepath เส้นทางไฟล์
     * @return bool
     */
    public static function isImage($filepath) {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        $extension = strtolower(pathinfo($filepath, PATHINFO_EXTENSION));
        return in_array($extension, $imageTypes);
    }
    
    /**
     * ปรับขนาดรูปภาพ
     * 
     * @param string $source ไฟล์ต้นทาง
     * @param string $destination ไฟล์ปลายทาง
     * @param int $maxWidth ความกว้างสูงสุด
     * @param int $maxHeight ความสูงสูงสุด
     * @param int $quality คุณภาพ (สำหรับ JPEG)
     * @return bool
     */
    public static function resizeImage($source, $destination, $maxWidth, $maxHeight, $quality = 90) {
        if (!file_exists($source) || !self::isImage($source)) {
            return false;
        }
        
        $imageInfo = getimagesize($source);
        if (!$imageInfo) {
            return false;
        }
        
        $originalWidth = $imageInfo[0];
        $originalHeight = $imageInfo[1];
        $imageType = $imageInfo[2];
        
        // คำนวณขนาดใหม่
        $ratio = min($maxWidth / $originalWidth, $maxHeight / $originalHeight);
        $newWidth = round($originalWidth * $ratio);
        $newHeight = round($originalHeight * $ratio);
        
        // สร้างรูปภาพต้นทาง
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($source);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($source);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($source);
                break;
            default:
                return false;
        }
        
        if (!$sourceImage) {
            return false;
        }
        
        // สร้างรูปภาพใหม่
        $newImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // รักษาความโปร่งใสสำหรับ PNG และ GIF
        if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
            imagealphablending($newImage, false);
            imagesavealpha($newImage, true);
            $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
            imagefilledrectangle($newImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // ปรับขนาด
        imagecopyresampled($newImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $originalWidth, $originalHeight);
        
        // บันทึกรูปภาพ
        $result = false;
        switch ($imageType) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($newImage, $destination, $quality);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($newImage, $destination);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($newImage, $destination);
                break;
        }
        
        // ทำความสะอาด
        imagedestroy($sourceImage);
        imagedestroy($newImage);
        
        return $result;
    }
    
    /**
     * อ่านไฟล์เป็น base64
     * 
     * @param string $filepath เส้นทางไฟล์
     * @return string|false
     */
    public static function toBase64($filepath) {
        if (!file_exists($filepath)) {
            return false;
        }
        
        $data = file_get_contents($filepath);
        if ($data === false) {
            return false;
        }
        
        $mimeType = mime_content_type($filepath);
        return 'data:' . $mimeType . ';base64,' . base64_encode($data);
    }
    
    /**
     * บันทึกไฟล์จาก base64
     * 
     * @param string $base64Data ข้อมูล base64
     * @param string $filepath เส้นทางไฟล์ปลายทาง
     * @return bool
     */
    public static function fromBase64($base64Data, $filepath) {
        // แยกข้อมูล MIME type และ base64
        if (preg_match('/^data:([^;]+);base64,(.+)$/', $base64Data, $matches)) {
            $data = base64_decode($matches[2]);
        } else {
            $data = base64_decode($base64Data);
        }
        
        if ($data === false) {
            return false;
        }
        
        $directory = dirname($filepath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        return file_put_contents($filepath, $data) !== false;
    }
    
    /**
     * ดึงรายการไฟล์ในโฟลเดอร์
     * 
     * @param string $directory โฟลเดอร์
     * @param array $extensions นามสกุลไฟล์ที่ต้องการ
     * @param bool $recursive ค้นหาในโฟลเดอร์ย่อย
     * @return array
     */
    public static function getFiles($directory, $extensions = [], $recursive = false) {
        $files = [];
        
        if (!is_dir($directory)) {
            return $files;
        }
        
        $iterator = $recursive ? 
            new RecursiveIteratorIterator(new RecursiveDirectoryIterator($directory)) :
            new DirectoryIterator($directory);
        
        foreach ($iterator as $file) {
            if ($file->isDot() || $file->isDir()) {
                continue;
            }
            
            $extension = strtolower($file->getExtension());
            
            if (empty($extensions) || in_array($extension, $extensions)) {
                $files[] = [
                    'name' => $file->getFilename(),
                    'path' => $file->getPathname(),
                    'size' => $file->getSize(),
                    'modified' => $file->getMTime(),
                    'extension' => $extension
                ];
            }
        }
        
        return $files;
    }
    
    /**
     * ลบโฟลเดอร์และไฟล์ทั้งหมดข้างใน
     * 
     * @param string $directory โฟลเดอร์
     * @return bool
     */
    public static function deleteDirectory($directory) {
        if (!is_dir($directory)) {
            return false;
        }
        
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($files as $file) {
            if ($file->isDir()) {
                rmdir($file->getRealPath());
            } else {
                unlink($file->getRealPath());
            }
        }
        
        return rmdir($directory);
    }
    
    /**
     * คำนวณ hash ของไฟล์
     * 
     * @param string $filepath เส้นทางไฟล์
     * @param string $algorithm อัลกอริทึม hash
     * @return string|false
     */
    public static function getFileHash($filepath, $algorithm = 'md5') {
        if (!file_exists($filepath)) {
            return false;
        }
        
        return hash_file($algorithm, $filepath);
    }
}
