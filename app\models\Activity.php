<?php
/**
 * Activity Model
 * 
 * โมเดลสำหรับจัดการข้อมูลกิจกรรม
 */

class Activity extends BaseModel {
    protected $table = 'activities';
    protected $primaryKey = 'id';
    protected $fillable = [
        'customer_id',
        'opportunity_id',
        'contact_id',
        'type',
        'subject',
        'description',
        'scheduled_date',
        'scheduled_time',
        'duration',
        'status',
        'outcome',
        'follow_up_date',
        'assigned_to',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];
    
    /**
     * ดึงข้อมูลกิจกรรมของลูกค้า
     * 
     * @param int $customerId ID ของลูกค้า
     * @return array
     */
    public function getByCustomerId($customerId) {
        return $this->getWhere(['customer_id' => $customerId], 'AND');
    }
    
    /**
     * ดึงข้อมูลกิจกรรมของโอกาสการขาย
     * 
     * @param int $opportunityId ID ของโอกาสการขาย
     * @return array
     */
    public function getByOpportunityId($opportunityId) {
        return $this->getWhere(['opportunity_id' => $opportunityId], 'AND');
    }
    
    /**
     * ดึงข้อมูลกิจกรรมที่มอบหมายให้ผู้ใช้
     * 
     * @param int $userId ID ของผู้ใช้
     * @return array
     */
    public function getByAssignedUser($userId) {
        return $this->getWhere(['assigned_to' => $userId], 'AND');
    }
    
    /**
     * ดึงข้อมูลกิจกรรมที่กำลังจะมาถึง
     * 
     * @param int $userId ID ของผู้ใช้ (ถ้ามี)
     * @param int $days จำนวนวันข้างหน้า
     * @return array
     */
    public function getUpcomingActivities($userId = null, $days = 7) {
        $sql = "SELECT a.*, c.company_name, o.title as opportunity_title, 
                       ct.first_name, ct.last_name, u.first_name as assigned_first_name, u.last_name as assigned_last_name
                FROM " . TABLE_PREFIX . $this->table . " a
                LEFT JOIN " . TABLE_PREFIX . "customers c ON a.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON a.opportunity_id = o.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON a.contact_id = ct.id
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.assigned_to = u.id
                WHERE a.status = :status 
                AND a.scheduled_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)";
        
        $params = [
            ':status' => ACTIVITY_STATUS_PLANNED,
            ':days' => $days
        ];
        
        if ($userId) {
            $sql .= " AND a.assigned_to = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $sql .= " ORDER BY a.scheduled_date ASC, a.scheduled_time ASC";
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลกิจกรรมที่เลยกำหนดแล้ว
     * 
     * @param int $userId ID ของผู้ใช้ (ถ้ามี)
     * @return array
     */
    public function getOverdueActivities($userId = null) {
        $sql = "SELECT a.*, c.company_name, o.title as opportunity_title, 
                       ct.first_name, ct.last_name, u.first_name as assigned_first_name, u.last_name as assigned_last_name
                FROM " . TABLE_PREFIX . $this->table . " a
                LEFT JOIN " . TABLE_PREFIX . "customers c ON a.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON a.opportunity_id = o.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON a.contact_id = ct.id
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.assigned_to = u.id
                WHERE a.status = :status 
                AND (a.scheduled_date < CURDATE() OR 
                     (a.scheduled_date = CURDATE() AND a.scheduled_time < CURTIME()))";
        
        $params = [':status' => ACTIVITY_STATUS_PLANNED];
        
        if ($userId) {
            $sql .= " AND a.assigned_to = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $sql .= " ORDER BY a.scheduled_date DESC, a.scheduled_time DESC";
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลกิจกรรมพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param int $activityId ID ของกิจกรรม
     * @return array|bool
     */
    public function getActivityWithRelations($activityId) {
        $sql = "SELECT a.*, c.company_name, o.title as opportunity_title, 
                       ct.first_name, ct.last_name, ct.email, ct.phone,
                       u.first_name as assigned_first_name, u.last_name as assigned_last_name,
                       creator.first_name as creator_first_name, creator.last_name as creator_last_name
                FROM " . TABLE_PREFIX . $this->table . " a
                LEFT JOIN " . TABLE_PREFIX . "customers c ON a.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON a.opportunity_id = o.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON a.contact_id = ct.id
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.assigned_to = u.id
                LEFT JOIN " . TABLE_PREFIX . "users creator ON a.created_by = creator.id
                WHERE a.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $activityId]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลกิจกรรมทั้งหมดพร้อมข้อมูลที่เกี่ยวข้อง
     * 
     * @param array $filters ตัวกรอง
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function getAllActivitiesWithRelations($filters = [], $page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // สร้าง WHERE clause
        $whereClause = [];
        $params = [];
        
        if (!empty($filters['customer_id'])) {
            $whereClause[] = "a.customer_id = :customer_id";
            $params[':customer_id'] = $filters['customer_id'];
        }
        
        if (!empty($filters['opportunity_id'])) {
            $whereClause[] = "a.opportunity_id = :opportunity_id";
            $params[':opportunity_id'] = $filters['opportunity_id'];
        }
        
        if (!empty($filters['assigned_to'])) {
            $whereClause[] = "a.assigned_to = :assigned_to";
            $params[':assigned_to'] = $filters['assigned_to'];
        }
        
        if (!empty($filters['type'])) {
            $whereClause[] = "a.type = :type";
            $params[':type'] = $filters['type'];
        }
        
        if (!empty($filters['status'])) {
            $whereClause[] = "a.status = :status";
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['date_from'])) {
            $whereClause[] = "a.scheduled_date >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $whereClause[] = "a.scheduled_date <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }
        
        $whereStr = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total 
                     FROM " . TABLE_PREFIX . $this->table . " a " . $whereStr;
        
        $this->db->prepare($countSql);
        if (!empty($params)) {
            $this->db->bind($params);
        }
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT a.*, c.company_name, o.title as opportunity_title, 
                       ct.first_name, ct.last_name,
                       u.first_name as assigned_first_name, u.last_name as assigned_last_name
                FROM " . TABLE_PREFIX . $this->table . " a
                LEFT JOIN " . TABLE_PREFIX . "customers c ON a.customer_id = c.id
                LEFT JOIN " . TABLE_PREFIX . "opportunities o ON a.opportunity_id = o.id
                LEFT JOIN " . TABLE_PREFIX . "contacts ct ON a.contact_id = ct.id
                LEFT JOIN " . TABLE_PREFIX . "users u ON a.assigned_to = u.id
                " . $whereStr . "
                ORDER BY a.scheduled_date DESC, a.scheduled_time DESC
                LIMIT :offset, :perPage";
        
        $params[':offset'] = $offset;
        $params[':perPage'] = $perPage;
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * อัปเดตสถานะกิจกรรม
     * 
     * @param int $activityId ID ของกิจกรรม
     * @param string $status สถานะใหม่
     * @param string $outcome ผลลัพธ์ (ถ้ามี)
     * @return bool
     */
    public function updateStatus($activityId, $status, $outcome = null) {
        $data = ['status' => $status];
        
        if ($outcome !== null) {
            $data['outcome'] = $outcome;
        }
        
        return $this->update($activityId, $data);
    }
    
    /**
     * นับจำนวนกิจกรรมตามสถานะ
     * 
     * @param int $userId ID ของผู้ใช้ (ถ้ามี)
     * @return array
     */
    public function getActivityCountByStatus($userId = null) {
        $sql = "SELECT status, COUNT(*) as count 
                FROM " . TABLE_PREFIX . $this->table;
        
        $params = [];
        
        if ($userId) {
            $sql .= " WHERE assigned_to = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $sql .= " GROUP BY status";
        
        $this->db->prepare($sql);
        
        if (!empty($params)) {
            $this->db->bind($params);
        }
        
        $this->db->execute();
        
        $results = $this->db->fetchAll();
        
        // จัดรูปแบบผลลัพธ์
        $counts = [
            ACTIVITY_STATUS_PLANNED => 0,
            ACTIVITY_STATUS_COMPLETED => 0,
            ACTIVITY_STATUS_CANCELLED => 0
        ];
        
        foreach ($results as $result) {
            $counts[$result['status']] = $result['count'];
        }
        
        return $counts;
    }
}
