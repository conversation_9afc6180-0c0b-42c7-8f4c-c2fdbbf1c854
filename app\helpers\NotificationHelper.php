<?php
/**
 * Notification Helper
 * 
 * คลาสช่วยเหลือสำหรับการจัดการการแจ้งเตือน
 */

class NotificationHelper {
    
    /**
     * ส่งการแจ้งเตือนให้ผู้ใช้
     * 
     * @param int $userId ID ของผู้ใช้
     * @param string $type ประเภทการแจ้งเตือน
     * @param string $title หัวข้อ
     * @param string $message ข้อความ
     * @param array $data ข้อมูลเพิ่มเติม
     * @return bool
     */
    public static function send($userId, $type, $title, $message, $data = []) {
        $notificationModel = new Notification();
        return $notificationModel->createNotification($userId, $type, $title, $message, $data);
    }
    
    /**
     * ส่งการแจ้งเตือนให้หลายผู้ใช้
     * 
     * @param array $userIds รายการ ID ของผู้ใช้
     * @param string $type ประเภทการแจ้งเตือน
     * @param string $title หัวข้อ
     * @param string $message ข้อความ
     * @param array $data ข้อมูลเพิ่มเติม
     * @return bool
     */
    public static function sendToMultiple($userIds, $type, $title, $message, $data = []) {
        $notificationModel = new Notification();
        $success = true;
        
        foreach ($userIds as $userId) {
            if (!$notificationModel->createNotification($userId, $type, $title, $message, $data)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * ส่งการแจ้งเตือนให้ผู้ใช้ตามบทบาท
     * 
     * @param string $role บทบาทผู้ใช้
     * @param string $type ประเภทการแจ้งเตือน
     * @param string $title หัวข้อ
     * @param string $message ข้อความ
     * @param array $data ข้อมูลเพิ่มเติม
     * @return bool
     */
    public static function sendToRole($role, $type, $title, $message, $data = []) {
        $userModel = new User();
        $users = $userModel->getByRole($role);
        
        $userIds = array_column($users, 'id');
        return self::sendToMultiple($userIds, $type, $title, $message, $data);
    }
    
    /**
     * ส่งการแจ้งเตือนเกี่ยวกับโอกาสการขาย
     * 
     * @param int $opportunityId ID ของโอกาสการขาย
     * @param string $action การกระทำ
     * @param int $assignedTo ผู้รับผิดชอบ
     * @return bool
     */
    public static function sendOpportunityNotification($opportunityId, $action, $assignedTo = null) {
        $opportunityModel = new Opportunity();
        $opportunity = $opportunityModel->getById($opportunityId);
        
        if (!$opportunity) {
            return false;
        }
        
        $messages = [
            'created' => 'โอกาสการขายใหม่ถูกสร้างขึ้น',
            'updated' => 'โอกาสการขายถูกอัปเดต',
            'stage_changed' => 'ขั้นตอนของโอกาสการขายเปลี่ยนแปลง',
            'won' => 'โอกาสการขายสำเร็จ! 🎉',
            'lost' => 'โอกาสการขายไม่สำเร็จ',
            'assigned' => 'โอกาสการขายถูกมอบหมายให้คุณ'
        ];
        
        $title = $messages[$action] ?? 'โอกาสการขายมีการเปลี่ยนแปลง';
        $message = "โอกาสการขาย: {$opportunity['title']}";
        
        $data = [
            'opportunity_id' => $opportunityId,
            'action' => $action,
            'customer_name' => $opportunity['customer_name'] ?? ''
        ];
        
        // ส่งให้ผู้รับผิดชอบ
        $userId = $assignedTo ?: $opportunity['assigned_to'];
        if ($userId) {
            self::send($userId, NOTIFICATION_TYPE_OPPORTUNITY, $title, $message, $data);
        }
        
        // ส่งให้ผู้จัดการถ้าเป็นการเปลี่ยนแปลงสำคัญ
        if (in_array($action, ['won', 'lost', 'stage_changed'])) {
            self::sendToRole(ROLE_MANAGER, NOTIFICATION_TYPE_OPPORTUNITY, $title, $message, $data);
        }
        
        return true;
    }
    
    /**
     * ส่งการแจ้งเตือนเกี่ยวกับใบเสนอราคา
     * 
     * @param int $quotationId ID ของใบเสนอราคา
     * @param string $action การกระทำ
     * @return bool
     */
    public static function sendQuotationNotification($quotationId, $action) {
        $quotationModel = new Quotation();
        $quotation = $quotationModel->getById($quotationId);
        
        if (!$quotation) {
            return false;
        }
        
        $messages = [
            'created' => 'ใบเสนอราคาใหม่ถูกสร้างขึ้น',
            'sent' => 'ใบเสนอราคาถูกส่งให้ลูกค้าแล้ว',
            'accepted' => 'ใบเสนอราคาได้รับการยอมรับ! 🎉',
            'rejected' => 'ใบเสนอราคาถูกปฏิเสธ',
            'expired' => 'ใบเสนอราคาหมดอายุแล้ว',
            'updated' => 'ใบเสนอราคาถูกอัปเดต'
        ];
        
        $title = $messages[$action] ?? 'ใบเสนอราคามีการเปลี่ยนแปลง';
        $message = "ใบเสนอราคา: {$quotation['quotation_number']}";
        
        $data = [
            'quotation_id' => $quotationId,
            'action' => $action,
            'customer_name' => $quotation['customer_name'] ?? '',
            'amount' => $quotation['total_amount'] ?? 0
        ];
        
        // ส่งให้ผู้สร้าง
        if ($quotation['created_by']) {
            self::send($quotation['created_by'], NOTIFICATION_TYPE_QUOTATION, $title, $message, $data);
        }
        
        // ส่งให้ผู้จัดการถ้าเป็นการเปลี่ยนแปลงสำคัญ
        if (in_array($action, ['accepted', 'rejected', 'expired'])) {
            self::sendToRole(ROLE_MANAGER, NOTIFICATION_TYPE_QUOTATION, $title, $message, $data);
        }
        
        return true;
    }
    
    /**
     * ส่งการแจ้งเตือนเกี่ยวกับใบแจ้งหนี้
     * 
     * @param int $invoiceId ID ของใบแจ้งหนี้
     * @param string $action การกระทำ
     * @return bool
     */
    public static function sendInvoiceNotification($invoiceId, $action) {
        $invoiceModel = new Invoice();
        $invoice = $invoiceModel->getById($invoiceId);
        
        if (!$invoice) {
            return false;
        }
        
        $messages = [
            'created' => 'ใบแจ้งหนี้ใหม่ถูกสร้างขึ้น',
            'sent' => 'ใบแจ้งหนี้ถูกส่งให้ลูกค้าแล้ว',
            'paid' => 'ใบแจ้งหนี้ได้รับการชำระเงินแล้ว! 💰',
            'partial_paid' => 'ใบแจ้งหนี้ได้รับการชำระเงินบางส่วน',
            'overdue' => 'ใบแจ้งหนี้เลยกำหนดชำระแล้ว! ⚠️',
            'cancelled' => 'ใบแจ้งหนี้ถูกยกเลิก'
        ];
        
        $title = $messages[$action] ?? 'ใบแจ้งหนี้มีการเปลี่ยนแปลง';
        $message = "ใบแจ้งหนี้: {$invoice['invoice_number']}";
        
        $data = [
            'invoice_id' => $invoiceId,
            'action' => $action,
            'customer_name' => $invoice['customer_name'] ?? '',
            'amount' => $invoice['total_amount'] ?? 0,
            'due_date' => $invoice['due_date'] ?? ''
        ];
        
        // ส่งให้ผู้สร้าง
        if ($invoice['created_by']) {
            self::send($invoice['created_by'], NOTIFICATION_TYPE_INVOICE, $title, $message, $data);
        }
        
        // ส่งให้ผู้จัดการและแอดมิน
        if (in_array($action, ['paid', 'overdue', 'cancelled'])) {
            self::sendToRole(ROLE_MANAGER, NOTIFICATION_TYPE_INVOICE, $title, $message, $data);
            self::sendToRole(ROLE_ADMIN, NOTIFICATION_TYPE_INVOICE, $title, $message, $data);
        }
        
        return true;
    }
    
    /**
     * ส่งการแจ้งเตือนเกี่ยวกับกิจกรรม
     * 
     * @param int $activityId ID ของกิจกรรม
     * @param string $action การกระทำ
     * @return bool
     */
    public static function sendActivityNotification($activityId, $action) {
        $activityModel = new Activity();
        $activity = $activityModel->getById($activityId);
        
        if (!$activity) {
            return false;
        }
        
        $messages = [
            'assigned' => 'กิจกรรมใหม่ถูกมอบหมายให้คุณ',
            'reminder' => 'เตือน: กิจกรรมกำลังจะมาถึง! ⏰',
            'overdue' => 'กิจกรรมเลยกำหนดแล้ว! ⚠️',
            'completed' => 'กิจกรรมเสร็จสิ้นแล้ว ✅',
            'updated' => 'กิจกรรมถูกอัปเดต'
        ];
        
        $title = $messages[$action] ?? 'กิจกรรมมีการเปลี่ยนแปลง';
        $message = "กิจกรรม: {$activity['subject']}";
        
        $data = [
            'activity_id' => $activityId,
            'action' => $action,
            'customer_name' => $activity['customer_name'] ?? '',
            'scheduled_date' => $activity['scheduled_date'] ?? ''
        ];
        
        // ส่งให้ผู้รับผิดชอบ
        if ($activity['assigned_to']) {
            self::send($activity['assigned_to'], NOTIFICATION_TYPE_ACTIVITY, $title, $message, $data);
        }
        
        return true;
    }
    
    /**
     * ส่งการแจ้งเตือนเตือนกิจกรรมที่กำลังจะมาถึง
     * 
     * @param int $hours จำนวนชั่วโมงข้างหน้า
     * @return bool
     */
    public static function sendUpcomingActivityReminders($hours = 24) {
        $activityModel = new Activity();
        $activities = $activityModel->getUpcomingActivities(null, $hours);
        
        foreach ($activities as $activity) {
            self::sendActivityNotification($activity['id'], 'reminder');
        }
        
        return true;
    }
    
    /**
     * ส่งการแจ้งเตือนใบแจ้งหนี้ที่เลยกำหนด
     * 
     * @return bool
     */
    public static function sendOverdueInvoiceNotifications() {
        $invoiceModel = new Invoice();
        $overdueInvoices = $invoiceModel->getOverdueInvoices();
        
        foreach ($overdueInvoices as $invoice) {
            self::sendInvoiceNotification($invoice['id'], 'overdue');
        }
        
        return true;
    }
    
    /**
     * ส่งอีเมลการแจ้งเตือน (ถ้าเปิดใช้งาน)
     * 
     * @param int $userId ID ของผู้ใช้
     * @param string $subject หัวข้ออีเมล
     * @param string $message ข้อความ
     * @return bool
     */
    public static function sendEmailNotification($userId, $subject, $message) {
        // ตรวจสอบการตั้งค่า
        $settingModel = new Setting();
        $emailEnabled = $settingModel->getValue('notification_email_enabled', false);
        
        if (!$emailEnabled) {
            return false;
        }
        
        // ดึงข้อมูลผู้ใช้
        $userModel = new User();
        $user = $userModel->getById($userId);
        
        if (!$user || empty($user['email'])) {
            return false;
        }
        
        // TODO: ส่งอีเมลจริง (ใช้ PHPMailer หรือ library อื่น)
        // ในที่นี้จะ log แทน
        Logger::info('Email notification sent', [
            'user_id' => $userId,
            'email' => $user['email'],
            'subject' => $subject,
            'message' => $message
        ]);
        
        return true;
    }
    
    /**
     * ดึงจำนวนการแจ้งเตือนที่ยังไม่ได้อ่าน
     * 
     * @param int $userId ID ของผู้ใช้
     * @return int
     */
    public static function getUnreadCount($userId) {
        $notificationModel = new Notification();
        return $notificationModel->getUnreadCount($userId);
    }
    
    /**
     * ทำเครื่องหมายการแจ้งเตือนว่าอ่านแล้ว
     * 
     * @param int $notificationId ID ของการแจ้งเตือน
     * @return bool
     */
    public static function markAsRead($notificationId) {
        $notificationModel = new Notification();
        return $notificationModel->markAsRead($notificationId);
    }
    
    /**
     * ทำเครื่องหมายการแจ้งเตือนทั้งหมดว่าอ่านแล้ว
     * 
     * @param int $userId ID ของผู้ใช้
     * @return bool
     */
    public static function markAllAsRead($userId) {
        $notificationModel = new Notification();
        return $notificationModel->markAllAsRead($userId);
    }
    
    /**
     * ลบการแจ้งเตือนเก่า
     * 
     * @param int $days จำนวนวันที่เก่ากว่า
     * @return bool
     */
    public static function cleanOldNotifications($days = 30) {
        $notificationModel = new Notification();
        return $notificationModel->deleteOldNotifications($days);
    }
}
