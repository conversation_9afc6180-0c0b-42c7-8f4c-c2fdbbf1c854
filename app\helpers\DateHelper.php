<?php
/**
 * Date Helper
 * 
 * คลาสช่วยเหลือสำหรับการจัดการวันที่และเวลา
 */

class DateHelper {
    
    /**
     * แปลงวันที่เป็นรูปแบบไทย
     * 
     * @param string $date วันที่
     * @param string $format รูปแบบที่ต้องการ
     * @return string
     */
    public static function formatThai($date, $format = 'd/m/Y') {
        if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return '-';
        }
        
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        
        if ($timestamp === false) {
            return '-';
        }
        
        return date($format, $timestamp);
    }
    
    /**
     * แปลงวันที่เป็นรูปแบบไทยพร้อมชื่อเดือน
     * 
     * @param string $date วันที่
     * @param bool $shortMonth ใช้ชื่อเดือนแบบสั้น
     * @return string
     */
    public static function formatThaiWithMonth($date, $shortMonth = false) {
        if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return '-';
        }
        
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        
        if ($timestamp === false) {
            return '-';
        }
        
        $day = date('j', $timestamp);
        $month = date('n', $timestamp);
        $year = date('Y', $timestamp) + 543; // แปลงเป็น พ.ศ.
        
        $thaiMonths = $shortMonth ? self::getThaiMonthsShort() : self::getThaiMonths();
        
        return $day . ' ' . $thaiMonths[$month] . ' ' . $year;
    }
    
    /**
     * แปลงวันที่และเวลาเป็นรูปแบบไทย
     * 
     * @param string $datetime วันที่และเวลา
     * @param bool $shortMonth ใช้ชื่อเดือนแบบสั้น
     * @return string
     */
    public static function formatThaiDateTime($datetime, $shortMonth = false) {
        if (empty($datetime) || $datetime === '0000-00-00 00:00:00') {
            return '-';
        }
        
        $timestamp = is_numeric($datetime) ? $datetime : strtotime($datetime);
        
        if ($timestamp === false) {
            return '-';
        }
        
        $dateStr = self::formatThaiWithMonth(date('Y-m-d', $timestamp), $shortMonth);
        $timeStr = date('H:i', $timestamp) . ' น.';
        
        return $dateStr . ' เวลา ' . $timeStr;
    }
    
    /**
     * คำนวณอายุจากวันเกิด
     * 
     * @param string $birthDate วันเกิด
     * @return int
     */
    public static function calculateAge($birthDate) {
        if (empty($birthDate) || $birthDate === '0000-00-00') {
            return 0;
        }
        
        $birth = new DateTime($birthDate);
        $today = new DateTime();
        
        return $today->diff($birth)->y;
    }
    
    /**
     * คำนวณจำนวนวันระหว่างวันที่
     * 
     * @param string $startDate วันที่เริ่มต้น
     * @param string $endDate วันที่สิ้นสุด
     * @return int
     */
    public static function daysBetween($startDate, $endDate) {
        $start = new DateTime($startDate);
        $end = new DateTime($endDate);
        
        return $end->diff($start)->days;
    }
    
    /**
     * ตรวจสอบว่าวันที่อยู่ในอดีตหรือไม่
     * 
     * @param string $date วันที่
     * @return bool
     */
    public static function isPast($date) {
        $dateTime = new DateTime($date);
        $now = new DateTime();
        
        return $dateTime < $now;
    }
    
    /**
     * ตรวจสอบว่าวันที่อยู่ในอนาคตหรือไม่
     * 
     * @param string $date วันที่
     * @return bool
     */
    public static function isFuture($date) {
        $dateTime = new DateTime($date);
        $now = new DateTime();
        
        return $dateTime > $now;
    }
    
    /**
     * ตรวจสอบว่าเป็นวันนี้หรือไม่
     * 
     * @param string $date วันที่
     * @return bool
     */
    public static function isToday($date) {
        return date('Y-m-d', strtotime($date)) === date('Y-m-d');
    }
    
    /**
     * แปลงวันที่เป็นรูปแบบสัมพัทธ์ (เช่น 2 วันที่แล้ว)
     * 
     * @param string $date วันที่
     * @return string
     */
    public static function timeAgo($date) {
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        $diff = time() - $timestamp;
        
        if ($diff < 60) {
            return 'เมื่อสักครู่';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . ' นาทีที่แล้ว';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' ชั่วโมงที่แล้ว';
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return $days . ' วันที่แล้ว';
        } elseif ($diff < 31536000) {
            $months = floor($diff / 2592000);
            return $months . ' เดือนที่แล้ว';
        } else {
            $years = floor($diff / 31536000);
            return $years . ' ปีที่แล้ว';
        }
    }
    
    /**
     * เพิ่มวันทำการ (ไม่รวมเสาร์-อาทิตย์)
     * 
     * @param string $date วันที่เริ่มต้น
     * @param int $days จำนวนวันทำการที่ต้องการเพิ่ม
     * @return string
     */
    public static function addBusinessDays($date, $days) {
        $currentDate = new DateTime($date);
        $addedDays = 0;
        
        while ($addedDays < $days) {
            $currentDate->add(new DateInterval('P1D'));
            
            // ตรวจสอบว่าเป็นวันทำการหรือไม่ (จันทร์-ศุกร์)
            if ($currentDate->format('N') < 6) {
                $addedDays++;
            }
        }
        
        return $currentDate->format('Y-m-d');
    }
    
    /**
     * ดึงรายการเดือนภาษาไทย
     * 
     * @return array
     */
    public static function getThaiMonths() {
        return [
            1 => 'มกราคม',
            2 => 'กุมภาพันธ์',
            3 => 'มีนาคม',
            4 => 'เมษายน',
            5 => 'พฤษภาคม',
            6 => 'มิถุนายน',
            7 => 'กรกฎาคม',
            8 => 'สิงหาคม',
            9 => 'กันยายน',
            10 => 'ตุลาคม',
            11 => 'พฤศจิกายน',
            12 => 'ธันวาคม'
        ];
    }
    
    /**
     * ดึงรายการเดือนภาษาไทยแบบสั้น
     * 
     * @return array
     */
    public static function getThaiMonthsShort() {
        return [
            1 => 'ม.ค.',
            2 => 'ก.พ.',
            3 => 'มี.ค.',
            4 => 'เม.ย.',
            5 => 'พ.ค.',
            6 => 'มิ.ย.',
            7 => 'ก.ค.',
            8 => 'ส.ค.',
            9 => 'ก.ย.',
            10 => 'ต.ค.',
            11 => 'พ.ย.',
            12 => 'ธ.ค.'
        ];
    }
    
    /**
     * ดึงรายการวันภาษาไทย
     * 
     * @return array
     */
    public static function getThaiDays() {
        return [
            1 => 'จันทร์',
            2 => 'อังคาร',
            3 => 'พุธ',
            4 => 'พฤหัสบดี',
            5 => 'ศุกร์',
            6 => 'เสาร์',
            7 => 'อาทิตย์'
        ];
    }
    
    /**
     * ดึงรายการวันภาษาไทยแบบสั้น
     * 
     * @return array
     */
    public static function getThaiDaysShort() {
        return [
            1 => 'จ.',
            2 => 'อ.',
            3 => 'พ.',
            4 => 'พฤ.',
            5 => 'ศ.',
            6 => 'ส.',
            7 => 'อา.'
        ];
    }
    
    /**
     * แปลงวันที่จาก พ.ศ. เป็น ค.ศ.
     * 
     * @param string $buddhistDate วันที่ พ.ศ.
     * @return string
     */
    public static function buddhistToChristian($buddhistDate) {
        if (empty($buddhistDate)) {
            return '';
        }
        
        // แยกส่วนของวันที่
        $parts = explode('-', $buddhistDate);
        if (count($parts) === 3) {
            $year = intval($parts[0]) - 543;
            return $year . '-' . $parts[1] . '-' . $parts[2];
        }
        
        return $buddhistDate;
    }
    
    /**
     * แปลงวันที่จาก ค.ศ. เป็น พ.ศ.
     * 
     * @param string $christianDate วันที่ ค.ศ.
     * @return string
     */
    public static function christianToBuddhist($christianDate) {
        if (empty($christianDate)) {
            return '';
        }
        
        // แยกส่วนของวันที่
        $parts = explode('-', $christianDate);
        if (count($parts) === 3) {
            $year = intval($parts[0]) + 543;
            return $year . '-' . $parts[1] . '-' . $parts[2];
        }
        
        return $christianDate;
    }
    
    /**
     * สร้างรายการวันที่ในช่วงที่กำหนด
     * 
     * @param string $startDate วันที่เริ่มต้น
     * @param string $endDate วันที่สิ้นสุด
     * @param string $format รูปแบบวันที่
     * @return array
     */
    public static function getDateRange($startDate, $endDate, $format = 'Y-m-d') {
        $dates = [];
        $current = new DateTime($startDate);
        $end = new DateTime($endDate);
        
        while ($current <= $end) {
            $dates[] = $current->format($format);
            $current->add(new DateInterval('P1D'));
        }
        
        return $dates;
    }
    
    /**
     * ตรวจสอบว่าปีเป็นปีอธิกสุรทินหรือไม่
     * 
     * @param int $year ปี
     * @return bool
     */
    public static function isLeapYear($year) {
        return date('L', mktime(0, 0, 0, 1, 1, $year)) == 1;
    }
    
    /**
     * ดึงจำนวนวันในเดือน
     * 
     * @param int $month เดือน
     * @param int $year ปี
     * @return int
     */
    public static function getDaysInMonth($month, $year) {
        return date('t', mktime(0, 0, 0, $month, 1, $year));
    }
}
