<?php
/**
 * Main Entry Point
 * 
 * ไฟล์หลักสำหรับเริ่มต้นการทำงานของระบบ
 */

// เริ่มต้น Session
session_name('sales_tracking_session');
session_start();

// กำหนด Error Reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// โหลดไฟล์การตั้งค่า
require_once 'app/config/config.php';
require_once 'app/config/database.php';
require_once 'app/config/constants.php';
require_once 'app/config/functions.php';

// โหลดคลาสพื้นฐาน
require_once 'app/models/Database.php';
require_once 'app/models/BaseModel.php';

// โหลดคลาสโมเดล
require_once 'app/models/User.php';
require_once 'app/models/Customer.php';
require_once 'app/models/Contact.php';
require_once 'app/models/Opportunity.php';
require_once 'app/models/Activity.php';
require_once 'app/models/Product.php';
require_once 'app/models/Quotation.php';
require_once 'app/models/Invoice.php';
require_once 'app/models/Payment.php';
require_once 'app/models/SalesTarget.php';
require_once 'app/models/Notification.php';
require_once 'app/models/Setting.php';

// โหลดคลาสคอนโทรลเลอร์
require_once 'app/controllers/BaseController.php';
require_once 'app/controllers/AuthController.php';
require_once 'app/controllers/DashboardController.php';
require_once 'app/controllers/CustomerController.php';
require_once 'app/controllers/ContactController.php';
require_once 'app/controllers/OpportunityController.php';
require_once 'app/controllers/ActivityController.php';
require_once 'app/controllers/ProductController.php';
require_once 'app/controllers/QuotationController.php';
require_once 'app/controllers/InvoiceController.php';
require_once 'app/controllers/PaymentController.php';
require_once 'app/controllers/SalesTargetController.php';
require_once 'app/controllers/ReportController.php';
require_once 'app/controllers/SettingController.php';
require_once 'app/controllers/UserController.php';
require_once 'app/controllers/NotificationController.php';

// โหลดคลาสช่วยเหลือ
require_once 'app/helpers/AuthHelper.php';
require_once 'app/helpers/ValidationHelper.php';
require_once 'app/helpers/FormHelper.php';
require_once 'app/helpers/DateHelper.php';
require_once 'app/helpers/NumberHelper.php';
require_once 'app/helpers/FileHelper.php';
require_once 'app/helpers/NotificationHelper.php';

// โหลดคลาสยูทิลิตี้
require_once 'app/utils/Logger.php';
require_once 'app/utils/Mailer.php';
require_once 'app/utils/Pdf.php';
require_once 'app/utils/Excel.php';

// กำหนดตัวแปรสำหรับเส้นทาง URL
$controller = isset($_GET['controller']) ? $_GET['controller'] : 'dashboard';
$action = isset($_GET['action']) ? $_GET['action'] : 'index';
$id = isset($_GET['id']) ? $_GET['id'] : null;

// ตรวจสอบการเข้าสู่ระบบ
$authHelper = new AuthHelper();
$isLoggedIn = $authHelper->isLoggedIn();

// ถ้ายังไม่ได้เข้าสู่ระบบและไม่ได้อยู่ที่หน้าเข้าสู่ระบบ ให้เปลี่ยนเส้นทางไปยังหน้าเข้าสู่ระบบ
if (!$isLoggedIn && $controller != 'auth') {
    header('Location: index.php?controller=auth&action=login');
    exit;
}

// ถ้าเข้าสู่ระบบแล้วและอยู่ที่หน้าเข้าสู่ระบบ ให้เปลี่ยนเส้นทางไปยังหน้าแดชบอร์ด
if ($isLoggedIn && $controller == 'auth' && $action == 'login') {
    header('Location: index.php?controller=dashboard&action=index');
    exit;
}

// ตรวจสอบสิทธิ์การเข้าถึง
if ($isLoggedIn && $controller != 'auth') {
    $user = $_SESSION['user'];
    $role = $user['role_id'];
    
    // ตรวจสอบสิทธิ์การเข้าถึงตามบทบาท
    if (!$authHelper->hasPermission($role, $controller, $action)) {
        // ถ้าไม่มีสิทธิ์เข้าถึง ให้แสดงหน้าข้อผิดพลาด
        include 'views/errors/403.php';
        exit;
    }
}

// สร้างชื่อคลาสคอนโทรลเลอร์
$controllerName = ucfirst($controller) . 'Controller';

// ตรวจสอบว่าคลาสคอนโทรลเลอร์มีอยู่จริงหรือไม่
if (!class_exists($controllerName)) {
    // ถ้าไม่มีคลาสคอนโทรลเลอร์ ให้แสดงหน้าข้อผิดพลาด
    include 'views/errors/404.php';
    exit;
}

// สร้างอ็อบเจ็กต์คอนโทรลเลอร์
$controllerObj = new $controllerName();

// ตรวจสอบว่าเมธอดมีอยู่จริงหรือไม่
if (!method_exists($controllerObj, $action)) {
    // ถ้าไม่มีเมธอด ให้แสดงหน้าข้อผิดพลาด
    include 'views/errors/404.php';
    exit;
}

// เรียกใช้เมธอดของคอนโทรลเลอร์
$controllerObj->$action($id);

