<?php
/**
 * Invoice Controller
 */

class InvoiceController extends BaseController {
    private $invoiceModel;
    private $customerModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->invoiceModel = new Invoice();
        $this->customerModel = new Customer();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $invoices = $this->invoiceModel->getPaginated($page);
        $this->setPagination($invoices);
        
        $this->data['invoices'] = $invoices['items'];
        $this->data['page_title'] = 'รายการใบแจ้งหนี้';
        $this->view('invoices/index');
    }
    
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $invoice = $this->invoiceModel->getById($id);
        if (!$invoice) {
            $this->show404();
            return;
        }
        
        $this->data['invoice'] = $invoice;
        $this->data['page_title'] = 'รายละเอียดใบแจ้งหนี้';
        $this->view('invoices/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'สร้างใบแจ้งหนี้ใหม่';
        $this->view('invoices/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=invoice&action=create');
            return;
        }
        
        $data = $this->post();
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->invoiceModel->create($data);
        
        if ($id) {
            showAlert('สร้างใบแจ้งหนี้เรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=invoice&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=invoice&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $invoice = $this->invoiceModel->getById($id);
        if (!$invoice) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['invoice'] = $invoice;
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขใบแจ้งหนี้';
        $this->view('invoices/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=invoice&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->invoiceModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขใบแจ้งหนี้เรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=invoice&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=invoice&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->invoiceModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
