# คู่มือการติดตั้งและการดูแลระบบติดตามงานขายและการเสนอราคา

## สารบัญ
1. [ความต้องการของระบบ](#ความต้องการของระบบ)
2. [การติดตั้งระบบ](#การติดตั้งระบบ)
3. [การตั้งค่าฐานข้อมูล](#การตั้งค่าฐานข้อมูล)
4. [การตั้งค่าระบบ](#การตั้งค่าระบบ)
5. [การอัปเดตระบบ](#การอัปเดตระบบ)
6. [การสำรองและกู้คืนข้อมูล](#การสำรองและกู้คืนข้อมูล)
7. [การแก้ไขปัญหาที่พบบ่อย](#การแก้ไขปัญหาที่พบบ่อย)
8. [การติดต่อขอความช่วยเหลือ](#การติดต่อขอความช่วยเหลือ)

## ความต้องการของระบบ

### ความต้องการด้านฮาร์ดแวร์
- CPU: 2 คอร์ขึ้นไป
- RAM: 4GB ขึ้นไป
- พื้นที่ดิสก์: 10GB ขึ้นไป

### ความต้องการด้านซอฟต์แวร์
- ระบบปฏิบัติการ: Windows Server, Linux (Ubuntu, CentOS, Debian) หรือ macOS
- เว็บเซิร์ฟเวอร์: Apache 2.4+ หรือ Nginx 1.18+
- PHP: 7.4+ หรือ 8.0+
- MySQL: 5.7+ หรือ MariaDB 10.3+
- PHP Extensions:
  - PDO
  - MySQLi
  - GD
  - Mbstring
  - XML
  - Zip
  - Curl
  - OpenSSL
  - JSON

## การติดตั้งระบบ

### การติดตั้งบนเซิร์ฟเวอร์ส่วนตัว

1. **เตรียมเซิร์ฟเวอร์**
   - ติดตั้งเว็บเซิร์ฟเวอร์ (Apache หรือ Nginx)
   - ติดตั้ง PHP และ Extensions ที่จำเป็น
   - ติดตั้ง MySQL หรือ MariaDB

2. **อัปโหลดไฟล์**
   - แตกไฟล์ ZIP ของระบบ
   - อัปโหลดไฟล์ทั้งหมดไปยังโฟลเดอร์ root ของเว็บเซิร์ฟเวอร์ (เช่น /var/www/html/ สำหรับ Apache หรือ /usr/share/nginx/html/ สำหรับ Nginx)

3. **ตั้งค่าสิทธิ์ไฟล์**
   ```bash
   chmod -R 755 /path/to/sales_tracking_system
   chmod -R 777 /path/to/sales_tracking_system/uploads
   chmod -R 777 /path/to/sales_tracking_system/logs
   ```

4. **ตั้งค่า Virtual Host (Apache)**
   ```apache
   <VirtualHost *:80>
       ServerName yourdomain.com
       ServerAlias www.yourdomain.com
       DocumentRoot /path/to/sales_tracking_system
       
       <Directory /path/to/sales_tracking_system>
           Options Indexes FollowSymLinks MultiViews
           AllowOverride All
           Require all granted
       </Directory>
       
       ErrorLog ${APACHE_LOG_DIR}/sales_tracking_error.log
       CustomLog ${APACHE_LOG_DIR}/sales_tracking_access.log combined
   </VirtualHost>
   ```

5. **ตั้งค่า Server Block (Nginx)**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com www.yourdomain.com;
       root /path/to/sales_tracking_system;
       index index.php index.html;
       
       location / {
           try_files $uri $uri/ /index.php?$query_string;
       }
       
       location ~ \.php$ {
           include snippets/fastcgi-php.conf;
           fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
       }
       
       location ~ /\.ht {
           deny all;
       }
   }
   ```

6. **รีสตาร์ทเว็บเซิร์ฟเวอร์**
   - Apache: `sudo systemctl restart apache2`
   - Nginx: `sudo systemctl restart nginx`

### การติดตั้งบนเว็บโฮสติ้ง

1. **อัปโหลดไฟล์**
   - แตกไฟล์ ZIP ของระบบ
   - อัปโหลดไฟล์ทั้งหมดไปยังโฟลเดอร์ root ของเว็บโฮสติ้ง (เช่น public_html)

2. **ตั้งค่าสิทธิ์ไฟล์**
   - ใช้ File Manager ของเว็บโฮสติ้งเพื่อตั้งค่าสิทธิ์ไฟล์
   - ตั้งค่าโฟลเดอร์ uploads และ logs เป็น 777 (หรือตามที่เว็บโฮสติ้งแนะนำ)

## การตั้งค่าฐานข้อมูล

1. **สร้างฐานข้อมูล**
   - สร้างฐานข้อมูลใหม่ผ่าน phpMyAdmin หรือ MySQL Command Line
   ```sql
   CREATE DATABASE sales_tracking_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **สร้างผู้ใช้ฐานข้อมูล**
   ```sql
   CREATE USER 'sales_user'@'localhost' IDENTIFIED BY 'your_strong_password';
   GRANT ALL PRIVILEGES ON sales_tracking_db.* TO 'sales_user'@'localhost';
   FLUSH PRIVILEGES;
   ```

3. **นำเข้าโครงสร้างฐานข้อมูล**
   - นำเข้าไฟล์ SQL ที่อยู่ในโฟลเดอร์ database
   ```bash
   mysql -u sales_user -p sales_tracking_db < /path/to/sales_tracking_system/database/sales_tracking_db.sql
   ```
   - หรือนำเข้าผ่าน phpMyAdmin

## การตั้งค่าระบบ

1. **แก้ไขไฟล์การตั้งค่าฐานข้อมูล**
   - เปิดไฟล์ `app/config/database.php`
   - แก้ไขค่าต่อไปนี้:
     ```php
     define('DB_HOST', 'localhost');
     define('DB_NAME', 'sales_tracking_db');
     define('DB_USER', 'sales_user');
     define('DB_PASS', 'your_strong_password');
     ```

2. **แก้ไขไฟล์การตั้งค่าทั่วไป**
   - เปิดไฟล์ `app/config/config.php`
   - แก้ไขค่าต่อไปนี้:
     ```php
     define('BASE_URL', 'https://yourdomain.com/');
     define('SITE_NAME', 'ระบบติดตามงานขายและการเสนอราคา');
     define('TIMEZONE', 'Asia/Bangkok');
     define('CURRENCY', 'THB');
     define('CURRENCY_SYMBOL', '฿');
     define('DATE_FORMAT', 'd/m/Y');
     define('TIME_FORMAT', 'H:i');
     define('COMPANY_NAME', 'บริษัท ของคุณ จำกัด');
     define('COMPANY_ADDRESS', 'ที่อยู่บริษัท');
     define('COMPANY_PHONE', 'เบอร์โทรศัพท์');
     define('COMPANY_EMAIL', 'อีเมล');
     define('COMPANY_WEBSITE', 'เว็บไซต์');
     define('COMPANY_TAX_ID', 'เลขประจำตัวผู้เสียภาษี');
     ```

3. **เข้าสู่ระบบครั้งแรก**
   - เปิดเว็บเบราว์เซอร์และไปที่ URL ของระบบ
   - เข้าสู่ระบบด้วยบัญชีผู้ดูแลระบบเริ่มต้น:
     - ชื่อผู้ใช้: admin
     - รหัสผ่าน: admin123
   - **สำคัญ**: เปลี่ยนรหัสผ่านทันทีหลังจากเข้าสู่ระบบครั้งแรก

4. **ตั้งค่าระบบเพิ่มเติม**
   - ไปที่เมนู "ตั้งค่า" > "ตั้งค่าทั่วไป"
   - กำหนดค่าต่างๆ ตามต้องการ:
     - ข้อมูลบริษัท
     - การแจ้งเตือน
     - การส่งอีเมล
     - รูปแบบเอกสาร
     - ฯลฯ

## การอัปเดตระบบ

1. **สำรองข้อมูล**
   - สำรองฐานข้อมูลและไฟล์ทั้งหมดก่อนทำการอัปเดต

2. **อัปเดตไฟล์**
   - แตกไฟล์ ZIP ของเวอร์ชันใหม่
   - อัปโหลดไฟล์ทั้งหมดไปแทนที่ไฟล์เดิม (ยกเว้นไฟล์การตั้งค่า)
   - อย่าลบโฟลเดอร์ uploads เพื่อรักษาไฟล์ที่ผู้ใช้อัปโหลด

3. **อัปเดตฐานข้อมูล**
   - รันสคริปต์อัปเดตฐานข้อมูล (ถ้ามี)
   ```bash
   mysql -u sales_user -p sales_tracking_db < /path/to/sales_tracking_system/database/update_vX.X.X.sql
   ```

4. **ตรวจสอบการตั้งค่า**
   - ตรวจสอบไฟล์การตั้งค่าว่ามีการเปลี่ยนแปลงหรือเพิ่มเติมค่าใหม่หรือไม่
   - อัปเดตไฟล์การตั้งค่าตามความจำเป็น

5. **ล้างแคช**
   - ลบไฟล์แคชทั้งหมดในโฟลเดอร์ cache (ถ้ามี)

## การสำรองและกู้คืนข้อมูล

### การสำรองข้อมูล

1. **สำรองฐานข้อมูล**
   ```bash
   mysqldump -u sales_user -p sales_tracking_db > backup_$(date +%Y%m%d).sql
   ```

2. **สำรองไฟล์**
   ```bash
   tar -czvf sales_tracking_files_$(date +%Y%m%d).tar.gz /path/to/sales_tracking_system
   ```

3. **การสำรองอัตโนมัติ**
   - ตั้งค่า Cron Job เพื่อสำรองข้อมูลอัตโนมัติ
   ```bash
   # สำรองทุกวันเวลา 01:00 น.
   0 1 * * * mysqldump -u sales_user -p'your_password' sales_tracking_db > /path/to/backups/db_$(date +\%Y\%m\%d).sql
   ```

### การกู้คืนข้อมูล

1. **กู้คืนฐานข้อมูล**
   ```bash
   mysql -u sales_user -p sales_tracking_db < backup_YYYYMMDD.sql
   ```

2. **กู้คืนไฟล์**
   ```bash
   tar -xzvf sales_tracking_files_YYYYMMDD.tar.gz -C /path/to/restore
   ```

## การแก้ไขปัญหาที่พบบ่อย

### ปัญหาการเข้าสู่ระบบ

**ปัญหา**: ไม่สามารถเข้าสู่ระบบได้แม้จะใช้รหัสผ่านที่ถูกต้อง

**วิธีแก้ไข**:
1. ตรวจสอบว่า PHP Session กำลังทำงานอย่างถูกต้อง
2. ตรวจสอบสิทธิ์ของโฟลเดอร์ sessions (ถ้ามี)
3. ลองล้างคุกกี้และแคชของเบราว์เซอร์
4. ตรวจสอบการตั้งค่า session.cookie_domain ใน php.ini

### ปัญหาการเชื่อมต่อฐานข้อมูล

**ปัญหา**: ไม่สามารถเชื่อมต่อกับฐานข้อมูลได้

**วิธีแก้ไข**:
1. ตรวจสอบการตั้งค่าในไฟล์ `app/config/database.php`
2. ตรวจสอบว่า MySQL หรือ MariaDB กำลังทำงาน
3. ตรวจสอบสิทธิ์ของผู้ใช้ฐานข้อมูล
4. ตรวจสอบว่าสามารถเชื่อมต่อกับฐานข้อมูลจากภายนอกได้หรือไม่

### ปัญหาการอัปโหลดไฟล์

**ปัญหา**: ไม่สามารถอัปโหลดไฟล์ได้

**วิธีแก้ไข**:
1. ตรวจสอบสิทธิ์ของโฟลเดอร์ uploads (ควรเป็น 777 หรือ rwxrwxrwx)
2. ตรวจสอบการตั้งค่า upload_max_filesize และ post_max_size ใน php.ini
3. ตรวจสอบว่าโฟลเดอร์ uploads มีพื้นที่เพียงพอ
4. ตรวจสอบ error log ของเว็บเซิร์ฟเวอร์

### ปัญหาการแสดงผล

**ปัญหา**: หน้าเว็บแสดงผลไม่ถูกต้อง

**วิธีแก้ไข**:
1. ตรวจสอบว่าไฟล์ CSS และ JavaScript ถูกโหลดอย่างถูกต้อง
2. ตรวจสอบการตั้งค่า BASE_URL ในไฟล์ `app/config/config.php`
3. ลองล้างแคชของเบราว์เซอร์
4. ตรวจสอบ Console ใน Developer Tools ของเบราว์เซอร์เพื่อดูข้อผิดพลาด

### ปัญหาการส่งอีเมล

**ปัญหา**: ระบบไม่สามารถส่งอีเมลได้

**วิธีแก้ไข**:
1. ตรวจสอบการตั้งค่าอีเมลในระบบ
2. ตรวจสอบว่า SMTP Server ทำงานอย่างถูกต้อง
3. ลองใช้ SMTP Server อื่น เช่น Gmail SMTP
4. ตรวจสอบ Firewall ว่าอนุญาตการเชื่อมต่อกับ SMTP Server หรือไม่

## การติดต่อขอความช่วยเหลือ

หากคุณพบปัญหาในการติดตั้งหรือใช้งานระบบ สามารถติดต่อขอความช่วยเหลือได้ที่:

- อีเมล: <EMAIL>
- โทรศัพท์: 02-XXX-XXXX
- เว็บไซต์: https://www.example.com/support
- เวลาทำการ: จันทร์-ศุกร์ 9:00-17:00 น.

