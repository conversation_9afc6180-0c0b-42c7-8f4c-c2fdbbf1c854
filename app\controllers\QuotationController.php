<?php
/**
 * Quotation Controller
 */

class QuotationController extends BaseController {
    private $quotationModel;
    private $customerModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->quotationModel = new Quotation();
        $this->customerModel = new Customer();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $quotations = $this->quotationModel->getPaginated($page);
        $this->setPagination($quotations);
        
        $this->data['quotations'] = $quotations['items'];
        $this->data['page_title'] = 'รายการใบเสนอราคา';
        $this->view('quotations/index');
    }
    
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $quotation = $this->quotationModel->getById($id);
        if (!$quotation) {
            $this->show404();
            return;
        }
        
        $this->data['quotation'] = $quotation;
        $this->data['page_title'] = 'รายละเอียดใบเสนอราคา';
        $this->view('quotations/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'สร้างใบเสนอราคาใหม่';
        $this->view('quotations/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=quotation&action=create');
            return;
        }
        
        $data = $this->post();
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->quotationModel->create($data);
        
        if ($id) {
            showAlert('สร้างใบเสนอราคาเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=quotation&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=quotation&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $quotation = $this->quotationModel->getById($id);
        if (!$quotation) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['quotation'] = $quotation;
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขใบเสนอราคา';
        $this->view('quotations/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=quotation&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->quotationModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขใบเสนอราคาเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=quotation&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=quotation&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->quotationModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
