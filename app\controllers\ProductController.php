<?php
/**
 * Product Controller
 */

class ProductController extends BaseController {
    private $productModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->productModel = new Product();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $products = $this->productModel->getPaginated($page);
        $this->setPagination($products);
        
        $this->data['products'] = $products['items'];
        $this->data['page_title'] = 'รายการสินค้า/บริการ';
        $this->view('products/index');
    }
    
    public function view($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $product = $this->productModel->getById($id);
        if (!$product) {
            $this->show404();
            return;
        }
        
        $this->data['product'] = $product;
        $this->data['page_title'] = 'รายละเอียดสินค้า/บริการ';
        $this->view('products/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'เพิ่มสินค้า/บริการใหม่';
        $this->view('products/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=product&action=create');
            return;
        }
        
        $data = $this->post();
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->productModel->create($data);
        
        if ($id) {
            showAlert('เพิ่มสินค้า/บริการเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=product&action=view&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=product&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $product = $this->productModel->getById($id);
        if (!$product) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $this->data['product'] = $product;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขสินค้า/บริการ';
        $this->view('products/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=product&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->productModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขสินค้า/บริการเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=product&action=view&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=product&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->productModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
