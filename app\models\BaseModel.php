<?php
/**
 * Base Model Class
 * 
 * คลาสพื้นฐานสำหรับโมเดลทั้งหมด
 */

abstract class BaseModel {
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    
    /**
     * คอนสตรักเตอร์
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * ดึงข้อมูลทั้งหมด
     * 
     * @param string $orderBy ฟิลด์ที่ใช้เรียงลำดับ
     * @param string $order ลำดับการเรียง (ASC หรือ DESC)
     * @return array
     */
    public function getAll($orderBy = null, $order = 'ASC') {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table;
        
        if ($orderBy) {
            $sql .= " ORDER BY " . $orderBy . " " . $order;
        }
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลแบบแบ่งหน้า
     * 
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @param string $orderBy ฟิลด์ที่ใช้เรียงลำดับ
     * @param string $order ลำดับการเรียง (ASC หรือ DESC)
     * @return array
     */
    public function getPaginated($page = 1, $perPage = ITEMS_PER_PAGE, $orderBy = null, $order = 'ASC') {
        $offset = ($page - 1) * $perPage;
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total FROM " . TABLE_PREFIX . $this->table;
        $this->db->prepare($countSql);
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table;
        
        if ($orderBy) {
            $sql .= " ORDER BY " . $orderBy . " " . $order;
        }
        
        $sql .= " LIMIT :offset, :perPage";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':offset' => $offset,
            ':perPage' => $perPage
        ]);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
    
    /**
     * ดึงข้อมูลตาม ID
     * 
     * @param int $id ID ของรายการ
     * @return array|bool
     */
    public function getById($id) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE " . $this->primaryKey . " = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลตามเงื่อนไข
     * 
     * @param array $conditions เงื่อนไขในการค้นหา
     * @param string $operator ตัวดำเนินการ (AND หรือ OR)
     * @return array
     */
    public function getWhere($conditions, $operator = 'AND') {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE ";
        
        $params = [];
        $whereClause = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = $field . " = :" . $field;
            $params[':' . $field] = $value;
        }
        
        $sql .= implode(" " . $operator . " ", $whereClause);
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลแถวเดียวตามเงื่อนไข
     * 
     * @param array $conditions เงื่อนไขในการค้นหา
     * @param string $operator ตัวดำเนินการ (AND หรือ OR)
     * @return array|bool
     */
    public function getOneWhere($conditions, $operator = 'AND') {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE ";
        
        $params = [];
        $whereClause = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = $field . " = :" . $field;
            $params[':' . $field] = $value;
        }
        
        $sql .= implode(" " . $operator . " ", $whereClause);
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * สร้างรายการใหม่
     * 
     * @param array $data ข้อมูลที่ต้องการเพิ่ม
     * @return int|bool
     */
    public function create($data) {
        // กรองข้อมูลตาม fillable fields
        $filteredData = array_intersect_key($data, array_flip($this->fillable));
        
        if (empty($filteredData)) {
            return false;
        }
        
        $fields = array_keys($filteredData);
        $placeholders = array_map(function($field) {
            return ':' . $field;
        }, $fields);
        
        $sql = "INSERT INTO " . TABLE_PREFIX . $this->table . " (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $params = [];
        foreach ($filteredData as $field => $value) {
            $params[':' . $field] = $value;
        }
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $result = $this->db->execute();
        
        if ($result) {
            return $this->db->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * อัปเดตรายการ
     * 
     * @param int $id ID ของรายการ
     * @param array $data ข้อมูลที่ต้องการอัปเดต
     * @return bool
     */
    public function update($id, $data) {
        // กรองข้อมูลตาม fillable fields
        $filteredData = array_intersect_key($data, array_flip($this->fillable));
        
        if (empty($filteredData)) {
            return false;
        }
        
        $setClause = [];
        foreach ($filteredData as $field => $value) {
            $setClause[] = $field . " = :" . $field;
        }
        
        $sql = "UPDATE " . TABLE_PREFIX . $this->table . " SET " . implode(', ', $setClause) . " WHERE " . $this->primaryKey . " = :id";
        
        $params = [':id' => $id];
        foreach ($filteredData as $field => $value) {
            $params[':' . $field] = $value;
        }
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        
        return $this->db->execute();
    }
    
    /**
     * ลบรายการ
     * 
     * @param int $id ID ของรายการ
     * @return bool
     */
    public function delete($id) {
        $sql = "DELETE FROM " . TABLE_PREFIX . $this->table . " WHERE " . $this->primaryKey . " = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        
        return $this->db->execute();
    }
    
    /**
     * ตรวจสอบว่ามีรายการอยู่หรือไม่
     * 
     * @param array $conditions เงื่อนไขในการค้นหา
     * @param string $operator ตัวดำเนินการ (AND หรือ OR)
     * @return bool
     */
    public function exists($conditions, $operator = 'AND') {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " WHERE ";
        
        $params = [];
        $whereClause = [];
        
        foreach ($conditions as $field => $value) {
            $whereClause[] = $field . " = :" . $field;
            $params[':' . $field] = $value;
        }
        
        $sql .= implode(" " . $operator . " ", $whereClause);
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * นับจำนวนรายการ
     * 
     * @param array $conditions เงื่อนไขในการค้นหา (ถ้ามี)
     * @param string $operator ตัวดำเนินการ (AND หรือ OR)
     * @return int
     */
    public function count($conditions = [], $operator = 'AND') {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table;
        
        $params = [];
        
        if (!empty($conditions)) {
            $sql .= " WHERE ";
            
            $whereClause = [];
            
            foreach ($conditions as $field => $value) {
                $whereClause[] = $field . " = :" . $field;
                $params[':' . $field] = $value;
            }
            
            $sql .= implode(" " . $operator . " ", $whereClause);
        }
        
        $this->db->prepare($sql);
        
        if (!empty($params)) {
            $this->db->bind($params);
        }
        
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['count'];
    }
    
    /**
     * ค้นหาข้อมูล
     * 
     * @param string $keyword คำค้นหา
     * @param array $fields ฟิลด์ที่ต้องการค้นหา
     * @return array
     */
    public function search($keyword, $fields) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE ";
        
        $whereClause = [];
        $params = [];
        
        foreach ($fields as $field) {
            $whereClause[] = $field . " LIKE :keyword";
        }
        
        $sql .= "(" . implode(" OR ", $whereClause) . ")";
        $params[':keyword'] = '%' . $keyword . '%';
        
        $this->db->prepare($sql);
        $this->db->bind($params);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ค้นหาข้อมูลแบบแบ่งหน้า
     * 
     * @param string $keyword คำค้นหา
     * @param array $fields ฟิลด์ที่ต้องการค้นหา
     * @param int $page หน้าปัจจุบัน
     * @param int $perPage จำนวนรายการต่อหน้า
     * @return array
     */
    public function searchPaginated($keyword, $fields, $page = 1, $perPage = ITEMS_PER_PAGE) {
        $offset = ($page - 1) * $perPage;
        
        // สร้าง WHERE clause สำหรับการค้นหา
        $whereClause = [];
        foreach ($fields as $field) {
            $whereClause[] = $field . " LIKE :keyword";
        }
        $whereStr = "(" . implode(" OR ", $whereClause) . ")";
        
        // คำนวณจำนวนรายการทั้งหมด
        $countSql = "SELECT COUNT(*) as total FROM " . TABLE_PREFIX . $this->table . " WHERE " . $whereStr;
        $this->db->prepare($countSql);
        $this->db->bind([':keyword' => '%' . $keyword . '%']);
        $this->db->execute();
        $totalItems = $this->db->fetch()['total'];
        
        // ดึงข้อมูลตามหน้า
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE " . $whereStr . " LIMIT :offset, :perPage";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':keyword' => '%' . $keyword . '%',
            ':offset' => $offset,
            ':perPage' => $perPage
        ]);
        $this->db->execute();
        
        $items = $this->db->fetchAll();
        
        // คำนวณจำนวนหน้าทั้งหมด
        $totalPages = ceil($totalItems / $perPage);
        
        return [
            'items' => $items,
            'total_items' => $totalItems,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'per_page' => $perPage
        ];
    }
}

