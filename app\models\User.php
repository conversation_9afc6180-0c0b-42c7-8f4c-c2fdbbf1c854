<?php
/**
 * User Model
 * 
 * คลาสสำหรับจัดการข้อมูลผู้ใช้งาน
 */

class User extends BaseModel {
    protected $table = 'users';
    protected $fillable = [
        'username', 'password', 'email', 'first_name', 'last_name', 
        'department_id', 'role_id', 'status', 'created_at', 'updated_at'
    ];
    
    /**
     * คอนสตรักเตอร์
     */
    public function __construct() {
        parent::__construct();
    }
    
    /**
     * ตรวจสอบการเข้าสู่ระบบ
     * 
     * @param string $username ชื่อผู้ใช้
     * @param string $password รหัสผ่าน
     * @return array|bool
     */
    public function login($username, $password) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE username = :username OR email = :email";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':username' => $username,
            ':email' => $username
        ]);
        $this->db->execute();
        
        $user = $this->db->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // อัปเดตเวลาเข้าสู่ระบบล่าสุด
            $this->updateLastLogin($user['id']);
            
            // ลบรหัสผ่านออกจากข้อมูลที่ส่งกลับ
            unset($user['password']);
            
            return $user;
        }
        
        return false;
    }
    
    /**
     * อัปเดตเวลาเข้าสู่ระบบล่าสุด
     * 
     * @param int $userId รหัสผู้ใช้
     * @return bool
     */
    private function updateLastLogin($userId) {
        $sql = "UPDATE " . TABLE_PREFIX . $this->table . " SET last_login = NOW() WHERE id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $userId]);
        
        return $this->db->execute();
    }
    
    /**
     * สร้างผู้ใช้ใหม่
     * 
     * @param array $data ข้อมูลผู้ใช้
     * @return int|bool
     */
    public function create($data) {
        // เข้ารหัสรหัสผ่าน
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_HASH_ALGO, ['cost' => PASSWORD_HASH_COST]);
        }
        
        // เพิ่มวันที่สร้างและอัปเดต
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return parent::create($data);
    }
    
    /**
     * อัปเดตผู้ใช้
     * 
     * @param int $id รหัสผู้ใช้
     * @param array $data ข้อมูลที่ต้องการอัปเดต
     * @return bool
     */
    public function update($id, $data) {
        // เข้ารหัสรหัสผ่านถ้ามีการอัปเดต
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_HASH_ALGO, ['cost' => PASSWORD_HASH_COST]);
        } else {
            // ถ้าไม่ได้อัปเดตรหัสผ่าน ให้ลบออกจากข้อมูลที่จะอัปเดต
            unset($data['password']);
        }
        
        // อัปเดตวันที่แก้ไข
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return parent::update($id, $data);
    }
    
    /**
     * เปลี่ยนรหัสผ่าน
     * 
     * @param int $id รหัสผู้ใช้
     * @param string $newPassword รหัสผ่านใหม่
     * @return bool
     */
    public function changePassword($id, $newPassword) {
        $hashedPassword = password_hash($newPassword, PASSWORD_HASH_ALGO, ['cost' => PASSWORD_HASH_COST]);
        
        $sql = "UPDATE " . TABLE_PREFIX . $this->table . " SET password = :password, updated_at = NOW() WHERE id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([
            ':password' => $hashedPassword,
            ':id' => $id
        ]);
        
        return $this->db->execute();
    }
    
    /**
     * ตรวจสอบรหัสผ่านปัจจุบัน
     * 
     * @param int $id รหัสผู้ใช้
     * @param string $password รหัสผ่านที่ต้องการตรวจสอบ
     * @return bool
     */
    public function verifyPassword($id, $password) {
        $sql = "SELECT password FROM " . TABLE_PREFIX . $this->table . " WHERE id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        $this->db->execute();
        
        $user = $this->db->fetch();
        
        if ($user) {
            return password_verify($password, $user['password']);
        }
        
        return false;
    }
    
    /**
     * ดึงข้อมูลผู้ใช้พร้อมข้อมูลบทบาทและแผนก
     * 
     * @param int $id รหัสผู้ใช้
     * @return array|bool
     */
    public function getUserWithDetails($id) {
        $sql = "SELECT u.*, r.name as role_name, d.name as department_name 
                FROM " . TABLE_PREFIX . $this->table . " u 
                LEFT JOIN " . TABLE_PREFIX . "roles r ON u.role_id = r.id 
                LEFT JOIN " . TABLE_PREFIX . "departments d ON u.department_id = d.id 
                WHERE u.id = :id";
        
        $this->db->prepare($sql);
        $this->db->bind([':id' => $id]);
        $this->db->execute();
        
        return $this->db->fetch();
    }
    
    /**
     * ดึงข้อมูลผู้ใช้ทั้งหมดพร้อมข้อมูลบทบาทและแผนก
     * 
     * @return array
     */
    public function getAllWithDetails() {
        $sql = "SELECT u.*, r.name as role_name, d.name as department_name 
                FROM " . TABLE_PREFIX . $this->table . " u 
                LEFT JOIN " . TABLE_PREFIX . "roles r ON u.role_id = r.id 
                LEFT JOIN " . TABLE_PREFIX . "departments d ON u.department_id = d.id 
                ORDER BY u.id";
        
        $this->db->prepare($sql);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลผู้ใช้ตามบทบาท
     * 
     * @param int $roleId รหัสบทบาท
     * @return array
     */
    public function getUsersByRole($roleId) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE role_id = :role_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':role_id' => $roleId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ดึงข้อมูลผู้ใช้ตามแผนก
     * 
     * @param int $departmentId รหัสแผนก
     * @return array
     */
    public function getUsersByDepartment($departmentId) {
        $sql = "SELECT * FROM " . TABLE_PREFIX . $this->table . " WHERE department_id = :department_id";
        
        $this->db->prepare($sql);
        $this->db->bind([':department_id' => $departmentId]);
        $this->db->execute();
        
        return $this->db->fetchAll();
    }
    
    /**
     * ตรวจสอบว่าชื่อผู้ใช้มีอยู่แล้วหรือไม่
     * 
     * @param string $username ชื่อผู้ใช้
     * @param int $excludeId รหัสผู้ใช้ที่ต้องการยกเว้น (สำหรับการอัปเดต)
     * @return bool
     */
    public function isUsernameExists($username, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " WHERE username = :username";
        
        if ($excludeId) {
            $sql .= " AND id != :id";
        }
        
        $this->db->prepare($sql);
        $params = [':username' => $username];
        
        if ($excludeId) {
            $params[':id'] = $excludeId;
        }
        
        $this->db->bind($params);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * ตรวจสอบว่าอีเมลมีอยู่แล้วหรือไม่
     * 
     * @param string $email อีเมล
     * @param int $excludeId รหัสผู้ใช้ที่ต้องการยกเว้น (สำหรับการอัปเดต)
     * @return bool
     */
    public function isEmailExists($email, $excludeId = null) {
        $sql = "SELECT COUNT(*) as count FROM " . TABLE_PREFIX . $this->table . " WHERE email = :email";
        
        if ($excludeId) {
            $sql .= " AND id != :id";
        }
        
        $this->db->prepare($sql);
        $params = [':email' => $email];
        
        if ($excludeId) {
            $params[':id'] = $excludeId;
        }
        
        $this->db->bind($params);
        $this->db->execute();
        
        $result = $this->db->fetch();
        
        return $result['count'] > 0;
    }
}

