<?php
/**
 * Opportunity Controller
 * 
 * คอนโทรลเลอร์สำหรับจัดการข้อมูลโอกาสการขาย
 */

class OpportunityController extends BaseController {
    private $opportunityModel;
    private $customerModel;
    private $contactModel;
    
    public function __construct() {
        parent::__construct();
        
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        
        $this->opportunityModel = new Opportunity();
        $this->customerModel = new Customer();
        $this->contactModel = new Contact();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $opportunities = $this->opportunityModel->getPaginated($page);
        $this->setPagination($opportunities);
        
        $this->data['opportunities'] = $opportunities['items'];
        $this->data['page_title'] = 'รายการโอกาสการขาย';
        
        $this->view('opportunities/index');
    }
    
    public function show($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $opportunity = $this->opportunityModel->getById($id);
        if (!$opportunity) {
            $this->show404();
            return;
        }
        
        $this->data['opportunity'] = $opportunity;
        $this->data['page_title'] = 'รายละเอียดโอกาสการขาย';
        
        $this->view('opportunities/view');
    }
    
    public function create() {
        if ($this->isMethod('POST')) {
            $this->processCreate();
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'เพิ่มโอกาสการขายใหม่';
        
        $this->view('opportunities/create');
    }
    
    private function processCreate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=opportunity&action=create');
            return;
        }
        
        $data = $this->post();
        $data['created_by'] = $this->user['id'];
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $id = $this->opportunityModel->create($data);
        
        if ($id) {
            showAlert('เพิ่มโอกาสการขายเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=opportunity&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=opportunity&action=create');
        }
    }
    
    public function edit($id) {
        if (!$id) {
            $this->show404();
            return;
        }
        
        $opportunity = $this->opportunityModel->getById($id);
        if (!$opportunity) {
            $this->show404();
            return;
        }
        
        if ($this->isMethod('POST')) {
            $this->processEdit($id);
            return;
        }
        
        $customers = $this->customerModel->getForDropdown();
        
        $this->data['opportunity'] = $opportunity;
        $this->data['customers'] = $customers;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'แก้ไขโอกาสการขาย';
        
        $this->view('opportunities/edit');
    }
    
    private function processEdit($id) {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=opportunity&action=edit&id=' . $id);
            return;
        }
        
        $data = $this->post();
        $data['updated_by'] = $this->user['id'];
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $success = $this->opportunityModel->update($id, $data);
        
        if ($success) {
            showAlert('แก้ไขโอกาสการขายเรียบร้อยแล้ว', 'success');
            $this->redirect('index.php?controller=opportunity&action=show&id=' . $id);
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
            $this->redirect('index.php?controller=opportunity&action=edit&id=' . $id);
        }
    }
    
    public function delete($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->opportunityModel->delete($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'ลบเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
}
