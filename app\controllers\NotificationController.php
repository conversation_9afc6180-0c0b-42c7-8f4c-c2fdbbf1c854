<?php
/**
 * Notification Controller
 */

class NotificationController extends BaseController {
    private $notificationModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->notificationModel = new Notification();
    }
    
    public function index() {
        $page = $this->get('page', 1);
        $type = $this->get('type', '');
        $unreadOnly = $this->get('unread_only', false);
        
        $notifications = $this->notificationModel->getUserNotifications(
            $this->user['id'], 
            $unreadOnly, 
            ITEMS_PER_PAGE
        );
        
        $this->data['notifications'] = $notifications;
        $this->data['type'] = $type;
        $this->data['unread_only'] = $unreadOnly;
        $this->data['page_title'] = 'การแจ้งเตือน';
        $this->view('notifications/index');
    }
    
    public function markAsRead($id) {
        if (!$id || !$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->notificationModel->markAsRead($id);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'อัปเดตสถานะเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
    
    public function markAllAsRead() {
        if (!$this->isMethod('POST') || !$this->validateCSRF()) {
            $this->json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        $success = $this->notificationModel->markAllAsRead($this->user['id']);
        
        if ($success) {
            $this->json(['success' => true, 'message' => 'อัปเดตสถานะทั้งหมดเรียบร้อยแล้ว']);
        } else {
            $this->json(['success' => false, 'message' => 'เกิดข้อผิดพลาด'], 500);
        }
    }
    
    public function getUnreadCount() {
        if (!$this->isAjax()) {
            $this->show404();
            return;
        }
        
        $count = $this->notificationModel->getUnreadCount($this->user['id']);
        $this->json(['count' => $count]);
    }
    
    public function getRecent() {
        if (!$this->isAjax()) {
            $this->show404();
            return;
        }
        
        $limit = $this->get('limit', 5);
        $notifications = $this->notificationModel->getUserNotifications(
            $this->user['id'], 
            false, 
            $limit
        );
        
        $this->json(['notifications' => $notifications]);
    }
}
