-- ฐานข้อมูลสำหรับระบบติดตามงานขายและการเสนอราคา

-- สร้างฐานข้อมูล
CREATE DATABASE IF NOT EXISTS `sales_tracking_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `sales_tracking_db`;

-- ตาราง roles (บทบาทผู้ใช้งาน)
CREATE TABLE IF NOT EXISTS `sts_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `permissions` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง departments (แผนก)
CREATE TABLE IF NOT EXISTS `sts_departments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง users (ผู้ใช้งาน)
CREATE TABLE IF NOT EXISTS `sts_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `department_id` (`department_id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `sts_users_ibfk_1` FOREIGN KEY (`department_id`) REFERENCES `sts_departments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_users_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `sts_roles` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง customers (ลูกค้า)
CREATE TABLE IF NOT EXISTS `sts_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `address` text DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `website` varchar(100) DEFAULT NULL,
  `industry` varchar(50) DEFAULT NULL,
  `size` enum('small','medium','large') DEFAULT NULL,
  `type` enum('prospect','customer') NOT NULL DEFAULT 'prospect',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `sts_customers_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง contacts (ผู้ติดต่อ)
CREATE TABLE IF NOT EXISTS `sts_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `position` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `sts_contacts_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `sts_customers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง opportunities (โอกาสการขาย)
CREATE TABLE IF NOT EXISTS `sts_opportunities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `contact_id` int(11) DEFAULT NULL,
  `expected_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `probability` int(3) NOT NULL DEFAULT 0,
  `source` varchar(50) DEFAULT NULL,
  `stage` enum('lead','qualification','proposal','negotiation','closed_won','closed_lost') NOT NULL DEFAULT 'lead',
  `expected_close_date` date DEFAULT NULL,
  `actual_close_date` date DEFAULT NULL,
  `status` enum('open','won','lost') NOT NULL DEFAULT 'open',
  `notes` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `contact_id` (`contact_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sts_opportunities_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `sts_customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_opportunities_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `sts_contacts` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_opportunities_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง activities (กิจกรรม)
CREATE TABLE IF NOT EXISTS `sts_activities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('call','meeting','email','note') NOT NULL,
  `subject` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `activity_date` datetime NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `status` enum('planned','completed','cancelled') NOT NULL DEFAULT 'planned',
  `customer_id` int(11) DEFAULT NULL,
  `contact_id` int(11) DEFAULT NULL,
  `opportunity_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `contact_id` (`contact_id`),
  KEY `opportunity_id` (`opportunity_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sts_activities_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `sts_customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_activities_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `sts_contacts` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_activities_ibfk_3` FOREIGN KEY (`opportunity_id`) REFERENCES `sts_opportunities` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_activities_ibfk_4` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง products (สินค้า/บริการ)
CREATE TABLE IF NOT EXISTS `sts_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `sku` varchar(50) DEFAULT NULL,
  `unit` varchar(20) DEFAULT NULL,
  `price` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku` (`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง quotations (ใบเสนอราคา)
CREATE TABLE IF NOT EXISTS `sts_quotations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quotation_number` varchar(20) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `contact_id` int(11) DEFAULT NULL,
  `opportunity_id` int(11) DEFAULT NULL,
  `subject` varchar(100) NOT NULL,
  `issue_date` date NOT NULL,
  `valid_until` date NOT NULL,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `discount_type` enum('percentage','fixed') DEFAULT NULL,
  `discount_value` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `terms` text DEFAULT NULL,
  `status` enum('draft','sent','accepted','rejected','expired') NOT NULL DEFAULT 'draft',
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `quotation_number` (`quotation_number`),
  KEY `customer_id` (`customer_id`),
  KEY `contact_id` (`contact_id`),
  KEY `opportunity_id` (`opportunity_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sts_quotations_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `sts_customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_quotations_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `sts_contacts` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_quotations_ibfk_3` FOREIGN KEY (`opportunity_id`) REFERENCES `sts_opportunities` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_quotations_ibfk_4` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง quotation_items (รายการในใบเสนอราคา)
CREATE TABLE IF NOT EXISTS `sts_quotation_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quotation_id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `description` text NOT NULL,
  `quantity` decimal(10,2) NOT NULL DEFAULT 1.00,
  `unit_price` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`id`),
  KEY `quotation_id` (`quotation_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `sts_quotation_items_ibfk_1` FOREIGN KEY (`quotation_id`) REFERENCES `sts_quotations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_quotation_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `sts_products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง invoices (ใบแจ้งหนี้)
CREATE TABLE IF NOT EXISTS `sts_invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(20) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `contact_id` int(11) DEFAULT NULL,
  `quotation_id` int(11) DEFAULT NULL,
  `subject` varchar(100) NOT NULL,
  `issue_date` date NOT NULL,
  `due_date` date NOT NULL,
  `subtotal` decimal(15,2) NOT NULL DEFAULT 0.00,
  `discount_type` enum('percentage','fixed') DEFAULT NULL,
  `discount_value` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax` decimal(15,2) NOT NULL DEFAULT 0.00,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  `amount_paid` decimal(15,2) NOT NULL DEFAULT 0.00,
  `notes` text DEFAULT NULL,
  `terms` text DEFAULT NULL,
  `status` enum('pending','partial','paid','overdue','cancelled') NOT NULL DEFAULT 'pending',
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_number` (`invoice_number`),
  KEY `customer_id` (`customer_id`),
  KEY `contact_id` (`contact_id`),
  KEY `quotation_id` (`quotation_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sts_invoices_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `sts_customers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_invoices_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `sts_contacts` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_invoices_ibfk_3` FOREIGN KEY (`quotation_id`) REFERENCES `sts_quotations` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_invoices_ibfk_4` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง invoice_items (รายการในใบแจ้งหนี้)
CREATE TABLE IF NOT EXISTS `sts_invoice_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `product_id` int(11) DEFAULT NULL,
  `description` text NOT NULL,
  `quantity` decimal(10,2) NOT NULL DEFAULT 1.00,
  `unit_price` decimal(15,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `total` decimal(15,2) NOT NULL DEFAULT 0.00,
  PRIMARY KEY (`id`),
  KEY `invoice_id` (`invoice_id`),
  KEY `product_id` (`product_id`),
  CONSTRAINT `sts_invoice_items_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `sts_invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_invoice_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `sts_products` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง payments (การชำระเงิน)
CREATE TABLE IF NOT EXISTS `sts_payments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL,
  `payment_number` varchar(20) NOT NULL,
  `amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `payment_date` date NOT NULL,
  `payment_method` enum('cash','bank_transfer','credit_card','cheque') NOT NULL,
  `reference` varchar(100) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payment_number` (`payment_number`),
  KEY `invoice_id` (`invoice_id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sts_payments_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `sts_invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `sts_payments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง sales_targets (เป้าหมายการขาย)
CREATE TABLE IF NOT EXISTS `sts_sales_targets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `target_type` enum('revenue','deals','customers') NOT NULL,
  `target_value` decimal(15,2) NOT NULL DEFAULT 0.00,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `department_id` (`department_id`),
  CONSTRAINT `sts_sales_targets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `sts_sales_targets_ibfk_2` FOREIGN KEY (`department_id`) REFERENCES `sts_departments` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง notifications (การแจ้งเตือน)
CREATE TABLE IF NOT EXISTS `sts_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `sts_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `sts_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ตาราง settings (การตั้งค่าระบบ)
CREATE TABLE IF NOT EXISTS `sts_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_group` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ข้อมูลเริ่มต้น

-- ข้อมูลบทบาท
INSERT INTO `sts_roles` (`id`, `name`, `description`, `permissions`, `created_at`, `updated_at`) VALUES
(1, 'Administrator', 'ผู้ดูแลระบบ มีสิทธิ์เข้าถึงทุกฟังก์ชันในระบบ', '{"all": true}', NOW(), NOW()),
(2, 'Manager', 'ผู้จัดการ มีสิทธิ์เข้าถึงรายงานและจัดการข้อมูลทั่วไป', '{"dashboard": true, "customers": true, "opportunities": true, "quotations": true, "invoices": true, "reports": true, "settings": false}', NOW(), NOW()),
(3, 'Sales Representative', 'พนักงานขาย มีสิทธิ์จัดการลูกค้า โอกาสการขาย และใบเสนอราคา', '{"dashboard": true, "customers": true, "opportunities": true, "quotations": true, "invoices": true, "reports": false, "settings": false}', NOW(), NOW()),
(4, 'User', 'ผู้ใช้งานทั่วไป มีสิทธิ์เข้าถึงข้อมูลพื้นฐาน', '{"dashboard": true, "customers": {"view": true, "create": false, "edit": false, "delete": false}, "opportunities": {"view": true, "create": false, "edit": false, "delete": false}, "quotations": {"view": true, "create": false, "edit": false, "delete": false}, "invoices": {"view": true, "create": false, "edit": false, "delete": false}, "reports": false, "settings": false}', NOW(), NOW());

-- ข้อมูลแผนก
INSERT INTO `sts_departments` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, 'ฝ่ายบริหาร', 'ฝ่ายบริหารองค์กร', NOW(), NOW()),
(2, 'ฝ่ายขาย', 'ฝ่ายขายและการตลาด', NOW(), NOW()),
(3, 'ฝ่ายบัญชี', 'ฝ่ายบัญชีและการเงิน', NOW(), NOW()),
(4, 'ฝ่ายไอที', 'ฝ่ายเทคโนโลยีสารสนเทศ', NOW(), NOW());

-- ข้อมูลผู้ใช้งาน
INSERT INTO `sts_users` (`id`, `username`, `password`, `email`, `first_name`, `last_name`, `phone`, `profile_image`, `department_id`, `role_id`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$12$1234567890123456789012uQQTqQzWUZM0OcDsyRZUP4jDpKLMcZu', '<EMAIL>', 'ผู้ดูแล', 'ระบบ', '0812345678', NULL, 1, 1, 'active', NOW(), NOW(), NOW()),
(2, 'manager', '$2y$12$1234567890123456789012uQQTqQzWUZM0OcDsyRZUP4jDpKLMcZu', '<EMAIL>', 'ผู้จัดการ', 'ฝ่ายขาย', '**********', NULL, 2, 2, 'active', NOW(), NOW(), NOW()),
(3, 'sales1', '$2y$12$1234567890123456789012uQQTqQzWUZM0OcDsyRZUP4jDpKLMcZu', '<EMAIL>', 'พนักงานขาย', 'คนที่ 1', '**********', NULL, 2, 3, 'active', NOW(), NOW(), NOW()),
(4, 'sales2', '$2y$12$1234567890123456789012uQQTqQzWUZM0OcDsyRZUP4jDpKLMcZu', '<EMAIL>', 'พนักงานขาย', 'คนที่ 2', '**********', NULL, 2, 3, 'active', NOW(), NOW(), NOW()),
(5, 'account', '$2y$12$1234567890123456789012uQQTqQzWUZM0OcDsyRZUP4jDpKLMcZu', '<EMAIL>', 'พนักงาน', 'บัญชี', '**********', NULL, 3, 4, 'active', NOW(), NOW(), NOW());

-- ข้อมูลลูกค้า
INSERT INTO `sts_customers` (`id`, `name`, `address`, `phone`, `email`, `website`, `industry`, `size`, `type`, `status`, `notes`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'บริษัท เอบีซี จำกัด', '123 ถนนสุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110', '02-123-4567', '<EMAIL>', 'www.abc.co.th', 'Manufacturing', 'large', 'customer', 'active', 'ลูกค้าหลัก มีการสั่งซื้อสินค้าเป็นประจำ', 1, NOW(), NOW()),
(2, 'บริษัท เอ็กซ์วายแซด จำกัด', '456 ถนนสีลม แขวงสีลม เขตบางรัก กรุงเทพฯ 10500', '02-234-5678', '<EMAIL>', 'www.xyz.co.th', 'Technology', 'medium', 'customer', 'active', 'ลูกค้าใหม่ เพิ่งเริ่มทำธุรกิจด้วย', 3, NOW(), NOW()),
(3, 'บริษัท วันทูทรี จำกัด', '789 ถนนพระราม 9 แขวงห้วยขวาง เขตห้วยขวาง กรุงเทพฯ 10310', '02-345-6789', '<EMAIL>', 'www.123.co.th', 'Retail', 'small', 'prospect', 'active', 'ลูกค้าที่มีศักยภาพ กำลังอยู่ในขั้นตอนการเจรจา', 4, NOW(), NOW());

-- ข้อมูลผู้ติดต่อ
INSERT INTO `sts_contacts` (`id`, `customer_id`, `first_name`, `last_name`, `position`, `email`, `phone`, `mobile`, `is_primary`, `notes`, `created_at`, `updated_at`) VALUES
(1, 1, 'สมชาย', 'ใจดี', 'ผู้จัดการฝ่ายจัดซื้อ', '<EMAIL>', '02-123-4567 ต่อ 101', '************', 1, 'ติดต่อได้ในวันจันทร์-ศุกร์', NOW(), NOW()),
(2, 1, 'สมหญิง', 'รักดี', 'ผู้จัดการฝ่ายบัญชี', '<EMAIL>', '02-123-4567 ต่อ 102', '************', 0, 'ติดต่อเรื่องการชำระเงิน', NOW(), NOW()),
(3, 2, 'มานะ', 'มานี', 'กรรมการผู้จัดการ', '<EMAIL>', '02-234-5678 ต่อ 100', '************', 1, 'ติดต่อได้ตลอดเวลา', NOW(), NOW()),
(4, 3, 'วิชัย', 'ชัยวิชิต', 'ผู้จัดการทั่วไป', '<EMAIL>', '02-345-6789 ต่อ 100', '************', 1, 'ติดต่อได้ในวันจันทร์-ศุกร์', NOW(), NOW());

-- ข้อมูลสินค้า/บริการ
INSERT INTO `sts_products` (`id`, `name`, `description`, `sku`, `unit`, `price`, `tax_rate`, `status`, `created_at`, `updated_at`) VALUES
(1, 'คอมพิวเตอร์โน้ตบุ๊ก รุ่น A', 'คอมพิวเตอร์โน้ตบุ๊กสำหรับใช้งานทั่วไป', 'NB-001', 'เครื่อง', 25000.00, 7.00, 'active', NOW(), NOW()),
(2, 'คอมพิวเตอร์ตั้งโต๊ะ รุ่น B', 'คอมพิวเตอร์ตั้งโต๊ะสำหรับสำนักงาน', 'PC-001', 'เครื่อง', 20000.00, 7.00, 'active', NOW(), NOW()),
(3, 'เครื่องพิมพ์ รุ่น C', 'เครื่องพิมพ์เลเซอร์สี', 'PR-001', 'เครื่อง', 15000.00, 7.00, 'active', NOW(), NOW()),
(4, 'บริการดูแลระบบ', 'บริการดูแลระบบคอมพิวเตอร์รายเดือน', 'SV-001', 'เดือน', 5000.00, 7.00, 'active', NOW(), NOW()),
(5, 'บริการติดตั้งระบบเครือข่าย', 'บริการติดตั้งระบบเครือข่ายภายในองค์กร', 'SV-002', 'งาน', 50000.00, 7.00, 'active', NOW(), NOW());

-- ข้อมูลโอกาสการขาย
INSERT INTO `sts_opportunities` (`id`, `name`, `customer_id`, `contact_id`, `expected_amount`, `probability`, `source`, `stage`, `expected_close_date`, `actual_close_date`, `status`, `notes`, `user_id`, `created_at`, `updated_at`) VALUES
(1, 'การจัดซื้อคอมพิวเตอร์ใหม่', 1, 1, 250000.00, 80, 'Website', 'proposal', '2023-12-31', NULL, 'open', 'ลูกค้าต้องการซื้อคอมพิวเตอร์ใหม่ทั้งหมด 10 เครื่อง', 3, NOW(), NOW()),
(2, 'บริการดูแลระบบประจำปี', 2, 3, 60000.00, 90, 'Referral', 'negotiation', '2023-11-30', NULL, 'open', 'ลูกค้าต้องการต่อสัญญาบริการดูแลระบบประจำปี', 4, NOW(), NOW()),
(3, 'การติดตั้งระบบเครือข่ายใหม่', 3, 4, 150000.00, 50, 'Cold Call', 'qualification', '2024-01-31', NULL, 'open', 'ลูกค้าต้องการปรับปรุงระบบเครือข่ายภายในองค์กร', 3, NOW(), NOW());

-- ข้อมูลใบเสนอราคา
INSERT INTO `sts_quotations` (`id`, `quotation_number`, `customer_id`, `contact_id`, `opportunity_id`, `subject`, `issue_date`, `valid_until`, `subtotal`, `discount_type`, `discount_value`, `tax`, `total`, `notes`, `terms`, `status`, `user_id`, `created_at`, `updated_at`) VALUES
(1, 'QT202301001', 1, 1, 1, 'ใบเสนอราคาคอมพิวเตอร์', '2023-10-01', '2023-10-31', 250000.00, 'percentage', 5.00, 16625.00, 254375.00, 'ราคานี้รวมค่าติดตั้งและค่าขนส่งแล้ว', 'ชำระเงินภายใน 30 วันหลังจากได้รับสินค้า', 'sent', 3, NOW(), NOW()),
(2, 'QT202302001', 2, 3, 2, 'ใบเสนอราคาบริการดูแลระบบประจำปี', '2023-10-15', '2023-11-15', 60000.00, NULL, 0.00, 4200.00, 64200.00, 'บริการดูแลระบบประจำปี ระยะเวลา 12 เดือน', 'ชำระเงินล่วงหน้า 50% ก่อนเริ่มให้บริการ', 'draft', 4, NOW(), NOW());

-- ข้อมูลรายการในใบเสนอราคา
INSERT INTO `sts_quotation_items` (`id`, `quotation_id`, `product_id`, `description`, `quantity`, `unit_price`, `tax_rate`, `total`) VALUES
(1, 1, 1, 'คอมพิวเตอร์โน้ตบุ๊ก รุ่น A', 5.00, 25000.00, 7.00, 125000.00),
(2, 1, 2, 'คอมพิวเตอร์ตั้งโต๊ะ รุ่น B', 5.00, 20000.00, 7.00, 100000.00),
(3, 1, 3, 'เครื่องพิมพ์ รุ่น C', 2.00, 15000.00, 7.00, 30000.00),
(4, 2, 4, 'บริการดูแลระบบ', 12.00, 5000.00, 7.00, 60000.00);

-- ข้อมูลการตั้งค่าระบบ
INSERT INTO `sts_settings` (`id`, `setting_key`, `setting_value`, `setting_group`, `created_at`, `updated_at`) VALUES
(1, 'company_name', 'บริษัท ระบบติดตามงานขาย จำกัด', 'company', NOW(), NOW()),
(2, 'company_address', '123 ถนนรัชดาภิเษก แขวงดินแดง เขตดินแดง กรุงเทพฯ 10400', 'company', NOW(), NOW()),
(3, 'company_phone', '02-123-4567', 'company', NOW(), NOW()),
(4, 'company_email', '<EMAIL>', 'company', NOW(), NOW()),
(5, 'company_website', 'www.salestracking.co.th', 'company', NOW(), NOW()),
(6, 'company_tax_id', '0123456789012', 'company', NOW(), NOW()),
(7, 'quotation_prefix', 'QT', 'document', NOW(), NOW()),
(8, 'invoice_prefix', 'INV', 'document', NOW(), NOW()),
(9, 'receipt_prefix', 'REC', 'document', NOW(), NOW()),
(10, 'payment_prefix', 'PAY', 'document', NOW(), NOW()),
(11, 'default_tax_rate', '7', 'finance', NOW(), NOW()),
(12, 'currency_symbol', '฿', 'finance', NOW(), NOW()),
(13, 'currency_code', 'THB', 'finance', NOW(), NOW()),
(14, 'decimal_places', '2', 'finance', NOW(), NOW()),
(15, 'date_format', 'd/m/Y', 'system', NOW(), NOW()),
(16, 'time_format', 'H:i', 'system', NOW(), NOW()),
(17, 'items_per_page', '10', 'system', NOW(), NOW()),
(18, 'quotation_terms', 'ใบเสนอราคานี้มีอายุ 30 วันนับจากวันที่ออกใบเสนอราคา\nราคาที่เสนอไม่รวมภาษีมูลค่าเพิ่ม 7%\nการชำระเงิน: ชำระเงินภายใน 30 วันหลังจากได้รับสินค้า\nการส่งมอบ: ภายใน 15 วันหลังจากได้รับการยืนยันคำสั่งซื้อ', 'document', NOW(), NOW()),
(19, 'invoice_terms', 'กำหนดชำระเงิน: ภายใน 30 วันนับจากวันที่ออกใบแจ้งหนี้\nวิธีการชำระเงิน: โอนเงินเข้าบัญชีธนาคาร หรือเช็คสั่งจ่ายในนามบริษัท\nกรณีชำระเงินล่าช้า: คิดดอกเบี้ย 1.5% ต่อเดือนของยอดค้างชำระ', 'document', NOW(), NOW()),
(20, 'system_name', 'ระบบติดตามงานขายและการเสนอราคา', 'system', NOW(), NOW()),
(21, 'system_version', '1.0.0', 'system', NOW(), NOW());

