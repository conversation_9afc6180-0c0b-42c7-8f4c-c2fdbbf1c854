<?php
/**
 * Setting Controller
 */

class SettingController extends BaseController {
    private $settingModel;
    
    public function __construct() {
        parent::__construct();
        if (!$this->user) {
            $this->redirect('index.php?controller=auth&action=login');
            return;
        }
        $this->settingModel = new Setting();
    }
    
    public function index() {
        if ($this->isMethod('POST')) {
            $this->processUpdate();
            return;
        }
        
        $settings = $this->settingModel->getAllSettings();
        
        $this->data['settings'] = $settings;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'การตั้งค่าระบบ';
        $this->view('settings/index');
    }
    
    private function processUpdate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=setting');
            return;
        }
        
        $data = $this->post();
        $success = $this->settingModel->updateMultiple($data);
        
        if ($success) {
            showAlert('อัปเดตการตั้งค่าเรียบร้อยแล้ว', 'success');
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
        }
        
        $this->redirect('index.php?controller=setting');
    }
    
    public function company() {
        if ($this->isMethod('POST')) {
            $this->processCompanyUpdate();
            return;
        }
        
        $companySettings = $this->settingModel->getByGroup('company');
        
        $this->data['company_settings'] = $companySettings;
        $this->data['csrf_token'] = generateCSRFToken();
        $this->data['page_title'] = 'ข้อมูลบริษัท';
        $this->view('settings/company');
    }
    
    private function processCompanyUpdate() {
        if (!$this->validateCSRF()) {
            showAlert('Invalid request', 'error');
            $this->redirect('index.php?controller=setting&action=company');
            return;
        }
        
        $data = $this->post();
        $success = $this->settingModel->updateMultiple($data);
        
        if ($success) {
            showAlert('อัปเดตข้อมูลบริษัทเรียบร้อยแล้ว', 'success');
        } else {
            showAlert('เกิดข้อผิดพลาด', 'error');
        }
        
        $this->redirect('index.php?controller=setting&action=company');
    }
}
