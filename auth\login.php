<?php
/**
 * Login Page
 * 
 * หน้าล็อกอินสำหรับระบบติดตามงานขายและการเสนอราคา
 */

// เริ่มต้น Session
session_start();

// ตรวจสอบว่าผู้ใช้ล็อกอินแล้วหรือไม่
if (isset($_SESSION['user_id'])) {
    // ถ้าล็อกอินแล้วให้ redirect ไปที่หน้าแดชบอร์ด
    header('Location: ../dashboard.php');
    exit;
}

// กำหนดตัวแปรสำหรับข้อความแจ้งเตือน
$error_message = '';
$success_message = '';

// ตรวจสอบว่ามีการส่งฟอร์มล็อกอินหรือไม่
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // ตรวจสอบว่ามีการส่งข้อมูลครบถ้วนหรือไม่
    if (isset($_POST['username']) && isset($_POST['password'])) {
        // รับค่าจากฟอร์ม
        $username = trim($_POST['username']);
        $password = $_POST['password'];
        
        // ตรวจสอบว่าข้อมูลถูกต้องหรือไม่
        if (empty($username) || empty($password)) {
            $error_message = 'กรุณากรอกชื่อผู้ใช้และรหัสผ่าน';
        } else {
            // เชื่อมต่อฐานข้อมูล
            require_once '../app/config/database.php';
            
            try {
                // สร้างคำสั่ง SQL สำหรับตรวจสอบผู้ใช้
                $sql = "SELECT * FROM users WHERE username = :username OR email = :email LIMIT 1";
                $stmt = $conn->prepare($sql);
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':email', $username);
                $stmt->execute();
                
                // ตรวจสอบว่ามีผู้ใช้หรือไม่
                if ($stmt->rowCount() > 0) {
                    // ดึงข้อมูลผู้ใช้
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    // ตรวจสอบรหัสผ่าน
                    if (password_verify($password, $user['password'])) {
                        // รหัสผ่านถูกต้อง
                        
                        // ตรวจสอบสถานะผู้ใช้
                        if ($user['status'] == 1) {
                            // บันทึกข้อมูลผู้ใช้ลงใน Session
                            $_SESSION['user_id'] = $user['id'];
                            $_SESSION['username'] = $user['username'];
                            $_SESSION['name'] = $user['name'];
                            $_SESSION['email'] = $user['email'];
                            $_SESSION['role'] = $user['role'];
                            $_SESSION['last_login'] = date('Y-m-d H:i:s');
                            
                            // บันทึกเวลาล็อกอินล่าสุด
                            $update_sql = "UPDATE users SET last_login = :last_login WHERE id = :id";
                            $update_stmt = $conn->prepare($update_sql);
                            $update_stmt->bindParam(':last_login', $_SESSION['last_login']);
                            $update_stmt->bindParam(':id', $user['id']);
                            $update_stmt->execute();
                            
                            // บันทึกประวัติการล็อกอิน
                            $log_sql = "INSERT INTO login_logs (user_id, login_time, ip_address, user_agent) VALUES (:user_id, :login_time, :ip_address, :user_agent)";
                            $log_stmt = $conn->prepare($log_sql);
                            $log_stmt->bindParam(':user_id', $user['id']);
                            $log_stmt->bindParam(':login_time', $_SESSION['last_login']);
                            $log_stmt->bindParam(':ip_address', $_SERVER['REMOTE_ADDR']);
                            $log_stmt->bindParam(':user_agent', $_SERVER['HTTP_USER_AGENT']);
                            $log_stmt->execute();
                            
                            // Redirect ไปที่หน้าแดชบอร์ด
                            header('Location: ../dashboard.php');
                            exit;
                        } else {
                            // ผู้ใช้ถูกระงับ
                            $error_message = 'บัญชีผู้ใช้ถูกระงับ กรุณาติดต่อผู้ดูแลระบบ';
                        }
                    } else {
                        // รหัสผ่านไม่ถูกต้อง
                        $error_message = 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง';
                    }
                } else {
                    // ไม่พบผู้ใช้
                    $error_message = 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง';
                }
            } catch (PDOException $e) {
                // เกิดข้อผิดพลาดในการเชื่อมต่อฐานข้อมูล
                $error_message = 'เกิดข้อผิดพลาดในการเชื่อมต่อฐานข้อมูล: ' . $e->getMessage();
            }
        }
    } else {
        $error_message = 'กรุณากรอกชื่อผู้ใช้และรหัสผ่าน';
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - ระบบติดตามงานขายและการเสนอราคา</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Sarabun', sans-serif;
        }
    </style>
</head>
<body>
    <div class="login-page">
        <div class="login-form">
            <div class="logo text-center mb-4">
                <img src="../assets/images/logo.png" alt="Logo" class="img-fluid" style="max-height: 80px;">
                <h2 class="mt-3">ระบบติดตามงานขาย<br>และการเสนอราคา</h2>
            </div>
            
            <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            
            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" data-validate="true">
                <div class="mb-4">
                    <label for="username" class="form-label">ชื่อผู้ใช้หรืออีเมล</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                        <input type="text" class="form-control" id="username" name="username" placeholder="กรอกชื่อผู้ใช้หรืออีเมล" required>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label for="password" class="form-label">รหัสผ่าน</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="password" name="password" placeholder="กรอกรหัสผ่าน" required>
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mb-4 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">จดจำฉันไว้</label>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i> เข้าสู่ระบบ
                    </button>
                </div>
                
                <div class="text-center mt-4">
                    <a href="forgot_password.php" class="text-decoration-none">ลืมรหัสผ่าน?</a>
                </div>
            </form>
            
            <div class="text-center mt-4">
                <p class="mb-0">&copy; <?php echo date('Y'); ?> ระบบติดตามงานขายและการเสนอราคา</p>
                <p class="text-muted small">Version 1.0.0</p>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
</body>
</html>

